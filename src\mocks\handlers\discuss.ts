// MSW handlers for Discuss module API endpoints
import { http, HttpResponse } from 'msw';
import {
  mockUsers,
  mockChannels,
  mockMessages,
  mockDirectMessages,
  mockTeams,
  mockNotificationSettings,
  mockPresenceInfo,
  mockCalls,
  mockCallParticipants,
  mockBots,
  mockAutomationRules,
  mockScheduledMessages,
  mockMessageArchives,
  mockRetentionPolicies,
  mockLegalHolds,
  mockExportRequests,
  mockExternalIntegrations,
  mockERPIntegrations,
  mockWebhooks,
  mockAPIKeys,
  getMockUserById,
  getMockChannelById,
  getMockMessagesByChannelId,
  getMockDirectMessagesByUserId,
  getMockTeamById,
  getMockCallById,
  getMockCallsByChannelId,
  getMockCallsByUserId,
  getMockCallParticipants,
  getMockBotById,
  getMockBotsByType,
  getMockEnabledBots,
  getMockAutomationRuleById,
  getMockEnabledAutomationRules,
  getMockScheduledMessageById,
  getMockPendingScheduledMessages,
  getMockScheduledMessagesByChannel,
  getMockArchivedMessageById,
  getMockArchivedMessagesByChannel,
  getMockRetentionPolicyById,
  getMockEnabledRetentionPolicies,
  getMockLegalHoldById,
  getMockActiveLegalHolds,
  getMockExportRequestById,
  getMockExportRequestsByUser,
  getMockIntegrationById,
  getMockIntegrationsByType,
  getMockEnabledIntegrations,
  getMockERPIntegrationById,
  getMockERPIntegrationsByModule,
  getMockWebhookById,
  getMockEnabledWebhooks,
  getMockAPIKeyById,
  getMockEnabledAPIKeys,
} from '../data/discuss';

// Base API URL for discuss endpoints
const API_BASE = '/api/discuss';

export const discussHandlers = [
  // Get all users
  http.get(`${API_BASE}/users`, () => {
    return HttpResponse.json({
      success: true,
      data: mockUsers,
    });
  }),

  // Get user by ID
  http.get(`${API_BASE}/users/:id`, ({ params }) => {
    const { id } = params;
    const user = getMockUserById(id as string);
    
    if (!user) {
      return HttpResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: user,
    });
  }),

  // Get all channels
  http.get(`${API_BASE}/channels`, () => {
    return HttpResponse.json({
      success: true,
      data: mockChannels,
    });
  }),

  // Get channel by ID
  http.get(`${API_BASE}/channels/:id`, ({ params }) => {
    const { id } = params;
    const channel = getMockChannelById(id as string);
    
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: channel,
    });
  }),

  // Get messages for a channel
  http.get(`${API_BASE}/channels/:id/messages`, ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '50');
    
    const messages = getMockMessagesByChannelId(id as string);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedMessages = messages.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedMessages,
      total: messages.length,
      page,
      pageSize,
      hasMore: endIndex < messages.length,
    });
  }),

  // Send a message
  http.post(`${API_BASE}/messages`, async ({ request }) => {
    const body = await request.json() as any;
    
    const newMessage = {
      id: `msg-${Date.now()}`,
      content: body.content,
      authorId: body.authorId || '1', // Default to current user
      channelId: body.channelId,
      timestamp: new Date(),
      reactions: [],
      attachments: body.attachments || [],
      mentions: body.mentions || [],
      isDeleted: false,
      deliveryStatus: 'sent' as const,
    };

    // Add to mock data (in real app, this would be persisted)
    mockMessages.push(newMessage);

    return HttpResponse.json({
      success: true,
      data: newMessage,
    });
  }),

  // Get direct messages for current user
  http.get(`${API_BASE}/direct-messages`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId') || '1'; // Default to current user
    
    const directMessages = getMockDirectMessagesByUserId(userId);

    return HttpResponse.json({
      success: true,
      data: directMessages,
    });
  }),

  // Get all teams
  http.get(`${API_BASE}/teams`, () => {
    return HttpResponse.json({
      success: true,
      data: mockTeams,
    });
  }),

  // Get team by ID
  http.get(`${API_BASE}/teams/:id`, ({ params }) => {
    const { id } = params;
    const team = getMockTeamById(id as string);
    
    if (!team) {
      return HttpResponse.json(
        { success: false, error: 'Team not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: team,
    });
  }),

  // Get notification settings
  http.get(`${API_BASE}/settings/notifications`, () => {
    return HttpResponse.json({
      success: true,
      data: mockNotificationSettings,
    });
  }),

  // Update notification settings
  http.put(`${API_BASE}/settings/notifications`, async ({ request }) => {
    const body = await request.json() as any;
    
    // In real app, this would update the user's settings
    Object.assign(mockNotificationSettings, body);

    return HttpResponse.json({
      success: true,
      data: mockNotificationSettings,
    });
  }),

  // Get presence info for all users
  http.get(`${API_BASE}/presence`, () => {
    return HttpResponse.json({
      success: true,
      data: mockPresenceInfo,
    });
  }),

  // Get presence info for a specific user
  http.get(`${API_BASE}/presence/:userId`, ({ params }) => {
    const { userId } = params;
    const presence = mockPresenceInfo.find(p => p.userId === userId);

    if (!presence) {
      return HttpResponse.json(
        { success: false, error: 'User presence not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: presence,
    });
  }),

  // Get presence info for multiple users
  http.post(`${API_BASE}/presence/bulk`, async ({ request }) => {
    const { userIds } = await request.json() as { userIds: string[] };

    const presenceMap: Record<string, any> = {};
    userIds.forEach(userId => {
      const presence = mockPresenceInfo.find(p => p.userId === userId);
      if (presence) {
        presenceMap[userId] = presence;
      }
    });

    return HttpResponse.json({
      success: true,
      data: presenceMap,
    });
  }),

  // Update user presence
  http.put(`${API_BASE}/presence`, async ({ request }) => {
    const body = await request.json() as any;

    const existingPresence = mockPresenceInfo.find(p => p.userId === body.userId);
    if (existingPresence) {
      Object.assign(existingPresence, {
        ...body,
        lastSeen: new Date(),
      });
    } else {
      mockPresenceInfo.push({
        ...body,
        lastSeen: new Date(),
      });
    }

    return HttpResponse.json({
      success: true,
      data: existingPresence || body,
    });
  }),

  // Get online users
  http.get(`${API_BASE}/presence/online`, ({ request }) => {
    const url = new URL(request.url);
    const channelId = url.searchParams.get('channelId');

    let onlineUsers = mockUsers.filter(user => user.status === 'online');

    // If channelId is specified, filter by channel members
    if (channelId) {
      const channel = getMockChannelById(channelId);
      if (channel) {
        onlineUsers = onlineUsers.filter(user => channel.memberIds.includes(user.id));
      }
    }

    return HttpResponse.json({
      success: true,
      data: onlineUsers,
    });
  }),

  // Send typing indicator
  http.post(`${API_BASE}/presence/typing`, async ({ request }) => {
    const body = await request.json() as any;

    // Update typing status
    const presence = mockPresenceInfo.find(p => p.userId === body.userId);
    if (presence) {
      presence.isTyping = body.isTyping;
      presence.currentChannel = body.channelId;
      presence.lastSeen = new Date();
    }

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Get typing users in a channel
  http.get(`${API_BASE}/presence/typing/:channelId`, ({ params }) => {
    const { channelId } = params;

    const typingUsers = mockUsers.filter(user => {
      const presence = mockPresenceInfo.find(p => p.userId === user.id);
      return presence?.isTyping && presence?.currentChannel === channelId;
    });

    return HttpResponse.json({
      success: true,
      data: typingUsers,
    });
  }),

  // Set user status
  http.put(`${API_BASE}/presence/status`, async ({ request }) => {
    const { userId, status, customMessage } = await request.json() as any;

    // Update user status
    const user = mockUsers.find(u => u.id === userId);
    if (user) {
      user.status = status;
      user.lastSeen = new Date();
    }

    // Update presence info
    const presence = mockPresenceInfo.find(p => p.userId === userId);
    if (presence) {
      presence.status = status;
      presence.customMessage = customMessage;
      presence.lastSeen = new Date();
    }

    return HttpResponse.json({
      success: true,
      data: { userId, status, customMessage },
    });
  }),

  // Search messages with advanced filtering
  http.get(`${API_BASE}/search/messages`, ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');
    const channels = url.searchParams.get('channels')?.split(',') || [];
    const authors = url.searchParams.get('authors')?.split(',') || [];
    const dateFrom = url.searchParams.get('dateFrom') ? new Date(url.searchParams.get('dateFrom')!) : null;
    const dateTo = url.searchParams.get('dateTo') ? new Date(url.searchParams.get('dateTo')!) : null;
    const hasAttachments = url.searchParams.get('hasAttachments') === 'true';
    const messageType = url.searchParams.get('messageType') || 'all';

    let filteredMessages = mockMessages;

    // Filter by channels
    if (channels.length > 0) {
      filteredMessages = filteredMessages.filter(m => channels.includes(m.channelId || ''));
    }

    // Filter by authors
    if (authors.length > 0) {
      filteredMessages = filteredMessages.filter(m => authors.includes(m.authorId));
    }

    // Filter by date range
    if (dateFrom) {
      filteredMessages = filteredMessages.filter(m => new Date(m.timestamp) >= dateFrom);
    }
    if (dateTo) {
      filteredMessages = filteredMessages.filter(m => new Date(m.timestamp) <= dateTo);
    }

    // Filter by attachments
    if (hasAttachments) {
      filteredMessages = filteredMessages.filter(m => m.attachments.length > 0);
    }

    // Filter by message type
    if (messageType === 'files') {
      filteredMessages = filteredMessages.filter(m => m.attachments.length > 0);
    } else if (messageType === 'links') {
      filteredMessages = filteredMessages.filter(m =>
        m.content.includes('http://') || m.content.includes('https://')
      );
    }

    // Filter by query
    if (query) {
      filteredMessages = filteredMessages.filter(m =>
        m.content.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Create search results with snippets and highlights
    const results = filteredMessages.map(message => {
      const author = getMockUserById(message.authorId);
      const channel = getMockChannelById(message.channelId || '');

      // Create snippet and highlights
      const content = message.content;
      const queryLower = query.toLowerCase();
      const contentLower = content.toLowerCase();
      const matchIndex = contentLower.indexOf(queryLower);

      let snippet = content;
      let highlights: number[] = [];

      if (matchIndex !== -1 && query) {
        // Create snippet around the match
        const contextLength = 75;
        const start = Math.max(0, matchIndex - contextLength);
        const end = Math.min(content.length, matchIndex + query.length + contextLength);
        snippet = content.substring(start, end);

        if (start > 0) snippet = '...' + snippet;
        if (end < content.length) snippet = snippet + '...';

        // Calculate highlight positions in snippet
        const snippetLower = snippet.toLowerCase();
        let searchIndex = 0;
        while ((searchIndex = snippetLower.indexOf(queryLower, searchIndex)) !== -1) {
          highlights.push(searchIndex);
          searchIndex += queryLower.length;
        }
      }

      return {
        message,
        author,
        channel,
        snippet,
        highlights,
        score: query ? (matchIndex === -1 ? 0 : 1 / (matchIndex + 1)) : 1,
      };
    });

    // Sort by relevance (score)
    results.sort((a, b) => b.score - a.score);

    // Paginate results
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedResults = results.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: {
        results: paginatedResults,
        total: results.length,
        page,
        pageSize,
        hasMore: endIndex < results.length,
        query,
        filters: {
          channels,
          authors,
          dateFrom,
          dateTo,
          hasAttachments,
          messageType,
        },
        searchTime: Math.random() * 100 + 50, // Mock search time
      },
    });
  }),

  // Search users
  http.get(`${API_BASE}/search/users`, ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    let filteredUsers = mockUsers;

    if (query) {
      filteredUsers = filteredUsers.filter(user =>
        user.name.toLowerCase().includes(query.toLowerCase()) ||
        user.email.toLowerCase().includes(query.toLowerCase())
      );
    }

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: {
        users: paginatedUsers,
        total: filteredUsers.length,
        page,
        pageSize,
        hasMore: endIndex < filteredUsers.length,
      },
    });
  }),

  // Search channels
  http.get(`${API_BASE}/search/channels`, ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    let filteredChannels = mockChannels;

    if (query) {
      filteredChannels = filteredChannels.filter(channel =>
        channel.name.toLowerCase().includes(query.toLowerCase()) ||
        (channel.description && channel.description.toLowerCase().includes(query.toLowerCase()))
      );
    }

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedChannels = filteredChannels.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: {
        channels: paginatedChannels,
        total: filteredChannels.length,
        page,
        pageSize,
        hasMore: endIndex < filteredChannels.length,
      },
    });
  }),

  // Search files/attachments
  http.get(`${API_BASE}/search/files`, ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');
    const channels = url.searchParams.get('channels')?.split(',') || [];
    const fileTypes = url.searchParams.get('fileTypes')?.split(',') || [];
    const dateFrom = url.searchParams.get('dateFrom') ? new Date(url.searchParams.get('dateFrom')!) : null;
    const dateTo = url.searchParams.get('dateTo') ? new Date(url.searchParams.get('dateTo')!) : null;

    // Get all messages with attachments
    let messagesWithFiles = mockMessages.filter(m => m.attachments.length > 0);

    // Filter by channels
    if (channels.length > 0) {
      messagesWithFiles = messagesWithFiles.filter(m => channels.includes(m.channelId || ''));
    }

    // Filter by date range
    if (dateFrom) {
      messagesWithFiles = messagesWithFiles.filter(m => new Date(m.timestamp) >= dateFrom);
    }
    if (dateTo) {
      messagesWithFiles = messagesWithFiles.filter(m => new Date(m.timestamp) <= dateTo);
    }

    // Create file results
    const fileResults: any[] = [];
    messagesWithFiles.forEach(message => {
      message.attachments.forEach(attachment => {
        // Filter by file types
        if (fileTypes.length > 0 && !fileTypes.includes(attachment.type)) {
          return;
        }

        // Filter by query
        if (query && !attachment.name.toLowerCase().includes(query.toLowerCase())) {
          return;
        }

        fileResults.push({
          attachment,
          message,
          author: getMockUserById(message.authorId),
          channel: getMockChannelById(message.channelId || ''),
        });
      });
    });

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedFiles = fileResults.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: {
        files: paginatedFiles,
        total: fileResults.length,
        page,
        pageSize,
        hasMore: endIndex < fileResults.length,
      },
    });
  }),

  // Get search suggestions
  http.get(`${API_BASE}/search/suggestions`, ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const type = url.searchParams.get('type') || 'all';

    const suggestions: any[] = [];

    if (type === 'all' || type === 'users') {
      // User suggestions
      mockUsers.forEach(user => {
        if (user.name.toLowerCase().includes(query.toLowerCase())) {
          suggestions.push({
            text: `@${user.name}`,
            type: 'user',
            data: user,
          });
        }
      });
    }

    if (type === 'all' || type === 'channels') {
      // Channel suggestions
      mockChannels.forEach(channel => {
        if (channel.name.toLowerCase().includes(query.toLowerCase())) {
          suggestions.push({
            text: `#${channel.name}`,
            type: 'channel',
            data: channel,
          });
        }
      });
    }

    // Query suggestions (mock common searches)
    if (type === 'all' || type === 'messages') {
      const commonQueries = [
        'meeting notes',
        'project update',
        'deadline',
        'budget',
        'presentation',
        'feedback',
        'review',
        'schedule',
      ];

      commonQueries.forEach(commonQuery => {
        if (commonQuery.toLowerCase().includes(query.toLowerCase())) {
          suggestions.push({
            text: commonQuery,
            type: 'query',
          });
        }
      });
    }

    return HttpResponse.json({
      success: true,
      data: {
        suggestions: suggestions.slice(0, 10), // Limit to 10 suggestions
      },
    });
  }),

  // Search history endpoints
  http.post(`${API_BASE}/search/history`, async ({ request }) => {
    // Mock saving search history
    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  http.get(`${API_BASE}/search/history`, ({ request }) => {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');

    // Mock search history
    const history = [
      {
        query: 'project update',
        filters: {},
        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        resultCount: 15,
      },
      {
        query: 'meeting notes',
        filters: { channels: ['general'] },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        resultCount: 8,
      },
      {
        query: 'deadline',
        filters: { hasAttachments: true },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
        resultCount: 3,
      },
    ].slice(0, limit);

    return HttpResponse.json({
      success: true,
      data: history,
    });
  }),

  http.delete(`${API_BASE}/search/history`, () => {
    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Message pinning endpoints
  http.post(`${API_BASE}/messages/:messageId/pin`, async ({ params, request }) => {
    const { messageId } = params;
    const { reason, userId } = await request.json() as any;

    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    // Add pinned property to message
    (message as any).isPinned = true;
    (message as any).pinnedBy = userId || '1';
    (message as any).pinnedAt = new Date();
    (message as any).pinReason = reason;

    return HttpResponse.json({
      success: true,
      data: {
        messageId,
        isPinned: true,
        pinnedBy: userId || '1',
        pinnedAt: new Date(),
        reason,
      },
    });
  }),

  http.delete(`${API_BASE}/messages/:messageId/pin`, ({ params }) => {
    const { messageId } = params;

    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    // Remove pinned properties
    delete (message as any).isPinned;
    delete (message as any).pinnedBy;
    delete (message as any).pinnedAt;
    delete (message as any).pinReason;

    return HttpResponse.json({
      success: true,
      data: { messageId, isPinned: false },
    });
  }),

  // Get pinned messages for a channel
  http.get(`${API_BASE}/channels/:channelId/pinned`, ({ params }) => {
    const { channelId } = params;

    const pinnedMessages = mockMessages
      .filter(m => m.channelId === channelId && (m as any).isPinned)
      .map(message => ({
        id: `pin-${message.id}`,
        message,
        author: getMockUserById(message.authorId),
        pinnedBy: getMockUserById((message as any).pinnedBy),
        pinnedAt: (message as any).pinnedAt,
        reason: (message as any).pinReason,
      }));

    return HttpResponse.json({
      success: true,
      data: pinnedMessages,
    });
  }),

  // Poll endpoints
  http.post(`${API_BASE}/polls`, async ({ request }) => {
    const pollData = await request.json() as any;

    const poll = {
      id: `poll-${Date.now()}`,
      ...pollData,
      createdAt: new Date(),
      totalVotes: 0,
      isActive: true,
    };

    return HttpResponse.json({
      success: true,
      data: poll,
    });
  }),

  http.post(`${API_BASE}/polls/:pollId/vote`, async ({ params, request }) => {
    const { pollId } = params;
    const { optionIds, userId } = await request.json() as any;

    // Mock voting logic
    return HttpResponse.json({
      success: true,
      data: {
        pollId,
        optionIds,
        userId,
        votedAt: new Date(),
      },
    });
  }),

  http.put(`${API_BASE}/polls/:pollId/close`, ({ params }) => {
    const { pollId } = params;

    return HttpResponse.json({
      success: true,
      data: { pollId, isActive: false, closedAt: new Date() },
    });
  }),

  // Task integration endpoints
  http.post(`${API_BASE}/tasks`, async ({ request }) => {
    const taskData = await request.json() as any;

    const task = {
      id: `task-${Date.now()}`,
      ...taskData,
      createdAt: new Date(),
    };

    return HttpResponse.json({
      success: true,
      data: task,
    });
  }),

  http.put(`${API_BASE}/tasks/:taskId/status`, async ({ params, request }) => {
    const { taskId } = params;
    const { status } = await request.json() as any;

    return HttpResponse.json({
      success: true,
      data: { taskId, status, updatedAt: new Date() },
    });
  }),

  http.get(`${API_BASE}/tasks`, ({ request }) => {
    const url = new URL(request.url);
    const channelId = url.searchParams.get('channelId');
    const messageId = url.searchParams.get('messageId');

    // Mock tasks
    const tasks = [
      {
        id: 'task-1',
        title: 'Review project proposal',
        description: 'Review the new project proposal and provide feedback',
        assigneeId: '2',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        priority: 'high',
        status: 'todo',
        createdBy: '1',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        messageId: messageId || 'msg-1',
        channelId: channelId || 'general',
      },
    ];

    return HttpResponse.json({
      success: true,
      data: tasks,
    });
  }),

  // Notification endpoints
  http.get(`${API_BASE}/notifications/settings`, () => {
    return HttpResponse.json({
      success: true,
      data: mockNotificationSettings,
    });
  }),

  http.put(`${API_BASE}/notifications/settings`, async ({ request }) => {
    const settings = await request.json() as any;

    // Update mock settings
    Object.assign(mockNotificationSettings, settings);

    return HttpResponse.json({
      success: true,
      data: mockNotificationSettings,
    });
  }),

  // Mark messages as read
  http.post(`${API_BASE}/messages/read`, async ({ request }) => {
    const { messageIds, userId } = await request.json() as any;

    // Update message read status
    messageIds.forEach((messageId: string) => {
      const message = mockMessages.find(m => m.id === messageId);
      if (message) {
        if (!(message as any).readBy) {
          (message as any).readBy = [];
        }

        // Add user to read list if not already there
        const existingRead = (message as any).readBy.find((r: any) => r.userId === userId);
        if (!existingRead) {
          (message as any).readBy.push({
            userId,
            readAt: new Date(),
          });
        }

        // Update delivery status
        message.deliveryStatus = 'read';
      }
    });

    return HttpResponse.json({
      success: true,
      data: { messageIds, userId, readAt: new Date() },
    });
  }),

  // Mark channel as read
  http.post(`${API_BASE}/channels/:channelId/read`, async ({ params, request }) => {
    const { channelId } = params;
    const { userId } = await request.json() as any;

    // Mark all messages in channel as read
    const channelMessages = mockMessages.filter(m => m.channelId === channelId);
    channelMessages.forEach(message => {
      if (!(message as any).readBy) {
        (message as any).readBy = [];
      }

      const existingRead = (message as any).readBy.find((r: any) => r.userId === userId);
      if (!existingRead) {
        (message as any).readBy.push({
          userId,
          readAt: new Date(),
        });
      }

      message.deliveryStatus = 'read';
    });

    return HttpResponse.json({
      success: true,
      data: { channelId, userId, readAt: new Date() },
    });
  }),

  // Get read status for a message
  http.get(`${API_BASE}/messages/:messageId/read-status`, ({ params }) => {
    const { messageId } = params;

    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    const readBy = (message as any).readBy || [];
    const readByUsers = readBy.map((r: any) => ({
      user: getMockUserById(r.userId),
      readAt: r.readAt,
    }));

    return HttpResponse.json({
      success: true,
      data: {
        isRead: readBy.length > 0,
        readBy: readByUsers,
      },
    });
  }),

  // Send notification
  http.post(`${API_BASE}/notifications/send`, async ({ request }) => {
    const notification = await request.json() as any;

    // Mock sending notification
    console.log('Mock notification sent:', notification);

    return HttpResponse.json({
      success: true,
      data: { notificationId: `notif-${Date.now()}`, sentAt: new Date() },
    });
  }),

  // Get notification history
  http.get(`${API_BASE}/notifications/history`, ({ request }) => {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');

    // Mock notification history
    const notifications = Array.from({ length: Math.min(limit, 10) }, (_, i) => ({
      id: `notif-${i}`,
      type: ['message', 'mention', 'reaction'][i % 3],
      title: `Notification ${i + 1}`,
      body: `This is notification ${i + 1}`,
      timestamp: new Date(Date.now() - i * 60 * 60 * 1000),
      isRead: i > 3,
      data: {
        messageId: `msg-${i}`,
        channelId: 'general',
      },
    }));

    return HttpResponse.json({
      success: true,
      data: notifications,
    });
  }),

  // File upload endpoint
  http.post(`${API_BASE}/files/upload`, async ({ request }) => {
    // Simulate file upload
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockFile = {
      id: `file-${Date.now()}`,
      name: 'uploaded-file.jpg',
      type: 'image',
      url: '/mock-files/uploaded-file.jpg',
      size: 1024 * 1024, // 1MB
      mimeType: 'image/jpeg',
    };

    return HttpResponse.json({
      success: true,
      data: mockFile,
    });
  }),

  // WebSocket connection simulation (for real-time features)
  http.get(`${API_BASE}/ws`, () => {
    // In a real app, this would establish a WebSocket connection
    return HttpResponse.json({
      success: true,
      message: 'WebSocket connection established',
    });
  }),

  // Typing indicator
  http.post(`${API_BASE}/typing`, async ({ request }) => {
    const body = await request.json() as any;
    
    // Update typing status
    const presence = mockPresenceInfo.find(p => p.userId === body.userId);
    if (presence) {
      presence.isTyping = body.isTyping;
      presence.currentChannel = body.channelId;
    }

    return HttpResponse.json({
      success: true,
      data: { userId: body.userId, isTyping: body.isTyping },
    });
  }),

  // Message reactions
  http.post(`${API_BASE}/messages/:messageId/reactions`, async ({ params, request }) => {
    const { messageId } = params;
    const body = await request.json() as any;
    
    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    const existingReaction = message.reactions.find(r => r.emoji === body.emoji);
    if (existingReaction) {
      if (!existingReaction.userIds.includes(body.userId)) {
        existingReaction.userIds.push(body.userId);
        existingReaction.count++;
      }
    } else {
      message.reactions.push({
        emoji: body.emoji,
        userIds: [body.userId],
        count: 1,
      });
    }

    return HttpResponse.json({
      success: true,
      data: message.reactions,
    });
  }),

  // Remove message reaction
  http.delete(`${API_BASE}/messages/:messageId/reactions/:emoji`, ({ params, request }) => {
    const { messageId, emoji } = params;
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId') || '1';

    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    const reaction = message.reactions.find(r => r.emoji === emoji);
    if (reaction) {
      reaction.userIds = reaction.userIds.filter(id => id !== userId);
      reaction.count = reaction.userIds.length;

      if (reaction.count === 0) {
        message.reactions = message.reactions.filter(r => r.emoji !== emoji);
      }
    }

    return HttpResponse.json({
      success: true,
      data: message.reactions,
    });
  }),

  // Update message
  http.put(`${API_BASE}/messages/:messageId`, async ({ params, request }) => {
    const { messageId } = params;
    const body = await request.json() as any;

    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    // Update message content
    message.content = body.content;
    message.editedAt = new Date();

    return HttpResponse.json({
      success: true,
      data: message,
    });
  }),

  // Delete message
  http.delete(`${API_BASE}/messages/:messageId`, ({ params }) => {
    const { messageId } = params;

    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    message.isDeleted = true;

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Pin message
  http.post(`${API_BASE}/messages/:messageId/pin`, ({ params }) => {
    const { messageId } = params;

    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    // In real app, this would add to pinned messages list
    console.log(`Message ${messageId} pinned`);

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Unpin message
  http.delete(`${API_BASE}/messages/:messageId/pin`, ({ params }) => {
    const { messageId } = params;

    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    // In real app, this would remove from pinned messages list
    console.log(`Message ${messageId} unpinned`);

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Mark messages as read
  http.post(`${API_BASE}/messages/read`, async ({ request }) => {
    const body = await request.json() as any;
    const { messageIds } = body;

    messageIds.forEach((messageId: string) => {
      const message = mockMessages.find(m => m.id === messageId);
      if (message) {
        message.deliveryStatus = 'read';
      }
    });

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Get message thread
  http.get(`${API_BASE}/messages/:messageId/thread`, ({ params }) => {
    const { messageId } = params;

    // Find all messages in the same thread
    const threadMessages = mockMessages.filter(m =>
      m.parentMessageId === messageId || m.id === messageId
    );

    return HttpResponse.json({
      success: true,
      data: threadMessages,
    });
  }),

  // Channel management endpoints
  http.post(`${API_BASE}/channels`, async ({ request }) => {
    const body = await request.json() as any;

    const newChannel = {
      id: `channel-${Date.now()}`,
      name: body.name,
      description: body.description || '',
      type: body.type || 'public',
      memberIds: body.memberIds || ['1'], // Creator is always a member
      createdBy: '1', // Current user
      createdAt: new Date(),
      lastActivity: new Date(),
      isArchived: false,
      settings: {
        notifications: true,
        allowFileUploads: true,
        allowExternalLinks: true,
      },
    };

    mockChannels.push(newChannel);

    return HttpResponse.json({
      success: true,
      data: newChannel,
    });
  }),

  http.put(`${API_BASE}/channels/:channelId`, async ({ params, request }) => {
    const { channelId } = params;
    const body = await request.json() as any;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    // Update channel properties
    if (body.name) channel.name = body.name;
    if (body.description !== undefined) channel.description = body.description;
    if (body.settings) Object.assign(channel.settings, body.settings);

    return HttpResponse.json({
      success: true,
      data: channel,
    });
  }),

  http.delete(`${API_BASE}/channels/:channelId`, ({ params }) => {
    const { channelId } = params;

    const channelIndex = mockChannels.findIndex(c => c.id === channelId);
    if (channelIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    mockChannels.splice(channelIndex, 1);

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Channel members management
  http.get(`${API_BASE}/channels/:channelId/members`, ({ params }) => {
    const { channelId } = params;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    const members = channel.memberIds.map(id => getMockUserById(id)).filter(Boolean);

    return HttpResponse.json({
      success: true,
      data: members,
    });
  }),

  http.post(`${API_BASE}/channels/:channelId/members`, async ({ params, request }) => {
    const { channelId } = params;
    const body = await request.json() as any;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    if (!channel.memberIds.includes(body.userId)) {
      channel.memberIds.push(body.userId);
    }

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  http.delete(`${API_BASE}/channels/:channelId/members/:userId`, ({ params }) => {
    const { channelId, userId } = params;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    channel.memberIds = channel.memberIds.filter(id => id !== userId);

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Channel actions
  http.post(`${API_BASE}/channels/:channelId/join`, ({ params }) => {
    const { channelId } = params;
    const userId = '1'; // Current user

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    if (!channel.memberIds.includes(userId)) {
      channel.memberIds.push(userId);
    }

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  http.post(`${API_BASE}/channels/:channelId/leave`, ({ params }) => {
    const { channelId } = params;
    const userId = '1'; // Current user

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    channel.memberIds = channel.memberIds.filter(id => id !== userId);

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  http.post(`${API_BASE}/channels/:channelId/archive`, ({ params }) => {
    const { channelId } = params;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    channel.isArchived = true;

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  http.post(`${API_BASE}/channels/:channelId/unarchive`, ({ params }) => {
    const { channelId } = params;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    channel.isArchived = false;

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  http.post(`${API_BASE}/channels/:channelId/mute`, async ({ params, request }) => {
    const { channelId } = params;
    const body = await request.json() as any;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    channel.settings.notifications = false;
    if (body.muteUntil) {
      channel.settings.muteUntil = new Date(body.muteUntil);
    }

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  http.post(`${API_BASE}/channels/:channelId/unmute`, ({ params }) => {
    const { channelId } = params;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    channel.settings.notifications = true;
    delete channel.settings.muteUntil;

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Channel statistics
  http.get(`${API_BASE}/channels/:channelId/stats`, ({ params }) => {
    const { channelId } = params;

    const channel = mockChannels.find(c => c.id === channelId);
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    const channelMessages = mockMessages.filter(m => m.channelId === channelId);
    const stats = {
      memberCount: channel.memberIds.length,
      messageCount: channelMessages.length,
      lastActivity: channel.lastActivity,
      pinnedMessageCount: 0, // TODO: Implement pinned messages tracking
    };

    return HttpResponse.json({
      success: true,
      data: stats,
    });
  }),

  // Search channels
  http.get(`${API_BASE}/channels/search`, ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';

    const filteredChannels = mockChannels.filter(channel =>
      channel.name.toLowerCase().includes(query.toLowerCase()) ||
      (channel.description && channel.description.toLowerCase().includes(query.toLowerCase()))
    );

    return HttpResponse.json({
      success: true,
      data: filteredChannels,
    });
  }),

  // === CALL ENDPOINTS ===

  // Start a new call
  http.post(`${API_BASE}/calls/start`, async ({ request }) => {
    const body = await request.json() as {
      type: 'voice' | 'video';
      channelId?: string;
      participantIds: string[];
    };

    const newCall = {
      id: `call-${Date.now()}`,
      type: body.type,
      channelId: body.channelId,
      participantIds: body.participantIds,
      startedBy: '1', // Current user
      startedAt: new Date(),
      status: 'ringing' as const,
    };

    // Add to mock data
    mockCalls.push(newCall);

    return HttpResponse.json({
      success: true,
      data: newCall,
    });
  }),

  // Join a call
  http.post(`${API_BASE}/calls/:callId/join`, async ({ params, request }) => {
    const { callId } = params;
    const body = await request.json() as {
      userId: string;
      mediaConstraints: { audio: boolean; video: boolean };
    };

    const call = getMockCallById(callId as string);
    if (!call) {
      return HttpResponse.json(
        { success: false, error: 'Call not found' },
        { status: 404 }
      );
    }

    const participant = {
      userId: body.userId,
      joinedAt: new Date(),
      isMuted: false,
      isVideoEnabled: body.mediaConstraints.video,
      isScreenSharing: false,
    };

    // Add participant to mock data
    if (!mockCallParticipants[callId as string]) {
      mockCallParticipants[callId as string] = [];
    }
    mockCallParticipants[callId as string].push(participant);

    // Update call status to active if it was ringing
    if (call.status === 'ringing') {
      call.status = 'active';
    }

    return HttpResponse.json({
      success: true,
      data: participant,
    });
  }),

  // Leave a call
  http.post(`${API_BASE}/calls/:callId/leave`, async ({ params, request }) => {
    const { callId } = params;
    const body = await request.json() as { userId: string };

    const participants = getMockCallParticipants(callId as string);
    const participant = participants.find(p => p.userId === body.userId);

    if (participant) {
      participant.leftAt = new Date();
    }

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // End a call
  http.post(`${API_BASE}/calls/:callId/end`, ({ params }) => {
    const { callId } = params;
    const call = getMockCallById(callId as string);

    if (call) {
      call.status = 'ended';
      call.endedAt = new Date();

      // Mark all participants as left
      const participants = getMockCallParticipants(callId as string);
      participants.forEach(p => {
        if (!p.leftAt) {
          p.leftAt = new Date();
        }
      });
    }

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Update call settings
  http.put(`${API_BASE}/calls/:callId/settings`, async ({ params, request }) => {
    const { callId } = params;
    const body = await request.json() as {
      userId: string;
      isMuted?: boolean;
      isVideoEnabled?: boolean;
      isScreenSharing?: boolean;
    };

    const participants = getMockCallParticipants(callId as string);
    const participant = participants.find(p => p.userId === body.userId);

    if (participant) {
      if (body.isMuted !== undefined) participant.isMuted = body.isMuted;
      if (body.isVideoEnabled !== undefined) participant.isVideoEnabled = body.isVideoEnabled;
      if (body.isScreenSharing !== undefined) participant.isScreenSharing = body.isScreenSharing;
    }

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Get call details
  http.get(`${API_BASE}/calls/:callId`, ({ params }) => {
    const { callId } = params;
    const call = getMockCallById(callId as string);

    if (!call) {
      return HttpResponse.json(
        { success: false, error: 'Call not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: call,
    });
  }),

  // Get call participants
  http.get(`${API_BASE}/calls/:callId/participants`, ({ params }) => {
    const { callId } = params;
    const participants = getMockCallParticipants(callId as string);

    return HttpResponse.json({
      success: true,
      data: participants,
    });
  }),

  // Start call recording
  http.post(`${API_BASE}/calls/:callId/recording/start`, ({ params }) => {
    const { callId } = params;
    const recordingId = `recording-${Date.now()}`;

    return HttpResponse.json({
      success: true,
      data: { recordingId },
    });
  }),

  // Stop call recording
  http.post(`${API_BASE}/calls/:callId/recording/stop`, async ({ params, request }) => {
    const { callId } = params;
    const body = await request.json() as { recordingId: string };
    const recordingUrl = `/recordings/${callId}-${body.recordingId}.mp4`;

    return HttpResponse.json({
      success: true,
      data: { recordingUrl },
    });
  }),

  // Get call recordings
  http.get(`${API_BASE}/calls/:callId/recordings`, ({ params }) => {
    const { callId } = params;

    // Mock recordings data
    const recordings = [
      {
        id: 'rec-1',
        callId: callId as string,
        name: `Call Recording - ${new Date().toLocaleDateString()}`,
        duration: 1800, // 30 minutes
        size: '45.2 MB',
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
        url: `/recordings/${callId}-rec-1.mp4`,
      },
      {
        id: 'rec-2',
        callId: callId as string,
        name: `Call Recording - ${new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toLocaleDateString()}`,
        duration: 2400, // 40 minutes
        size: '62.8 MB',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        url: `/recordings/${callId}-rec-2.mp4`,
      },
    ];

    return HttpResponse.json({
      success: true,
      data: recordings,
    });
  }),

  // Get call history
  http.get(`${API_BASE}/calls/history`, ({ request }) => {
    const url = new URL(request.url);
    const channelId = url.searchParams.get('channelId');
    const userId = url.searchParams.get('userId');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    let calls = mockCalls;

    if (channelId) {
      calls = calls.filter(call => call.channelId === channelId);
    }

    if (userId) {
      calls = calls.filter(call =>
        call.participantIds?.includes(userId) || call.startedBy === userId
      );
    }

    // Sort by most recent first
    calls.sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime());

    // Paginate
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCalls = calls.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedCalls,
      pagination: {
        page,
        limit,
        total: calls.length,
        totalPages: Math.ceil(calls.length / limit),
      },
    });
  }),

  // === SECURITY & ACCESS CONTROL ENDPOINTS ===

  // Get channel permissions
  http.get(`${API_BASE}/channels/:channelId/permissions`, ({ params }) => {
    const { channelId } = params;

    // Mock permissions data
    const permissions = [
      { userId: '1', roleId: 'admin', assignedAt: new Date(), assignedBy: '1' },
      { userId: '2', roleId: 'moderator', assignedAt: new Date(), assignedBy: '1' },
      { userId: '3', roleId: 'member', assignedAt: new Date(), assignedBy: '1' },
      { userId: '4', roleId: 'member', assignedAt: new Date(), assignedBy: '1' },
    ];

    return HttpResponse.json({
      success: true,
      data: permissions,
    });
  }),

  // Update channel permissions
  http.put(`${API_BASE}/channels/:channelId/permissions`, async ({ params, request }) => {
    const { channelId } = params;
    const body = await request.json() as { userId: string; roleId: string };

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Get audit log
  http.get(`${API_BASE}/audit`, ({ request }) => {
    const url = new URL(request.url);
    const channelId = url.searchParams.get('channelId');
    const userId = url.searchParams.get('userId');
    const action = url.searchParams.get('action');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');

    // Mock audit entries
    const auditEntries = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        userId: '2',
        action: 'message_sent',
        target: 'msg-123',
        targetType: 'message',
        details: { content: 'Hello team!', channelId: 'general' },
        ipAddress: '*************',
        severity: 'low',
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        userId: '1',
        action: 'user_kicked',
        target: '4',
        targetType: 'user',
        details: { reason: 'Spam', channelId: 'general' },
        ipAddress: '*************',
        severity: 'high',
      },
    ];

    return HttpResponse.json({
      success: true,
      data: auditEntries,
      pagination: {
        page,
        limit,
        total: auditEntries.length,
        totalPages: Math.ceil(auditEntries.length / limit),
      },
    });
  }),

  // Get flagged content
  http.get(`${API_BASE}/moderation/flagged`, ({ request }) => {
    const url = new URL(request.url);
    const status = url.searchParams.get('status');

    // Mock flagged content
    const flaggedContent = [
      {
        id: 'flag-1',
        type: 'message',
        contentId: 'msg-123',
        reason: 'Inappropriate language',
        reportedBy: '2',
        reportedAt: new Date(Date.now() - 30 * 60 * 1000),
        status: 'pending',
      },
      {
        id: 'flag-2',
        type: 'message',
        contentId: 'msg-124',
        reason: 'Spam content',
        reportedBy: '3',
        reportedAt: new Date(Date.now() - 60 * 60 * 1000),
        status: 'reviewed',
        moderatorId: '1',
      },
    ];

    return HttpResponse.json({
      success: true,
      data: status ? flaggedContent.filter(f => f.status === status) : flaggedContent,
    });
  }),

  // Take moderation action
  http.post(`${API_BASE}/moderation/action`, async ({ request }) => {
    const body = await request.json() as {
      flagId: string;
      action: string;
      reason: string;
      duration?: number;
    };

    return HttpResponse.json({
      success: true,
      data: {
        id: `action-${Date.now()}`,
        ...body,
        moderatorId: '1',
        timestamp: new Date(),
      },
    });
  }),

  // Get encryption settings
  http.get(`${API_BASE}/encryption/settings`, ({ request }) => {
    const url = new URL(request.url);
    const channelId = url.searchParams.get('channelId');

    // Mock encryption settings
    const settings = {
      enabled: false,
      algorithm: 'AES-256',
      keyRotationInterval: 30,
      requireEncryption: false,
      allowPlaintext: true,
    };

    return HttpResponse.json({
      success: true,
      data: settings,
    });
  }),

  // Update encryption settings
  http.put(`${API_BASE}/encryption/settings`, async ({ request }) => {
    const body = await request.json();

    return HttpResponse.json({
      success: true,
      data: body,
    });
  }),

  // Generate encryption key
  http.post(`${API_BASE}/encryption/keys/generate`, async ({ request }) => {
    const body = await request.json() as { algorithm: string };

    const newKey = {
      id: `key-${Date.now()}`,
      algorithm: body.algorithm,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      status: 'active',
      fingerprint: `SHA256:${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
    };

    return HttpResponse.json({
      success: true,
      data: newKey,
    });
  }),

  // Get encryption keys
  http.get(`${API_BASE}/encryption/keys`, () => {
    // Mock encryption keys
    const keys = [
      {
        id: 'key-1',
        algorithm: 'AES-256',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        expiresAt: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000),
        status: 'active',
        fingerprint: 'SHA256:nThbg6kXUpJWGl7E1IGOCspRomTxdCARLviKw6E5SY8',
      },
    ];

    return HttpResponse.json({
      success: true,
      data: keys,
    });
  }),

  // Revoke encryption key
  http.post(`${API_BASE}/encryption/keys/:keyId/revoke`, ({ params }) => {
    const { keyId } = params;

    return HttpResponse.json({
      success: true,
      data: null,
    });
  }),

  // Get call history
  http.get(`${API_BASE}/calls/history`, ({ request }) => {
    const url = new URL(request.url);
    const channelId = url.searchParams.get('channelId');
    const userId = url.searchParams.get('userId');
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    let filteredCalls = mockCalls;

    if (channelId) {
      filteredCalls = getMockCallsByChannelId(channelId);
    } else if (userId) {
      filteredCalls = getMockCallsByUserId(userId);
    }

    // Sort by most recent first
    filteredCalls.sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime());

    // Paginate
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedCalls = filteredCalls.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedCalls,
      pagination: {
        page,
        pageSize,
        total: filteredCalls.length,
        hasMore: endIndex < filteredCalls.length,
      },
    });
  }),

  // Bot API endpoints
  // Get all bots
  http.get(`${API_BASE}/bots`, ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedBots = mockBots.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedBots,
      total: mockBots.length,
      page,
      pageSize,
      hasMore: endIndex < mockBots.length,
    });
  }),

  // Get bot by ID
  http.get(`${API_BASE}/bots/:id`, ({ params }) => {
    const { id } = params;
    const bot = getMockBotById(id as string);

    if (!bot) {
      return HttpResponse.json(
        { success: false, error: 'Bot not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: bot,
    });
  }),

  // Create bot
  http.post(`${API_BASE}/bots`, async ({ request }) => {
    const botData = await request.json() as any;
    const newBot = {
      id: `bot-${Date.now()}`,
      ...botData,
      createdAt: new Date(),
      lastActive: new Date(),
      analytics: {
        totalInteractions: 0,
        successfulResponses: 0,
        failedResponses: 0,
        averageResponseTime: 0,
        popularCommands: {},
      },
    };

    mockBots.push(newBot);

    return HttpResponse.json({
      success: true,
      data: newBot,
    });
  }),

  // Update bot
  http.put(`${API_BASE}/bots/:id`, async ({ params, request }) => {
    const { id } = params;
    const updates = await request.json() as any;
    const botIndex = mockBots.findIndex(bot => bot.id === id);

    if (botIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Bot not found' },
        { status: 404 }
      );
    }

    mockBots[botIndex] = { ...mockBots[botIndex], ...updates };

    return HttpResponse.json({
      success: true,
      data: mockBots[botIndex],
    });
  }),

  // Delete bot
  http.delete(`${API_BASE}/bots/:id`, ({ params }) => {
    const { id } = params;
    const botIndex = mockBots.findIndex(bot => bot.id === id);

    if (botIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Bot not found' },
        { status: 404 }
      );
    }

    mockBots.splice(botIndex, 1);

    return HttpResponse.json({
      success: true,
    });
  }),

  // Toggle bot
  http.post(`${API_BASE}/bots/:id/toggle`, async ({ params, request }) => {
    const { id } = params;
    const { enabled } = await request.json() as any;
    const botIndex = mockBots.findIndex(bot => bot.id === id);

    if (botIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Bot not found' },
        { status: 404 }
      );
    }

    mockBots[botIndex].enabled = enabled;

    return HttpResponse.json({
      success: true,
      data: mockBots[botIndex],
    });
  }),

  // Bot interaction
  http.post(`${API_BASE}/bots/:id/interact`, async ({ params, request }) => {
    const { id } = params;
    const interaction = await request.json() as any;
    const bot = getMockBotById(id as string);

    if (!bot) {
      return HttpResponse.json(
        { success: false, error: 'Bot not found' },
        { status: 404 }
      );
    }

    // Simulate bot response
    return HttpResponse.json({
      success: true,
      response: `Bot ${bot.name} processed command: ${interaction.command}`,
      actions: [],
    });
  }),

  // Bot analytics
  http.get(`${API_BASE}/bots/:id/analytics`, ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || 'week';
    const bot = getMockBotById(id as string);

    if (!bot) {
      return HttpResponse.json(
        { success: false, error: 'Bot not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: {
        ...bot.analytics,
        period,
        chartData: [
          { date: '2024-01-01', interactions: 12 },
          { date: '2024-01-02', interactions: 18 },
          { date: '2024-01-03', interactions: 15 },
        ],
      },
    });
  }),

  // Process message for bots
  http.post(`${API_BASE}/bots/process-message`, async ({ request }) => {
    const { message } = await request.json() as any;

    // Simulate bot processing
    console.log('Processing message for bots:', message);

    return HttpResponse.json({
      success: true,
    });
  }),

  // Automation API endpoints
  // Get automation rules
  http.get(`${API_BASE}/automation`, ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedRules = mockAutomationRules.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedRules,
      total: mockAutomationRules.length,
      page,
      pageSize,
      hasMore: endIndex < mockAutomationRules.length,
    });
  }),

  // Create automation rule
  http.post(`${API_BASE}/automation`, async ({ request }) => {
    const ruleData = await request.json() as any;
    const newRule = {
      id: `auto-${Date.now()}`,
      ...ruleData,
      createdAt: new Date(),
      executionCount: 0,
    };

    mockAutomationRules.push(newRule);

    return HttpResponse.json({
      success: true,
      data: newRule,
    });
  }),

  // Update automation rule
  http.put(`${API_BASE}/automation/:id`, async ({ params, request }) => {
    const { id } = params;
    const updates = await request.json() as any;
    const ruleIndex = mockAutomationRules.findIndex(rule => rule.id === id);

    if (ruleIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Automation rule not found' },
        { status: 404 }
      );
    }

    mockAutomationRules[ruleIndex] = { ...mockAutomationRules[ruleIndex], ...updates };

    return HttpResponse.json({
      success: true,
      data: mockAutomationRules[ruleIndex],
    });
  }),

  // Delete automation rule
  http.delete(`${API_BASE}/automation/:id`, ({ params }) => {
    const { id } = params;
    const ruleIndex = mockAutomationRules.findIndex(rule => rule.id === id);

    if (ruleIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Automation rule not found' },
        { status: 404 }
      );
    }

    mockAutomationRules.splice(ruleIndex, 1);

    return HttpResponse.json({
      success: true,
    });
  }),

  // Scheduled Messages API endpoints
  // Get scheduled messages
  http.get(`${API_BASE}/scheduled-messages`, ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedMessages = mockScheduledMessages.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedMessages,
      total: mockScheduledMessages.length,
      page,
      pageSize,
      hasMore: endIndex < mockScheduledMessages.length,
    });
  }),

  // Create scheduled message
  http.post(`${API_BASE}/scheduled-messages`, async ({ request }) => {
    const messageData = await request.json() as any;
    const newMessage = {
      id: `sched-${Date.now()}`,
      ...messageData,
      status: 'pending' as const,
      createdAt: new Date(),
    };

    mockScheduledMessages.push(newMessage);

    return HttpResponse.json({
      success: true,
      data: newMessage,
    });
  }),

  // Cancel scheduled message
  http.post(`${API_BASE}/scheduled-messages/:id/cancel`, ({ params }) => {
    const { id } = params;
    const messageIndex = mockScheduledMessages.findIndex(msg => msg.id === id);

    if (messageIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Scheduled message not found' },
        { status: 404 }
      );
    }

    mockScheduledMessages[messageIndex].status = 'cancelled';

    return HttpResponse.json({
      success: true,
    });
  }),

  // Archival API endpoints
  // Search archive
  http.get(`${API_BASE}/archival/search`, ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const channels = url.searchParams.get('channels')?.split(',') || [];
    const users = url.searchParams.get('users')?.split(',') || [];
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '50');

    let filteredArchives = mockMessageArchives;

    if (query) {
      filteredArchives = filteredArchives.filter(archive =>
        archive.content.toLowerCase().includes(query.toLowerCase())
      );
    }

    if (channels.length > 0) {
      filteredArchives = filteredArchives.filter(archive =>
        channels.includes(archive.channelId)
      );
    }

    if (users.length > 0) {
      filteredArchives = filteredArchives.filter(archive =>
        users.includes(archive.authorId)
      );
    }

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedArchives = filteredArchives.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedArchives,
      total: filteredArchives.length,
      page,
      pageSize,
      hasMore: endIndex < filteredArchives.length,
    });
  }),

  // Get archived message by ID
  http.get(`${API_BASE}/archival/messages/:id`, ({ params }) => {
    const { id } = params;
    const archive = getMockArchivedMessageById(id as string);

    if (!archive) {
      return HttpResponse.json(
        { success: false, error: 'Archived message not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: archive,
    });
  }),

  // Archive messages manually
  http.post(`${API_BASE}/archival/archive`, async ({ request }) => {
    const { messageIds, reason } = await request.json() as any;

    // Simulate archiving process
    console.log('Archiving messages:', messageIds, 'Reason:', reason);

    return HttpResponse.json({
      success: true,
      message: `${messageIds.length} messages archived successfully`,
    });
  }),

  // Get archive statistics
  http.get(`${API_BASE}/archival/stats`, () => {
    return HttpResponse.json({
      success: true,
      data: {
        totalArchived: mockMessageArchives.length,
        archivedThisMonth: 45,
        storageUsed: '2.3 GB',
        retentionPolicies: mockRetentionPolicies.length,
        legalHolds: mockLegalHolds.length,
      },
    });
  }),

  // Retention Policies
  // Get retention policies
  http.get(`${API_BASE}/archival/retention`, ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedPolicies = mockRetentionPolicies.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedPolicies,
      total: mockRetentionPolicies.length,
      page,
      pageSize,
      hasMore: endIndex < mockRetentionPolicies.length,
    });
  }),

  // Create retention policy
  http.post(`${API_BASE}/archival/retention`, async ({ request }) => {
    const policyData = await request.json() as any;
    const newPolicy = {
      id: `policy-${Date.now()}`,
      ...policyData,
      enabled: true,
      createdBy: '1',
      createdAt: new Date(),
    };

    mockRetentionPolicies.push(newPolicy);

    return HttpResponse.json({
      success: true,
      data: newPolicy,
    });
  }),

  // Update retention policy
  http.put(`${API_BASE}/archival/retention/:id`, async ({ params, request }) => {
    const { id } = params;
    const updates = await request.json() as any;
    const policyIndex = mockRetentionPolicies.findIndex(policy => policy.id === id);

    if (policyIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Retention policy not found' },
        { status: 404 }
      );
    }

    mockRetentionPolicies[policyIndex] = { ...mockRetentionPolicies[policyIndex], ...updates };

    return HttpResponse.json({
      success: true,
      data: mockRetentionPolicies[policyIndex],
    });
  }),

  // Delete retention policy
  http.delete(`${API_BASE}/archival/retention/:id`, ({ params }) => {
    const { id } = params;
    const policyIndex = mockRetentionPolicies.findIndex(policy => policy.id === id);

    if (policyIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Retention policy not found' },
        { status: 404 }
      );
    }

    mockRetentionPolicies.splice(policyIndex, 1);

    return HttpResponse.json({
      success: true,
    });
  }),

  // Apply retention policy
  http.post(`${API_BASE}/archival/retention/:id/apply`, ({ params }) => {
    const { id } = params;
    const policy = getMockRetentionPolicyById(id as string);

    if (!policy) {
      return HttpResponse.json(
        { success: false, error: 'Retention policy not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: {
        messagesProcessed: 150,
        messagesArchived: 45,
        messagesDeleted: 12,
      },
    });
  }),

  // Legal Holds
  // Get legal holds
  http.get(`${API_BASE}/archival/legal-holds`, ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedHolds = mockLegalHolds.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedHolds,
      total: mockLegalHolds.length,
      page,
      pageSize,
      hasMore: endIndex < mockLegalHolds.length,
    });
  }),

  // Create legal hold
  http.post(`${API_BASE}/archival/legal-holds`, async ({ request }) => {
    const holdData = await request.json() as any;
    const newHold = {
      id: `hold-${Date.now()}`,
      ...holdData,
      status: 'active' as const,
      createdBy: '1',
      createdAt: new Date(),
    };

    mockLegalHolds.push(newHold);

    return HttpResponse.json({
      success: true,
      data: newHold,
    });
  }),

  // Release legal hold
  http.post(`${API_BASE}/archival/legal-holds/:id/release`, async ({ params, request }) => {
    const { id } = params;
    const { reason } = await request.json() as any;
    const holdIndex = mockLegalHolds.findIndex(hold => hold.id === id);

    if (holdIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Legal hold not found' },
        { status: 404 }
      );
    }

    mockLegalHolds[holdIndex].status = 'released';
    mockLegalHolds[holdIndex].releasedAt = new Date();

    return HttpResponse.json({
      success: true,
      data: mockLegalHolds[holdIndex],
    });
  }),

  // Get legal hold messages
  http.get(`${API_BASE}/archival/legal-holds/:id/messages`, ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '50');

    const hold = getMockLegalHoldById(id as string);
    if (!hold) {
      return HttpResponse.json(
        { success: false, error: 'Legal hold not found' },
        { status: 404 }
      );
    }

    // Filter archives by legal hold criteria
    const filteredArchives = mockMessageArchives.filter(archive =>
      archive.metadata.legalHold
    );

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedArchives = filteredArchives.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedArchives,
      total: filteredArchives.length,
      page,
      pageSize,
      hasMore: endIndex < filteredArchives.length,
    });
  }),

  // Export Requests
  // Get export requests
  http.get(`${API_BASE}/archival/exports`, ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedExports = mockExportRequests.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedExports,
      total: mockExportRequests.length,
      page,
      pageSize,
      hasMore: endIndex < mockExportRequests.length,
    });
  }),

  // Create export request
  http.post(`${API_BASE}/archival/exports`, async ({ request }) => {
    const exportData = await request.json() as any;
    const newExport = {
      id: `export-${Date.now()}`,
      ...exportData,
      status: 'pending' as const,
      requestedBy: '1',
      requestedAt: new Date(),
    };

    mockExportRequests.push(newExport);

    return HttpResponse.json({
      success: true,
      data: newExport,
    });
  }),

  // Get export status
  http.get(`${API_BASE}/archival/exports/:id`, ({ params }) => {
    const { id } = params;
    const exportRequest = getMockExportRequestById(id as string);

    if (!exportRequest) {
      return HttpResponse.json(
        { success: false, error: 'Export request not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: exportRequest,
    });
  }),

  // Download export
  http.get(`${API_BASE}/archival/exports/:id/download`, ({ params }) => {
    const { id } = params;
    const exportRequest = getMockExportRequestById(id as string);

    if (!exportRequest || exportRequest.status !== 'completed') {
      return HttpResponse.json(
        { success: false, error: 'Export not available for download' },
        { status: 404 }
      );
    }

    // Return mock file content
    const mockFileContent = JSON.stringify({
      export: {
        id: exportRequest.id,
        name: exportRequest.name,
        generatedAt: new Date(),
        data: mockMessages.slice(0, 10), // Sample data
      },
    });

    return new HttpResponse(mockFileContent, {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${exportRequest.name}.json"`,
      },
    });
  }),

  // Cancel export
  http.post(`${API_BASE}/archival/exports/:id/cancel`, ({ params }) => {
    const { id } = params;
    const exportIndex = mockExportRequests.findIndex(req => req.id === id);

    if (exportIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Export request not found' },
        { status: 404 }
      );
    }

    mockExportRequests[exportIndex].status = 'failed';
    mockExportRequests[exportIndex].error = 'Cancelled by user';

    return HttpResponse.json({
      success: true,
    });
  }),

  // Integration API endpoints
  // Get integrations
  http.get(`${API_BASE}/integrations`, ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedIntegrations = mockExternalIntegrations.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedIntegrations,
      total: mockExternalIntegrations.length,
      page,
      pageSize,
      hasMore: endIndex < mockExternalIntegrations.length,
    });
  }),

  // Get integration by ID
  http.get(`${API_BASE}/integrations/:id`, ({ params }) => {
    const { id } = params;
    const integration = getMockIntegrationById(id as string);

    if (!integration) {
      return HttpResponse.json(
        { success: false, error: 'Integration not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: integration,
    });
  }),

  // Create integration
  http.post(`${API_BASE}/integrations`, async ({ request }) => {
    const integrationData = await request.json() as any;
    const newIntegration = {
      id: `int-${Date.now()}`,
      ...integrationData,
      status: 'pending' as const,
      enabled: true,
      createdBy: '1',
      createdAt: new Date(),
      syncStats: {
        totalSynced: 0,
        successfulSyncs: 0,
        failedSyncs: 0,
        averageSyncTime: 0,
      },
    };

    mockExternalIntegrations.push(newIntegration);

    return HttpResponse.json({
      success: true,
      data: newIntegration,
    });
  }),

  // Update integration
  http.put(`${API_BASE}/integrations/:id`, async ({ params, request }) => {
    const { id } = params;
    const updates = await request.json() as any;
    const integrationIndex = mockExternalIntegrations.findIndex(int => int.id === id);

    if (integrationIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Integration not found' },
        { status: 404 }
      );
    }

    mockExternalIntegrations[integrationIndex] = {
      ...mockExternalIntegrations[integrationIndex],
      ...updates
    };

    return HttpResponse.json({
      success: true,
      data: mockExternalIntegrations[integrationIndex],
    });
  }),

  // Delete integration
  http.delete(`${API_BASE}/integrations/:id`, ({ params }) => {
    const { id } = params;
    const integrationIndex = mockExternalIntegrations.findIndex(int => int.id === id);

    if (integrationIndex === -1) {
      return HttpResponse.json(
        { success: false, error: 'Integration not found' },
        { status: 404 }
      );
    }

    mockExternalIntegrations.splice(integrationIndex, 1);

    return HttpResponse.json({
      success: true,
    });
  }),

  // Test integration
  http.post(`${API_BASE}/integrations/:id/test`, ({ params }) => {
    const { id } = params;
    const integration = getMockIntegrationById(id as string);

    if (!integration) {
      return HttpResponse.json(
        { success: false, error: 'Integration not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: {
        status: 'success',
        message: 'Integration test completed successfully',
        responseTime: 250,
        details: {
          connection: 'OK',
          authentication: 'OK',
          permissions: 'OK',
        },
      },
    });
  }),

  // Sync integration
  http.post(`${API_BASE}/integrations/:id/sync`, ({ params }) => {
    const { id } = params;
    const integration = getMockIntegrationById(id as string);

    if (!integration) {
      return HttpResponse.json(
        { success: false, error: 'Integration not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: {
        syncId: `sync-${Date.now()}`,
        status: 'started',
        estimatedDuration: 30000, // 30 seconds
      },
    });
  }),

  // Get integration logs
  http.get(`${API_BASE}/integrations/:id/logs`, ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '50');

    const integration = getMockIntegrationById(id as string);
    if (!integration) {
      return HttpResponse.json(
        { success: false, error: 'Integration not found' },
        { status: 404 }
      );
    }

    // Mock log entries
    const mockLogs = [
      {
        id: 'log-1',
        timestamp: new Date(),
        level: 'info',
        message: 'Sync completed successfully',
        details: { messagesSynced: 25 },
      },
      {
        id: 'log-2',
        timestamp: new Date(Date.now() - 60000),
        level: 'warning',
        message: 'Rate limit approaching',
        details: { remainingRequests: 10 },
      },
    ];

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedLogs = mockLogs.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedLogs,
      total: mockLogs.length,
      page,
      pageSize,
      hasMore: endIndex < mockLogs.length,
    });
  }),
];

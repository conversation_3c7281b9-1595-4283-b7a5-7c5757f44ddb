import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../../stores/themeStore';
import {
  ThemeToggle,
  CompanySelector,
  UserAvatarDropdown,
} from '../../ui';
import { cn } from '../../../utils/cn';
import {
  MenuIcon,
  ChevronLeftIcon,
} from '../../icons';

// Type Definitions
export interface DynamicAppHeaderProps {
  app: {
    name: string;
    icon: React.ReactNode;
    navLinks: { label: string; href: string; isActive?: boolean }[];
  };
  user: {
    name: string;
    avatar: React.ReactNode;
    notifications: { count: number; icon: React.ReactNode }[];
  };
  className?: string;
  'data-testid'?: string;
}

const DynamicAppHeader: React.FC<DynamicAppHeaderProps> = ({
  app,
  user,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isBackButtonHovered, setIsBackButtonHovered] = useState(false);

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <header
      className={cn('border-b backdrop-blur-sm', className)}
      style={{
        backgroundColor: colors.surface,
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      {/* Top Bar - Global Navigation */}
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left: App Icon, Name, and Navigation */}
          <div className="flex items-center space-x-8">
            {/* App Icon and Name - Enhanced hover with back arrow */}
            <button
              className="flex items-center space-x-3 px-1 py-1 rounded-lg transition-all duration-200"
              style={{
                backgroundColor: 'transparent',
              }}
              onMouseEnter={e => {
                setIsBackButtonHovered(true);
                e.currentTarget.style.backgroundColor = `${colors.hover}15`;
              }}
              onMouseLeave={e => {
                setIsBackButtonHovered(false);
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
              onClick={handleBackToDashboard}
              aria-label="Back to dashboard"
              title="Back to app grid"
            >
              <div className="w-8 h-8 flex items-center justify-center relative overflow-hidden">
                {/* App Icon */}
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-all duration-200 ${
                    isBackButtonHovered
                      ? 'transform translate-x-8 opacity-0'
                      : 'transform translate-x-0 opacity-100'
                  }`}
                >
                  {app.icon}
                </div>
                {/* Back Arrow */}
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-all duration-200 ${
                    isBackButtonHovered
                      ? 'transform translate-x-0 opacity-100'
                      : 'transform -translate-x-8 opacity-0'
                  }`}
                  style={{ color: colors.primary }}
                >
                  <ChevronLeftIcon className="w-5 h-5" />
                </div>
              </div>
              <h1
                className={`text-lg font-semibold tracking-tight transition-all duration-200 ${
                  isBackButtonHovered
                    ? 'transform -translate-x-1'
                    : 'transform translate-x-0'
                }`}
                style={{
                  color: isBackButtonHovered ? colors.primary : colors.text,
                }}
              >
                {app.name}
              </h1>
            </button>

            {/* Desktop Navigation Links - Clean hover only */}
            <nav className="hidden lg:flex items-center space-x-1">
              {app.navLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="px-4 py-2 text-sm font-medium rounded-md transition-all duration-150"
                  style={{
                    backgroundColor: link.isActive
                      ? `${colors.primary}10`
                      : 'transparent',
                    color: link.isActive
                      ? colors.primary
                      : colors.mutedForeground,
                  }}
                  onMouseEnter={e => {
                    if (!link.isActive) {
                      e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                      e.currentTarget.style.color = colors.text;
                    }
                  }}
                  onMouseLeave={e => {
                    if (!link.isActive) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = colors.mutedForeground;
                    }
                  }}
                >
                  {link.label}
                </a>
              ))}
            </nav>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden p-2 rounded-md transition-colors duration-150"
              style={{
                backgroundColor: 'transparent',
                color: colors.mutedForeground,
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                e.currentTarget.style.color = colors.text;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = colors.mutedForeground;
              }}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              <MenuIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Right: System & User Tray - Simplified */}
          <div className="flex items-center space-x-2">
            {/* Theme Toggle */}
            <ThemeToggle size="md" />

            {/* Company Selector */}
            <CompanySelector showLabel className="hidden sm:flex" />

            {/* Notifications - Clean hover only */}
            {user.notifications.map((notification, index) => (
              <button
                key={index}
                className="relative p-2 rounded-md transition-colors duration-150"
                style={{
                  backgroundColor: 'transparent',
                  color: colors.mutedForeground,
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                  e.currentTarget.style.color = colors.text;
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = colors.mutedForeground;
                }}
                aria-label={`Notifications (${notification.count})`}
              >
                {notification.icon}
                {notification.count > 0 && (
                  <span
                    className="absolute -top-0.5 -right-0.5 min-w-[18px] h-[18px] text-xs font-medium rounded-full flex items-center justify-center px-1"
                    style={{
                      backgroundColor: colors.error,
                      color: colors.errorForeground,
                    }}
                  >
                    {notification.count > 99 ? '99+' : notification.count}
                  </span>
                )}
              </button>
            ))}

            {/* User Avatar Dropdown */}
            <UserAvatarDropdown
              user={{
                name: user.name,
                avatar: user.avatar,
              }}
              onPreferences={() => console.log('Open preferences')}
              onLogout={() => console.log('Logout')}
              onDocumentation={() => console.log('Open documentation')}
              onShortcuts={() => console.log('Show shortcuts')}
              onInstallApp={() => console.log('Install app')}
              onOnboarding={() => console.log('Start onboarding')}
            />
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu - Simplified */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden border-t"
          style={{ borderColor: colors.border }}
        >
          <div className="px-4 py-4 space-y-1">
            {app.navLinks.map((link, index) => (
              <a
                key={index}
                href={link.href}
                className="block px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-150"
                style={{
                  backgroundColor: link.isActive
                    ? `${colors.primary}10`
                    : 'transparent',
                  color: link.isActive ? colors.primary : colors.text,
                }}
                onMouseEnter={e => {
                  if (!link.isActive) {
                    e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                  }
                }}
                onMouseLeave={e => {
                  if (!link.isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </a>
            ))}
          </div>
        </div>
      )}
    </header>
  );
};

export default DynamicAppHeader;

import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { Sidebar } from './Sidebar';

export interface DiscussLayoutProps {
  children: React.ReactNode;
  className?: string;
  'data-testid'?: string;
}

export const DiscussLayout: React.FC<DiscussLayoutProps> = ({
  children,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div
      className={`flex flex-1 ${className}`}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main Content Area */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {children}
      </main>
    </div>
  );
};

import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { Message, User } from '../../types';
import { UserAvatar } from './UserPresence';

export interface PinnedMessage {
  id: string;
  message: Message;
  author: User;
  pinnedBy: User;
  pinnedAt: Date;
  reason?: string;
}

export interface MessagePinProps {
  message: Message;
  author: User;
  isPinned: boolean;
  canPin: boolean;
  onPin: (messageId: string, reason?: string) => void;
  onUnpin: (messageId: string) => void;
  className?: string;
  'data-testid'?: string;
}

export const MessagePin: React.FC<MessagePinProps> = ({
  message,
  author,
  isPinned,
  canPin,
  onPin,
  onUnpin,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [showPinDialog, setShowPinDialog] = useState(false);
  const [pinReason, setPinReason] = useState('');

  const handlePinClick = () => {
    if (isPinned) {
      onUnpin(message.id);
    } else {
      setShowPinDialog(true);
    }
  };

  const handleConfirmPin = () => {
    onPin(message.id, pinReason.trim() || undefined);
    setShowPinDialog(false);
    setPinReason('');
  };

  const handleCancelPin = () => {
    setShowPinDialog(false);
    setPinReason('');
  };

  if (!canPin) return null;

  return (
    <>
      <button
        onClick={handlePinClick}
        className={`p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
          isPinned ? 'text-yellow-500' : ''
        } ${className}`}
        style={{ color: isPinned ? '#f59e0b' : colors.textSecondary }}
        title={isPinned ? 'Unpin message' : 'Pin message'}
        data-testid={testId}
      >
        📌
      </button>

      {/* Pin Dialog */}
      {showPinDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"
            style={{ backgroundColor: colors.background }}
          >
            <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
              Pin Message
            </h3>
            
            <div className="mb-4">
              <p className="text-sm mb-2" style={{ color: colors.textSecondary }}>
                Pin this message to make it easily accessible to all channel members.
              </p>
              
              {/* Message Preview */}
              <div
                className="p-3 border rounded-lg mb-3"
                style={{
                  borderColor: colors.border,
                  backgroundColor: colors.backgroundSecondary,
                }}
              >
                <div className="flex items-start space-x-2">
                  <UserAvatar user={author} size="small" />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-sm" style={{ color: colors.text }}>
                        {author.name}
                      </span>
                      <span className="text-xs" style={{ color: colors.textSecondary }}>
                        {new Date(message.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-sm" style={{ color: colors.text }}>
                      {message.content.length > 100 
                        ? `${message.content.substring(0, 100)}...` 
                        : message.content}
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Optional Reason */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
                  Reason (optional)
                </label>
                <input
                  type="text"
                  value={pinReason}
                  onChange={(e) => setPinReason(e.target.value)}
                  placeholder="Why is this message important?"
                  className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none"
                  style={{
                    borderColor: colors.border,
                    color: colors.text,
                  }}
                  maxLength={100}
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelPin}
                className="px-4 py-2 text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                style={{ color: colors.textSecondary }}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmPin}
                className="px-4 py-2 text-sm rounded-lg text-white transition-colors"
                style={{ backgroundColor: colors.primary }}
              >
                Pin Message
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export interface PinnedMessagesListProps {
  pinnedMessages: PinnedMessage[];
  onMessageClick: (message: Message) => void;
  onUnpin: (messageId: string) => void;
  canUnpin: (pinnedMessage: PinnedMessage) => boolean;
  className?: string;
  'data-testid'?: string;
}

export const PinnedMessagesList: React.FC<PinnedMessagesListProps> = ({
  pinnedMessages,
  onMessageClick,
  onUnpin,
  canUnpin,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  if (pinnedMessages.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`} data-testid={testId}>
        <div className="text-4xl mb-2">📌</div>
        <p className="text-sm" style={{ color: colors.textSecondary }}>
          No pinned messages yet
        </p>
      </div>
    );
  }

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <div className={`space-y-3 ${className}`} data-testid={testId}>
      <h3 className="text-sm font-medium flex items-center space-x-2" style={{ color: colors.text }}>
        <span>📌</span>
        <span>Pinned Messages ({pinnedMessages.length})</span>
      </h3>
      
      <div className="space-y-2">
        {pinnedMessages.map((pinnedMessage) => (
          <div
            key={pinnedMessage.id}
            className="border rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
            style={{
              borderColor: colors.border,
              backgroundColor: colors.backgroundSecondary,
            }}
            onClick={() => onMessageClick(pinnedMessage.message)}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-2 flex-1 min-w-0">
                <UserAvatar user={pinnedMessage.author} size="small" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-sm" style={{ color: colors.text }}>
                      {pinnedMessage.author.name}
                    </span>
                    <span className="text-xs" style={{ color: colors.textSecondary }}>
                      {formatDate(pinnedMessage.message.timestamp)}
                    </span>
                  </div>
                  <p className="text-sm mb-2" style={{ color: colors.text }}>
                    {pinnedMessage.message.content.length > 150 
                      ? `${pinnedMessage.message.content.substring(0, 150)}...` 
                      : pinnedMessage.message.content}
                  </p>
                  
                  {pinnedMessage.reason && (
                    <div
                      className="text-xs px-2 py-1 rounded"
                      style={{
                        backgroundColor: `${colors.primary}20`,
                        color: colors.primary,
                      }}
                    >
                      📌 {pinnedMessage.reason}
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2 mt-2">
                    <span className="text-xs" style={{ color: colors.textSecondary }}>
                      Pinned by {pinnedMessage.pinnedBy.name}
                    </span>
                    <span className="text-xs" style={{ color: colors.textSecondary }}>
                      {formatDate(pinnedMessage.pinnedAt)}
                    </span>
                  </div>
                </div>
              </div>
              
              {canUnpin(pinnedMessage) && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onUnpin(pinnedMessage.message.id);
                  }}
                  className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-red-500"
                  title="Unpin message"
                >
                  🗑️
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export interface PinnedMessageBannerProps {
  pinnedMessage: PinnedMessage;
  onViewAll: () => void;
  onDismiss: () => void;
  className?: string;
  'data-testid'?: string;
}

export const PinnedMessageBanner: React.FC<PinnedMessageBannerProps> = ({
  pinnedMessage,
  onViewAll,
  onDismiss,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  return (
    <div
      className={`flex items-center space-x-3 px-4 py-2 border-b ${className}`}
      style={{
        backgroundColor: `${colors.primary}10`,
        borderBottomColor: colors.border,
      }}
      data-testid={testId}
    >
      <div className="text-yellow-500">📌</div>
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium" style={{ color: colors.text }}>
          {pinnedMessage.author.name}: {pinnedMessage.message.content.length > 60 
            ? `${pinnedMessage.message.content.substring(0, 60)}...` 
            : pinnedMessage.message.content}
        </p>
        {pinnedMessage.reason && (
          <p className="text-xs" style={{ color: colors.textSecondary }}>
            {pinnedMessage.reason}
          </p>
        )}
      </div>
      
      <button
        onClick={onViewAll}
        className="text-xs px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        style={{ color: colors.primary }}
      >
        View All
      </button>
      
      <button
        onClick={onDismiss}
        className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        style={{ color: colors.textSecondary }}
      >
        ✕
      </button>
    </div>
  );
};

// External integration service for connecting with external platforms
import type { 
  ExternalIntegration,
  ERPIntegration,
  WebhookEndpoint,
  WebhookDelivery,
  APIKey,
  ApiResponse,
  PaginatedResponse 
} from '../types';

const API_BASE = '/api/discuss/integrations';

export interface CreateIntegrationRequest {
  name: string;
  type: ExternalIntegration['type'];
  config: ExternalIntegration['config'];
  credentials: Omit<ExternalIntegration['credentials'], 'data'> & {
    data: Record<string, string>; // Unencrypted for creation
  };
}

export interface UpdateIntegrationRequest {
  name?: string;
  config?: Partial<ExternalIntegration['config']>;
  enabled?: boolean;
}

export interface CreateWebhookRequest {
  name: string;
  url: string;
  events: WebhookEndpoint['events'];
  headers?: Record<string, string>;
  secret?: string;
  retryPolicy?: WebhookEndpoint['retryPolicy'];
}

export interface CreateAPIKeyRequest {
  name: string;
  permissions: APIKey['permissions'];
  rateLimit?: APIKey['rateLimit'];
  expiresAt?: Date;
}

export const integrationService = {
  // Get all integrations
  async getIntegrations(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<ExternalIntegration>> {
    const response = await fetch(`${API_BASE}?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch integrations');
    }
    
    return response.json();
  },

  // Get integration by ID
  async getIntegrationById(integrationId: string): Promise<ApiResponse<ExternalIntegration>> {
    const response = await fetch(`${API_BASE}/${integrationId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch integration');
    }
    
    return response.json();
  },

  // Create new integration
  async createIntegration(request: CreateIntegrationRequest): Promise<ApiResponse<ExternalIntegration>> {
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create integration');
    }
    
    return response.json();
  },

  // Update integration
  async updateIntegration(integrationId: string, request: UpdateIntegrationRequest): Promise<ApiResponse<ExternalIntegration>> {
    const response = await fetch(`${API_BASE}/${integrationId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update integration');
    }
    
    return response.json();
  },

  // Delete integration
  async deleteIntegration(integrationId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/${integrationId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete integration');
    }
    
    return response.json();
  },

  // Test integration connection
  async testIntegration(integrationId: string): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/${integrationId}/test`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to test integration');
    }
    
    return response.json();
  },

  // Sync integration data
  async syncIntegration(integrationId: string): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/${integrationId}/sync`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to sync integration');
    }
    
    return response.json();
  },

  // Get integration logs
  async getIntegrationLogs(integrationId: string, page: number = 1, pageSize: number = 50): Promise<PaginatedResponse<any>> {
    const response = await fetch(`${API_BASE}/${integrationId}/logs?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch integration logs');
    }
    
    return response.json();
  },
};

export const erpIntegrationService = {
  // Get ERP integrations
  async getERPIntegrations(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<ERPIntegration>> {
    const response = await fetch(`${API_BASE}/erp?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch ERP integrations');
    }
    
    return response.json();
  },

  // Create ERP integration
  async createERPIntegration(integration: Omit<ERPIntegration, 'id' | 'createdAt'>): Promise<ApiResponse<ERPIntegration>> {
    const response = await fetch(`${API_BASE}/erp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(integration),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create ERP integration');
    }
    
    return response.json();
  },

  // Update ERP integration
  async updateERPIntegration(integrationId: string, updates: Partial<ERPIntegration>): Promise<ApiResponse<ERPIntegration>> {
    const response = await fetch(`${API_BASE}/erp/${integrationId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update ERP integration');
    }
    
    return response.json();
  },

  // Delete ERP integration
  async deleteERPIntegration(integrationId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/erp/${integrationId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete ERP integration');
    }
    
    return response.json();
  },

  // Get ERP record context
  async getERPRecordContext(module: string, recordType: string, recordId: string): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/erp/context/${module}/${recordType}/${recordId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch ERP record context');
    }
    
    return response.json();
  },
};

export const webhookService = {
  // Get webhooks
  async getWebhooks(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<WebhookEndpoint>> {
    const response = await fetch(`${API_BASE}/webhooks?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch webhooks');
    }
    
    return response.json();
  },

  // Create webhook
  async createWebhook(request: CreateWebhookRequest): Promise<ApiResponse<WebhookEndpoint>> {
    const response = await fetch(`${API_BASE}/webhooks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create webhook');
    }
    
    return response.json();
  },

  // Update webhook
  async updateWebhook(webhookId: string, updates: Partial<CreateWebhookRequest>): Promise<ApiResponse<WebhookEndpoint>> {
    const response = await fetch(`${API_BASE}/webhooks/${webhookId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update webhook');
    }
    
    return response.json();
  },

  // Delete webhook
  async deleteWebhook(webhookId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/webhooks/${webhookId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete webhook');
    }
    
    return response.json();
  },

  // Test webhook
  async testWebhook(webhookId: string, testPayload?: any): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/webhooks/${webhookId}/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ payload: testPayload }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to test webhook');
    }
    
    return response.json();
  },

  // Get webhook deliveries
  async getWebhookDeliveries(webhookId: string, page: number = 1, pageSize: number = 50): Promise<PaginatedResponse<WebhookDelivery>> {
    const response = await fetch(`${API_BASE}/webhooks/${webhookId}/deliveries?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch webhook deliveries');
    }
    
    return response.json();
  },

  // Retry webhook delivery
  async retryWebhookDelivery(deliveryId: string): Promise<ApiResponse<WebhookDelivery>> {
    const response = await fetch(`${API_BASE}/webhook-deliveries/${deliveryId}/retry`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to retry webhook delivery');
    }
    
    return response.json();
  },
};

export const apiKeyService = {
  // Get API keys
  async getAPIKeys(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<Omit<APIKey, 'key'>>> {
    const response = await fetch(`${API_BASE}/api-keys?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch API keys');
    }
    
    return response.json();
  },

  // Create API key
  async createAPIKey(request: CreateAPIKeyRequest): Promise<ApiResponse<APIKey>> {
    const response = await fetch(`${API_BASE}/api-keys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create API key');
    }
    
    return response.json();
  },

  // Update API key
  async updateAPIKey(keyId: string, updates: Partial<CreateAPIKeyRequest>): Promise<ApiResponse<Omit<APIKey, 'key'>>> {
    const response = await fetch(`${API_BASE}/api-keys/${keyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update API key');
    }
    
    return response.json();
  },

  // Delete API key
  async deleteAPIKey(keyId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/api-keys/${keyId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete API key');
    }
    
    return response.json();
  },

  // Get API key usage
  async getAPIKeyUsage(keyId: string, period: 'day' | 'week' | 'month' = 'week'): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/api-keys/${keyId}/usage?period=${period}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch API key usage');
    }
    
    return response.json();
  },
};

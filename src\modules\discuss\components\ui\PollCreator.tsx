import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface PollOption {
  id: string;
  text: string;
  votes: number;
  voters: string[];
}

export interface Poll {
  id: string;
  question: string;
  options: PollOption[];
  allowMultiple: boolean;
  anonymous: boolean;
  expiresAt?: Date;
  createdBy: string;
  createdAt: Date;
  totalVotes: number;
  isActive: boolean;
}

export interface PollCreatorProps {
  onCreatePoll: (poll: Omit<Poll, 'id' | 'createdAt' | 'totalVotes' | 'isActive'>) => void;
  onCancel: () => void;
  className?: string;
  'data-testid'?: string;
}

export const PollCreator: React.FC<PollCreatorProps> = ({
  onCreatePoll,
  onCancel,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [question, setQuestion] = useState('');
  const [options, setOptions] = useState<string[]>(['', '']);
  const [allowMultiple, setAllowMultiple] = useState(false);
  const [anonymous, setAnonymous] = useState(false);
  const [hasExpiry, setHasExpiry] = useState(false);
  const [expiryDays, setExpiryDays] = useState(7);

  const addOption = () => {
    if (options.length < 10) {
      setOptions([...options, '']);
    }
  };

  const removeOption = (index: number) => {
    if (options.length > 2) {
      setOptions(options.filter((_, i) => i !== index));
    }
  };

  const updateOption = (index: number, value: string) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  const handleSubmit = () => {
    const validOptions = options.filter(opt => opt.trim().length > 0);
    
    if (question.trim().length === 0 || validOptions.length < 2) {
      return;
    }

    const pollOptions: PollOption[] = validOptions.map((text, index) => ({
      id: `option-${index}`,
      text: text.trim(),
      votes: 0,
      voters: [],
    }));

    const expiresAt = hasExpiry 
      ? new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000)
      : undefined;

    onCreatePoll({
      question: question.trim(),
      options: pollOptions,
      allowMultiple,
      anonymous,
      expiresAt,
      createdBy: '1', // Current user ID
    });
  };

  const isValid = question.trim().length > 0 && 
                  options.filter(opt => opt.trim().length > 0).length >= 2;

  return (
    <div
      className={`border rounded-lg p-4 ${className}`}
      style={{
        borderColor: colors.border,
        backgroundColor: colors.background,
      }}
      data-testid={testId}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold" style={{ color: colors.text }}>
            Create Poll
          </h3>
          <button
            onClick={onCancel}
            className="text-sm px-3 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            style={{ color: colors.textSecondary }}
          >
            Cancel
          </button>
        </div>

        {/* Question */}
        <div>
          <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
            Question
          </label>
          <input
            type="text"
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            placeholder="What would you like to ask?"
            className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none"
            style={{
              borderColor: colors.border,
              color: colors.text,
            }}
            maxLength={200}
          />
          <div className="text-xs mt-1" style={{ color: colors.textSecondary }}>
            {question.length}/200 characters
          </div>
        </div>

        {/* Options */}
        <div>
          <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
            Options
          </label>
          <div className="space-y-2">
            {options.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className="flex-1">
                  <input
                    type="text"
                    value={option}
                    onChange={(e) => updateOption(index, e.target.value)}
                    placeholder={`Option ${index + 1}`}
                    className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none"
                    style={{
                      borderColor: colors.border,
                      color: colors.text,
                    }}
                    maxLength={100}
                  />
                </div>
                {options.length > 2 && (
                  <button
                    onClick={() => removeOption(index)}
                    className="p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900 rounded transition-colors"
                    title="Remove option"
                  >
                    🗑️
                  </button>
                )}
              </div>
            ))}
          </div>
          
          {options.length < 10 && (
            <button
              onClick={addOption}
              className="mt-2 text-sm px-3 py-1 rounded border border-dashed hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              style={{
                borderColor: colors.border,
                color: colors.primary,
              }}
            >
              + Add Option
            </button>
          )}
        </div>

        {/* Settings */}
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="allowMultiple"
              checked={allowMultiple}
              onChange={(e) => setAllowMultiple(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="allowMultiple" className="text-sm" style={{ color: colors.text }}>
              Allow multiple selections
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="anonymous"
              checked={anonymous}
              onChange={(e) => setAnonymous(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="anonymous" className="text-sm" style={{ color: colors.text }}>
              Anonymous voting
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="hasExpiry"
              checked={hasExpiry}
              onChange={(e) => setHasExpiry(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="hasExpiry" className="text-sm" style={{ color: colors.text }}>
              Set expiry date
            </label>
          </div>

          {hasExpiry && (
            <div className="ml-6 flex items-center space-x-2">
              <span className="text-sm" style={{ color: colors.text }}>
                Expires in
              </span>
              <select
                value={expiryDays}
                onChange={(e) => setExpiryDays(parseInt(e.target.value))}
                className="px-2 py-1 border rounded bg-transparent outline-none"
                style={{
                  borderColor: colors.border,
                  color: colors.text,
                }}
              >
                <option value={1}>1 day</option>
                <option value={3}>3 days</option>
                <option value={7}>1 week</option>
                <option value={14}>2 weeks</option>
                <option value={30}>1 month</option>
              </select>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t" style={{ borderTopColor: colors.border }}>
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            style={{ color: colors.textSecondary }}
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={!isValid}
            className="px-4 py-2 text-sm rounded-lg text-white disabled:opacity-50 transition-colors"
            style={{ backgroundColor: colors.primary }}
          >
            Create Poll
          </button>
        </div>
      </div>
    </div>
  );
};

export interface PollDisplayProps {
  poll: Poll;
  currentUserId: string;
  onVote: (pollId: string, optionIds: string[]) => void;
  onClosePoll?: (pollId: string) => void;
  canClosePoll?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const PollDisplay: React.FC<PollDisplayProps> = ({
  poll,
  currentUserId,
  onVote,
  onClosePoll,
  canClosePoll = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [hasVoted, setHasVoted] = useState(() => 
    poll.options.some(option => option.voters.includes(currentUserId))
  );

  const handleOptionSelect = (optionId: string) => {
    if (hasVoted || !poll.isActive) return;

    if (poll.allowMultiple) {
      setSelectedOptions(prev => 
        prev.includes(optionId)
          ? prev.filter(id => id !== optionId)
          : [...prev, optionId]
      );
    } else {
      setSelectedOptions([optionId]);
    }
  };

  const handleVote = () => {
    if (selectedOptions.length > 0 && !hasVoted && poll.isActive) {
      onVote(poll.id, selectedOptions);
      setHasVoted(true);
      setSelectedOptions([]);
    }
  };

  const getOptionPercentage = (option: PollOption) => {
    return poll.totalVotes > 0 ? (option.votes / poll.totalVotes) * 100 : 0;
  };

  const isExpired = poll.expiresAt && new Date() > poll.expiresAt;
  const canVote = poll.isActive && !hasVoted && !isExpired;

  return (
    <div
      className={`border rounded-lg p-4 ${className}`}
      style={{
        borderColor: colors.border,
        backgroundColor: colors.backgroundSecondary,
      }}
      data-testid={testId}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-lg">📊</span>
              <span className="font-medium" style={{ color: colors.text }}>
                Poll
              </span>
              {!poll.isActive && (
                <span
                  className="text-xs px-2 py-1 rounded"
                  style={{
                    backgroundColor: colors.error + '20',
                    color: colors.error,
                  }}
                >
                  Closed
                </span>
              )}
              {isExpired && (
                <span
                  className="text-xs px-2 py-1 rounded"
                  style={{
                    backgroundColor: colors.textSecondary + '20',
                    color: colors.textSecondary,
                  }}
                >
                  Expired
                </span>
              )}
            </div>
            <h4 className="font-medium mb-2" style={{ color: colors.text }}>
              {poll.question}
            </h4>
          </div>
          
          {canClosePoll && poll.isActive && (
            <button
              onClick={() => onClosePoll?.(poll.id)}
              className="text-sm px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              style={{ color: colors.textSecondary }}
            >
              Close Poll
            </button>
          )}
        </div>

        {/* Options */}
        <div className="space-y-2">
          {poll.options.map((option) => {
            const percentage = getOptionPercentage(option);
            const isSelected = selectedOptions.includes(option.id);
            const userVoted = option.voters.includes(currentUserId);

            return (
              <div
                key={option.id}
                className={`relative border rounded-lg p-3 cursor-pointer transition-all ${
                  canVote ? 'hover:bg-gray-50 dark:hover:bg-gray-700' : ''
                } ${
                  isSelected ? 'ring-2 ring-opacity-50' : ''
                }`}
                style={{
                  borderColor: isSelected ? colors.primary : colors.border,
                  ringColor: isSelected ? colors.primary : 'transparent',
                  backgroundColor: userVoted ? `${colors.primary}10` : 'transparent',
                }}
                onClick={() => handleOptionSelect(option.id)}
              >
                {/* Progress bar background */}
                {hasVoted && (
                  <div
                    className="absolute inset-0 rounded-lg opacity-20"
                    style={{
                      background: `linear-gradient(to right, ${colors.primary} ${percentage}%, transparent ${percentage}%)`,
                    }}
                  />
                )}

                <div className="relative flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {canVote && (
                      <input
                        type={poll.allowMultiple ? 'checkbox' : 'radio'}
                        checked={isSelected}
                        onChange={() => {}}
                        className="pointer-events-none"
                      />
                    )}
                    <span style={{ color: colors.text }}>
                      {option.text}
                    </span>
                    {userVoted && (
                      <span className="text-xs" style={{ color: colors.primary }}>
                        ✓ Your vote
                      </span>
                    )}
                  </div>
                  
                  {hasVoted && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium" style={{ color: colors.text }}>
                        {option.votes}
                      </span>
                      <span className="text-sm" style={{ color: colors.textSecondary }}>
                        ({percentage.toFixed(1)}%)
                      </span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Vote Button */}
        {canVote && selectedOptions.length > 0 && (
          <button
            onClick={handleVote}
            className="w-full py-2 text-sm rounded-lg text-white transition-colors"
            style={{ backgroundColor: colors.primary }}
          >
            Vote
          </button>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between text-sm" style={{ color: colors.textSecondary }}>
          <div className="flex items-center space-x-4">
            <span>{poll.totalVotes} votes</span>
            {poll.allowMultiple && <span>Multiple choice</span>}
            {poll.anonymous && <span>Anonymous</span>}
          </div>
          
          {poll.expiresAt && (
            <span>
              Expires {poll.expiresAt.toLocaleDateString()}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

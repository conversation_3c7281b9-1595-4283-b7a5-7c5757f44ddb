import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { botService } from '../../services';
import type { Bot, BotCommand, BotTrigger } from '../../types';

export interface BotManagementProps {
  className?: string;
  'data-testid'?: string;
}

export const BotManagement: React.FC<BotManagementProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [bots, setBots] = useState<Bot[]>([]);
  const [selectedBot, setSelectedBot] = useState<Bot | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load bots on component mount
  useEffect(() => {
    loadBots();
  }, []);

  const loadBots = async () => {
    try {
      setIsLoading(true);
      const response = await botService.getBots();
      setBots(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load bots');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleBot = async (botId: string, enabled: boolean) => {
    try {
      await botService.toggleBot(botId, enabled);
      setBots(prev => prev.map(bot => 
        bot.id === botId ? { ...bot, enabled } : bot
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to toggle bot');
    }
  };

  const handleDeleteBot = async (botId: string) => {
    if (!confirm('Are you sure you want to delete this bot?')) return;

    try {
      await botService.deleteBot(botId);
      setBots(prev => prev.filter(bot => bot.id !== botId));
      if (selectedBot?.id === botId) {
        setSelectedBot(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete bot');
    }
  };

  const getBotTypeIcon = (type: Bot['type']) => {
    switch (type) {
      case 'faq': return '❓';
      case 'ai_assistant': return '🧠';
      case 'workflow': return '⚙️';
      case 'custom': return '🔧';
      default: return '🤖';
    }
  };

  const getBotStatusColor = (enabled: boolean) => {
    return enabled ? colors.success : colors.muted;
  };

  if (isLoading) {
    return (
      <div className={`p-6 ${className}`} data-testid={testId}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: colors.primary }}></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 ${className}`} data-testid={testId}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: colors.text }}>
            Bot Management
          </h2>
          <p className="text-sm mt-1" style={{ color: colors.muted }}>
            Manage your automated assistants and workflow bots
          </p>
        </div>
        <button
          onClick={() => setIsCreating(true)}
          className="px-4 py-2 rounded-lg text-white font-medium hover:opacity-90 transition-opacity"
          style={{ backgroundColor: colors.primary }}
        >
          Create Bot
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 rounded-lg" style={{ backgroundColor: colors.error + '20', color: colors.error }}>
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Bot List */}
        <div className="lg:col-span-1">
          <div className="rounded-lg border" style={{ borderColor: colors.border, backgroundColor: colors.background }}>
            <div className="p-4 border-b" style={{ borderColor: colors.border }}>
              <h3 className="font-semibold" style={{ color: colors.text }}>
                Bots ({bots.length})
              </h3>
            </div>
            <div className="max-h-96 overflow-y-auto">
              {bots.map((bot) => (
                <div
                  key={bot.id}
                  className={`p-4 border-b cursor-pointer hover:bg-opacity-50 transition-colors ${
                    selectedBot?.id === bot.id ? 'bg-opacity-20' : ''
                  }`}
                  style={{ 
                    borderColor: colors.border,
                    backgroundColor: selectedBot?.id === bot.id ? colors.primary + '20' : 'transparent'
                  }}
                  onClick={() => setSelectedBot(bot)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{bot.avatar || getBotTypeIcon(bot.type)}</span>
                      <div>
                        <div className="font-medium" style={{ color: colors.text }}>
                          {bot.name}
                        </div>
                        <div className="text-sm" style={{ color: colors.muted }}>
                          {bot.type.replace('_', ' ')}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: getBotStatusColor(bot.enabled) }}
                      />
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleBot(bot.id, !bot.enabled);
                        }}
                        className="text-xs px-2 py-1 rounded"
                        style={{ 
                          backgroundColor: bot.enabled ? colors.success + '20' : colors.muted + '20',
                          color: bot.enabled ? colors.success : colors.muted
                        }}
                      >
                        {bot.enabled ? 'ON' : 'OFF'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
              {bots.length === 0 && (
                <div className="p-8 text-center" style={{ color: colors.muted }}>
                  <div className="text-4xl mb-2">🤖</div>
                  <div>No bots configured</div>
                  <div className="text-sm">Create your first bot to get started</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bot Details */}
        <div className="lg:col-span-2">
          {selectedBot ? (
            <div className="rounded-lg border" style={{ borderColor: colors.border, backgroundColor: colors.background }}>
              <div className="p-4 border-b flex items-center justify-between" style={{ borderColor: colors.border }}>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{selectedBot.avatar || getBotTypeIcon(selectedBot.type)}</span>
                  <div>
                    <h3 className="font-semibold" style={{ color: colors.text }}>
                      {selectedBot.name}
                    </h3>
                    <p className="text-sm" style={{ color: colors.muted }}>
                      {selectedBot.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleToggleBot(selectedBot.id, !selectedBot.enabled)}
                    className="px-3 py-1 rounded text-sm font-medium"
                    style={{ 
                      backgroundColor: selectedBot.enabled ? colors.success : colors.muted,
                      color: 'white'
                    }}
                  >
                    {selectedBot.enabled ? 'Enabled' : 'Disabled'}
                  </button>
                  <button
                    onClick={() => handleDeleteBot(selectedBot.id)}
                    className="px-3 py-1 rounded text-sm font-medium text-white"
                    style={{ backgroundColor: colors.error }}
                  >
                    Delete
                  </button>
                </div>
              </div>

              <div className="p-4 space-y-6">
                {/* Commands */}
                <div>
                  <h4 className="font-medium mb-3" style={{ color: colors.text }}>
                    Commands ({selectedBot.commands.length})
                  </h4>
                  <div className="space-y-2">
                    {selectedBot.commands.map((command, index) => (
                      <div
                        key={index}
                        className="p-3 rounded border"
                        style={{ borderColor: colors.border, backgroundColor: colors.surface }}
                      >
                        <div className="flex items-center justify-between">
                          <code className="text-sm font-mono" style={{ color: colors.primary }}>
                            {command.command}
                          </code>
                          <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: colors.muted + '20', color: colors.muted }}>
                            {command.permissions.join(', ')}
                          </span>
                        </div>
                        <p className="text-sm mt-1" style={{ color: colors.muted }}>
                          {command.description}
                        </p>
                        <p className="text-xs mt-1" style={{ color: colors.muted }}>
                          Usage: <code>{command.usage}</code>
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Triggers */}
                <div>
                  <h4 className="font-medium mb-3" style={{ color: colors.text }}>
                    Triggers ({selectedBot.triggers.length})
                  </h4>
                  <div className="space-y-2">
                    {selectedBot.triggers.map((trigger, index) => (
                      <div
                        key={index}
                        className="p-3 rounded border"
                        style={{ borderColor: colors.border, backgroundColor: colors.surface }}
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium" style={{ color: colors.text }}>
                            {trigger.type.replace('_', ' ')}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded ${trigger.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                            {trigger.enabled ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <p className="text-sm mt-1" style={{ color: colors.muted }}>
                          Pattern: <code>{trigger.pattern}</code>
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Analytics */}
                <div>
                  <h4 className="font-medium mb-3" style={{ color: colors.text }}>
                    Analytics
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="p-3 rounded" style={{ backgroundColor: colors.surface }}>
                      <div className="text-2xl font-bold" style={{ color: colors.primary }}>
                        {selectedBot.analytics.totalInteractions}
                      </div>
                      <div className="text-sm" style={{ color: colors.muted }}>
                        Total Interactions
                      </div>
                    </div>
                    <div className="p-3 rounded" style={{ backgroundColor: colors.surface }}>
                      <div className="text-2xl font-bold" style={{ color: colors.success }}>
                        {selectedBot.analytics.successfulResponses}
                      </div>
                      <div className="text-sm" style={{ color: colors.muted }}>
                        Successful
                      </div>
                    </div>
                    <div className="p-3 rounded" style={{ backgroundColor: colors.surface }}>
                      <div className="text-2xl font-bold" style={{ color: colors.error }}>
                        {selectedBot.analytics.failedResponses}
                      </div>
                      <div className="text-sm" style={{ color: colors.muted }}>
                        Failed
                      </div>
                    </div>
                    <div className="p-3 rounded" style={{ backgroundColor: colors.surface }}>
                      <div className="text-2xl font-bold" style={{ color: colors.text }}>
                        {selectedBot.analytics.averageResponseTime}ms
                      </div>
                      <div className="text-sm" style={{ color: colors.muted }}>
                        Avg Response
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="rounded-lg border flex items-center justify-center h-96" style={{ borderColor: colors.border, backgroundColor: colors.background }}>
              <div className="text-center" style={{ color: colors.muted }}>
                <div className="text-4xl mb-2">🤖</div>
                <div>Select a bot to view details</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

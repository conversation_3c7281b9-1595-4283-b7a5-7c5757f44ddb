// Discuss-specific UI components will be exported here
// These are reusable UI components specific to the discuss module

// Input components
export { EmojiPicker } from './EmojiPicker';
export type { EmojiPickerProps } from './EmojiPicker';

export { FileUpload } from './FileUpload';
export type { FileUploadProps } from './FileUpload';

export { RichTextEditor } from './RichTextEditor';
export type { RichTextEditorProps } from './RichTextEditor';

export { MessageSearch } from './MessageSearch';
export type { MessageSearchProps, SearchResult, SearchFilters } from './MessageSearch';

export { ContentRenderer } from './ContentRenderer';
export type { ContentRendererProps } from './ContentRenderer';

export { FilePreview } from './FilePreview';
export type { FilePreviewProps } from './FilePreview';

// User presence components
export { UserPresence, UserAvatar, TypingIndicator, OnlineUsersList } from './UserPresence';
export type { UserPresenceProps, UserAvatarProps, TypingIndicatorProps, OnlineUsersListProps } from './UserPresence';

// Mention components
export { MentionSuggestions, MentionInput } from './MentionSuggestions';
export type { MentionSuggestionsProps, MentionInputProps, MentionSuggestion } from './MentionSuggestions';

// Reaction components
export { ReactionButton, ReactionPicker, MessageReactions, ReactionSummary, ReactionTooltip } from './ReactionButton';
export type { ReactionButtonProps, ReactionPickerProps, MessageReactionsProps, ReactionSummaryProps, ReactionTooltipProps } from './ReactionButton';

// Notification components
export { NotificationSettingsPanel } from './NotificationSettings';
export type { NotificationSettingsProps } from './NotificationSettings';

// Status components (moved to delivery status section below)

// Message pin components
export { MessagePin, PinnedMessagesList, PinnedMessageBanner } from './MessagePin';
export type { MessagePinProps, PinnedMessagesListProps, PinnedMessageBannerProps, PinnedMessage } from './MessagePin';

// Navigation and UX components
export { QuickSwitcher } from './QuickSwitcher';
export type { QuickSwitcherProps, QuickSwitcherItem } from './QuickSwitcher';

export { ViewModeSelector, ViewModeToggle } from './ViewModeSelector';
export type { ViewModeSelectorProps, ViewModeToggleProps, ViewMode, ViewModeOption } from './ViewModeSelector';

export { ChannelOrganizer } from './ChannelOrganizer';
export type { ChannelOrganizerProps, ChannelFolder } from './ChannelOrganizer';

// Collaboration components
export { PollCreator, PollDisplay } from './PollCreator';
export type { PollCreatorProps, PollDisplayProps, Poll, PollOption } from './PollCreator';

export { MessageToTask, TaskDisplay } from './TaskIntegration';
export type { MessageToTaskProps, TaskDisplayProps, Task } from './TaskIntegration';

// Delivery status components
export { DeliveryStatus, ReadReceiptTooltip, MessageStatusIndicator } from './DeliveryStatus';
export type { DeliveryStatusProps, ReadReceiptTooltipProps, MessageStatusIndicatorProps } from './DeliveryStatus';

// Call components
export { CallInterface } from './CallInterface';
export type { CallInterfaceProps } from './CallInterface';

export { CallButton, VoiceCallButton, VideoCallButton, CallButtonGroup, CallDropdown } from './CallButton';
export type { CallButtonProps, CallButtonGroupProps, CallDropdownProps } from './CallButton';

export { CallHistory, CallHistoryCompact } from './CallHistory';
export type { CallHistoryProps } from './CallHistory';

// Meeting components
export { InstantMeeting, QuickMeetingButton } from './InstantMeeting';
export type { InstantMeetingProps, QuickMeetingButtonProps } from './InstantMeeting';

export { MeetingRecording, RecordingHistory } from './MeetingRecording';
export type { MeetingRecordingProps, RecordingHistoryProps } from './MeetingRecording';

export { ConferenceRoom } from './ConferenceRoom';
export type { ConferenceRoomProps } from './ConferenceRoom';

export { MeetingInvitation, InvitationToast, InvitationManager } from './MeetingInvitation';
export type { MeetingInvitationProps, InvitationToastProps, InvitationManagerProps } from './MeetingInvitation';

// Security & Access Control components
export { ChannelPermissions } from './ChannelPermissions';
export type { ChannelPermissionsProps, ChannelRole, ChannelPermission, UserChannelRole } from './ChannelPermissions';

export { EncryptedMessaging } from './EncryptedMessaging';
export type { EncryptedMessagingProps, EncryptionSettings, EncryptionKey } from './EncryptedMessaging';

// Bot and AI components
export { BotManagement } from './BotManagement';
export type { BotManagementProps } from './BotManagement';

export { SmartResponseSuggestions } from './SmartResponseSuggestions';
export type { SmartResponseSuggestionsProps } from './SmartResponseSuggestions';

export { FAQManagement } from './FAQManagement';
export type { FAQManagementProps } from './FAQManagement';

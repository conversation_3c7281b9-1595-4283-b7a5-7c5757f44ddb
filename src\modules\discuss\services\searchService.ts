// Search service for message and content search functionality
import type { ApiResponse, Message, User, Channel } from '../types';

const API_BASE = '/api/discuss';

export interface SearchFilters {
  channels?: string[];
  authors?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  hasAttachments?: boolean;
  messageType?: 'all' | 'messages' | 'files' | 'links';
  sortBy?: 'relevance' | 'date' | 'author';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchResult {
  message: Message;
  author: User;
  channel: Channel;
  snippet: string;
  highlights: number[];
  score: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
  query: string;
  filters: SearchFilters;
  searchTime: number;
}

export const searchService = {
  // Search messages
  async searchMessages(
    query: string,
    filters: SearchFilters = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<ApiResponse<SearchResponse>> {
    const searchParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      pageSize: pageSize.toString(),
    });

    // Add filters to search params
    if (filters.channels?.length) {
      searchParams.append('channels', filters.channels.join(','));
    }
    
    if (filters.authors?.length) {
      searchParams.append('authors', filters.authors.join(','));
    }
    
    if (filters.dateFrom) {
      searchParams.append('dateFrom', filters.dateFrom.toISOString());
    }
    
    if (filters.dateTo) {
      searchParams.append('dateTo', filters.dateTo.toISOString());
    }
    
    if (filters.hasAttachments !== undefined) {
      searchParams.append('hasAttachments', filters.hasAttachments.toString());
    }
    
    if (filters.messageType && filters.messageType !== 'all') {
      searchParams.append('messageType', filters.messageType);
    }
    
    if (filters.sortBy) {
      searchParams.append('sortBy', filters.sortBy);
    }
    
    if (filters.sortOrder) {
      searchParams.append('sortOrder', filters.sortOrder);
    }

    const response = await fetch(`${API_BASE}/search/messages?${searchParams}`);
    
    if (!response.ok) {
      throw new Error('Search failed');
    }
    
    return response.json();
  },

  // Search users
  async searchUsers(
    query: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<ApiResponse<{
    users: User[];
    total: number;
    page: number;
    pageSize: number;
    hasMore: boolean;
  }>> {
    const searchParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      pageSize: pageSize.toString(),
    });

    const response = await fetch(`${API_BASE}/search/users?${searchParams}`);
    
    if (!response.ok) {
      throw new Error('User search failed');
    }
    
    return response.json();
  },

  // Search channels
  async searchChannels(
    query: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<ApiResponse<{
    channels: Channel[];
    total: number;
    page: number;
    pageSize: number;
    hasMore: boolean;
  }>> {
    const searchParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      pageSize: pageSize.toString(),
    });

    const response = await fetch(`${API_BASE}/search/channels?${searchParams}`);
    
    if (!response.ok) {
      throw new Error('Channel search failed');
    }
    
    return response.json();
  },

  // Search files/attachments
  async searchFiles(
    query: string,
    filters: {
      channels?: string[];
      fileTypes?: string[];
      dateFrom?: Date;
      dateTo?: Date;
    } = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<ApiResponse<{
    files: Array<{
      attachment: any;
      message: Message;
      author: User;
      channel: Channel;
    }>;
    total: number;
    page: number;
    pageSize: number;
    hasMore: boolean;
  }>> {
    const searchParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      pageSize: pageSize.toString(),
    });

    if (filters.channels?.length) {
      searchParams.append('channels', filters.channels.join(','));
    }
    
    if (filters.fileTypes?.length) {
      searchParams.append('fileTypes', filters.fileTypes.join(','));
    }
    
    if (filters.dateFrom) {
      searchParams.append('dateFrom', filters.dateFrom.toISOString());
    }
    
    if (filters.dateTo) {
      searchParams.append('dateTo', filters.dateTo.toISOString());
    }

    const response = await fetch(`${API_BASE}/search/files?${searchParams}`);
    
    if (!response.ok) {
      throw new Error('File search failed');
    }
    
    return response.json();
  },

  // Get search suggestions
  async getSearchSuggestions(
    query: string,
    type: 'all' | 'messages' | 'users' | 'channels' = 'all'
  ): Promise<ApiResponse<{
    suggestions: Array<{
      text: string;
      type: 'query' | 'user' | 'channel' | 'hashtag';
      data?: any;
    }>;
  }>> {
    const searchParams = new URLSearchParams({
      q: query,
      type,
    });

    const response = await fetch(`${API_BASE}/search/suggestions?${searchParams}`);
    
    if (!response.ok) {
      throw new Error('Failed to get search suggestions');
    }
    
    return response.json();
  },

  // Save search query to history
  async saveSearchQuery(query: string, filters: SearchFilters = {}): Promise<void> {
    try {
      await fetch(`${API_BASE}/search/history`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, filters }),
      });
    } catch (error) {
      // Silently fail - search history is not critical
      console.warn('Failed to save search query to history:', error);
    }
  },

  // Get search history
  async getSearchHistory(limit: number = 10): Promise<ApiResponse<Array<{
    query: string;
    filters: SearchFilters;
    timestamp: Date;
    resultCount: number;
  }>>> {
    const response = await fetch(`${API_BASE}/search/history?limit=${limit}`);
    
    if (!response.ok) {
      throw new Error('Failed to get search history');
    }
    
    return response.json();
  },

  // Clear search history
  async clearSearchHistory(): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/search/history`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to clear search history');
    }
    
    return response.json();
  },

  // Highlight search terms in text
  highlightSearchTerms(text: string, query: string): {
    highlightedText: string;
    highlights: number[];
  } {
    if (!query.trim()) {
      return { highlightedText: text, highlights: [] };
    }

    const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);
    const highlights: number[] = [];
    let highlightedText = text;
    let offset = 0;

    searchTerms.forEach(term => {
      const regex = new RegExp(`(${term})`, 'gi');
      const matches = Array.from(text.matchAll(regex));
      
      matches.forEach(match => {
        if (match.index !== undefined) {
          highlights.push(match.index + offset);
        }
      });
    });

    // Sort highlights by position
    highlights.sort((a, b) => a - b);

    return { highlightedText, highlights };
  },

  // Create search snippet from message content
  createSearchSnippet(content: string, query: string, maxLength: number = 150): {
    snippet: string;
    highlights: number[];
  } {
    if (!query.trim()) {
      return {
        snippet: content.length > maxLength ? content.substring(0, maxLength) + '...' : content,
        highlights: [],
      };
    }

    const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);
    const lowerContent = content.toLowerCase();
    
    // Find the first occurrence of any search term
    let firstMatchIndex = -1;
    for (const term of searchTerms) {
      const index = lowerContent.indexOf(term);
      if (index !== -1 && (firstMatchIndex === -1 || index < firstMatchIndex)) {
        firstMatchIndex = index;
      }
    }

    let snippet: string;
    let snippetStart = 0;

    if (firstMatchIndex !== -1) {
      // Center the snippet around the first match
      const contextLength = Math.floor((maxLength - query.length) / 2);
      snippetStart = Math.max(0, firstMatchIndex - contextLength);
      const snippetEnd = Math.min(content.length, snippetStart + maxLength);
      snippet = content.substring(snippetStart, snippetEnd);
      
      if (snippetStart > 0) snippet = '...' + snippet;
      if (snippetEnd < content.length) snippet = snippet + '...';
    } else {
      // No match found, just truncate from the beginning
      snippet = content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
    }

    // Calculate highlight positions relative to the snippet
    const { highlights } = this.highlightSearchTerms(snippet, query);

    return { snippet, highlights };
  },
};

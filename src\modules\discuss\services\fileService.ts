// File service for handling file uploads and management
import type { ApiResponse, Attachment } from '../types';

const API_BASE = '/api/discuss';

export interface UploadFileRequest {
  file: File;
  channelId?: string;
  messageId?: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface FileUploadResponse {
  attachment: Attachment;
  url: string;
}

export const fileService = {
  // Upload a single file
  async uploadFile(
    request: UploadFileRequest,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<ApiResponse<FileUploadResponse>> {
    const formData = new FormData();
    formData.append('file', request.file);
    
    if (request.channelId) {
      formData.append('channelId', request.channelId);
    }
    
    if (request.messageId) {
      formData.append('messageId', request.messageId);
    }

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress: UploadProgress = {
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100),
            };
            onProgress(progress);
          }
        });
      }

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Failed to parse response'));
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'));
      });

      xhr.open('POST', `${API_BASE}/files/upload`);
      xhr.send(formData);
    });
  },

  // Upload multiple files
  async uploadFiles(
    files: File[],
    channelId?: string,
    messageId?: string,
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): Promise<ApiResponse<FileUploadResponse[]>> {
    const uploadPromises = files.map((file, index) =>
      this.uploadFile(
        { file, channelId, messageId },
        onProgress ? (progress) => onProgress(index, progress) : undefined
      )
    );

    try {
      const results = await Promise.all(uploadPromises);
      const successfulUploads = results.filter(result => result.success);
      const failedUploads = results.filter(result => !result.success);

      if (failedUploads.length > 0) {
        console.warn(`${failedUploads.length} file uploads failed`);
      }

      return {
        success: successfulUploads.length > 0,
        data: successfulUploads.map(result => result.data!),
        message: `${successfulUploads.length}/${files.length} files uploaded successfully`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  },

  // Get file by ID
  async getFile(fileId: string): Promise<ApiResponse<Attachment>> {
    const response = await fetch(`${API_BASE}/files/${fileId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch file');
    }
    
    return response.json();
  },

  // Delete file
  async deleteFile(fileId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/files/${fileId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete file');
    }
    
    return response.json();
  },

  // Get file download URL
  getFileUrl(fileId: string): string {
    return `${API_BASE}/files/${fileId}/download`;
  },

  // Get file thumbnail URL (for images/videos)
  getThumbnailUrl(fileId: string, size: 'small' | 'medium' | 'large' = 'medium'): string {
    return `${API_BASE}/files/${fileId}/thumbnail?size=${size}`;
  },

  // Validate file before upload
  validateFile(file: File, options: {
    maxSize?: number;
    allowedTypes?: string[];
    allowedExtensions?: string[];
  } = {}): { valid: boolean; error?: string } {
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedTypes = [],
      allowedExtensions = [],
    } = options;

    // Check file size
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds ${(maxSize / 1024 / 1024).toFixed(1)}MB limit`,
      };
    }

    // Check file type
    if (allowedTypes.length > 0 && !allowedTypes.some(type => file.type.startsWith(type))) {
      return {
        valid: false,
        error: `File type ${file.type} is not allowed`,
      };
    }

    // Check file extension
    if (allowedExtensions.length > 0) {
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (!extension || !allowedExtensions.includes(extension)) {
        return {
          valid: false,
          error: `File extension .${extension} is not allowed`,
        };
      }
    }

    return { valid: true };
  },

  // Get file type category
  getFileCategory(file: File): 'image' | 'video' | 'audio' | 'document' | 'other' {
    if (file.type.startsWith('image/')) return 'image';
    if (file.type.startsWith('video/')) return 'video';
    if (file.type.startsWith('audio/')) return 'audio';
    if (
      file.type.includes('pdf') ||
      file.type.includes('document') ||
      file.type.includes('text') ||
      file.type.includes('spreadsheet') ||
      file.type.includes('presentation')
    ) {
      return 'document';
    }
    return 'other';
  },

  // Format file size for display
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Check if file is an image
  isImage(file: File): boolean {
    return file.type.startsWith('image/');
  },

  // Check if file is a video
  isVideo(file: File): boolean {
    return file.type.startsWith('video/');
  },

  // Check if file is audio
  isAudio(file: File): boolean {
    return file.type.startsWith('audio/');
  },

  // Generate file preview URL (for images)
  generatePreviewUrl(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.isImage(file)) {
        reject(new Error('File is not an image'));
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      reader.readAsDataURL(file);
    });
  },
};

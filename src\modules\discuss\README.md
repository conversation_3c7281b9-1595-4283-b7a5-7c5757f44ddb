# Discuss Module

A comprehensive team communication and collaboration module for the Nexed Web ERP system. This module provides real-time messaging, file sharing, voice/video calls, and advanced collaboration features.

## 🏗️ Architecture

The discuss module is organized as a self-contained module with its own components, services, types, and utilities:

```
src/modules/discuss/
├── DiscussModule.tsx          # Main module component
├── index.ts                   # Module exports
├── components/                # React components
│   ├── layout/               # Layout components (Sidebar, DiscussLayout)
│   ├── views/                # Main view components (Channels, DMs, Teams, Settings)
│   ├── core/                 # Core messaging components (Message, MessageList, MessageInput)
│   └── ui/                   # Discuss-specific UI components (Search, Presence, FileUpload, etc.)
├── services/                 # API services and business logic
├── types/                    # TypeScript type definitions
├── hooks/                    # Custom React hooks (useMessages, useWebSocket, useNotifications)
├── utils/                    # Utility functions
└── __tests__/                # Comprehensive test suite
```

## 🚀 Features Implemented

### ✅ Basic Architecture
- [x] Module structure and organization
- [x] Special discuss component (replaces DynamicAppView for discuss app)
- [x] Routing integration with existing app structure
- [x] TypeScript type definitions
- [x] Mock data and MSW handlers

### ✅ Core Views
- [x] **Channels View**: Team channel interface with message display
- [x] **Direct Messages View**: Private 1:1 conversations
- [x] **Teams View**: Team management and member overview
- [x] **Settings View**: Notification and privacy settings

### ✅ Layout Components
- [x] **DiscussLayout**: Main layout with sidebar and content area
- [x] **Sidebar**: Collapsible navigation with channels, DMs, and teams
- [x] **Responsive Design**: Works on desktop, tablet, and mobile

### ✅ Mock Data & API
- [x] Comprehensive mock data for users, channels, messages, teams
- [x] MSW handlers for all discuss API endpoints
- [x] Real-time simulation capabilities
- [x] Advanced search endpoints with filters
- [x] Notification and presence management APIs

### ✅ User Experience Enhancements
- [x] Dark & light mode support
- [x] Responsive design for all screen sizes
- [x] Multiple view modes (comfortable, cozy, compact)
- [x] Quick switcher for fast navigation
- [x] Customizable channel organization with folders
- [x] Advanced message search with filters and suggestions

### ✅ Testing & Documentation
- [x] Comprehensive test suite for all components
- [x] Hook testing with realistic scenarios
- [x] Service layer testing with MSW
- [x] Complete TypeScript type coverage
- [x] Detailed documentation and usage examples

## 🎯 Features Roadmap

### ✅ Core Messaging Infrastructure
- [x] Real-time messaging with WebSocket integration
- [x] Message components (Message, MessageList, MessageInput)
- [x] Threaded conversations
- [x] Message editing and deletion
- [x] Message state management
- [x] Message reactions and emojis
- [x] Delivery status and read receipts

### ✅ Rich Content & File Sharing
- [x] Rich text formatting (bold, italic, code, links)
- [x] File upload and sharing (images, docs, audio, video)
- [x] Image/video preview and gallery
- [x] Message search functionality with advanced filters
- [x] Content rendering components
- [x] File preview with fullscreen support

### ✅ User Presence & Interaction
- [x] Real-time presence indicators (online/offline/busy)
- [x] Typing indicators
- [x] User mentions (@username) with autocomplete
- [x] Emoji reactions and custom emojis
- [x] Message pinning functionality
- [x] User status management

### ✅ Notifications System
- [x] Desktop & mobile push notifications
- [x] Custom notification sounds
- [x] Mute/unmute channels and conversations
- [x] Smart notifications (mentions only)
- [x] Read receipts and delivery status
- [x] Notification preferences management

#### Voice/Video Integration
- [ ] Instant voice and video calls
- [ ] Screen sharing capabilities
- [ ] Meeting recording and playback
- [ ] Conference room integration
- [ ] Call history and logs

#### Security & Access Control
- [ ] Role-based channel permissions
- [ ] End-to-end encryption for sensitive conversations
- [ ] Audit logs for compliance
- [ ] Content moderation tools
- [ ] Message retention policies

#### Collaboration Features
- [ ] Polls and surveys
- [ ] Task creation from messages
- [ ] CRM/ERP record linking
- [ ] Shared notes and collaborative documents
- [ ] Code snippet sharing with syntax highlighting

#### User Experience Enhancements
- [ ] Dark/light mode integration
- [ ] Compact vs comfortable view modes
- [ ] Quick switcher for navigation
- [ ] Customizable channel organization
- [ ] Keyboard shortcuts

#### Automation & Bots
- [ ] Chatbot framework
- [ ] FAQ automation
- [ ] AI assistant integration
- [ ] Scheduled messages
- [ ] Workflow triggers

#### Archival & Compliance
- [ ] Searchable message archive
- [ ] Data retention policies
- [ ] Conversation export
- [ ] Legal hold support
- [ ] GDPR compliance tools

#### External Integrations
- [ ] Slack/Teams bridge
- [ ] WhatsApp Business integration
- [ ] Email notifications
- [ ] Calendar integration
- [ ] Third-party app webhooks

## 🛠️ Development

### Getting Started

The discuss module is automatically loaded when navigating to the discuss app (menu ID 10):

```typescript
// Navigate to discuss module
window.location.href = '/app?menu=10&view=channels';
```

### Adding New Components

1. Create component in appropriate directory:
   ```typescript
   // src/modules/discuss/components/core/Message.tsx
   export const Message: React.FC<MessageProps> = ({ message }) => {
     // Component implementation
   };
   ```

2. Export from index file:
   ```typescript
   // src/modules/discuss/components/core/index.ts
   export { Message } from './Message';
   ```

3. Use in views or other components:
   ```typescript
   import { Message } from '../core';
   ```

### Adding New Services

1. Create service file:
   ```typescript
   // src/modules/discuss/services/messageService.ts
   export const messageService = {
     sendMessage: async (message: CreateMessageRequest) => {
       // Service implementation
     }
   };
   ```

2. Export from services index
3. Use in components via custom hooks

### Mock Data

Mock data is organized in `src/mocks/data/discuss.ts` and includes:
- Users with presence information
- Channels (public/private)
- Messages with reactions and attachments
- Direct message conversations
- Teams and team memberships
- Notification settings

### API Endpoints

All discuss API endpoints are mocked using MSW:
- `GET /api/discuss/channels` - Get all channels
- `POST /api/discuss/messages` - Send a message
- `GET /api/discuss/users` - Get all users
- `PUT /api/discuss/presence` - Update user presence
- And many more...

## 🧪 Testing

### Running Tests
```bash
# Run all discuss module tests
npm test -- --testPathPattern=discuss

# Run specific component tests
npm test DiscussModule.test.tsx
```

### Storybook
```bash
# Start Storybook to view components
npm run storybook
```

Navigate to "Modules/Discuss" to see all discuss components.

## 📱 Usage Examples

### Basic Navigation
```typescript
// Navigate to channels
<Link to="/app?menu=10&view=channels">Channels</Link>

// Navigate to direct messages
<Link to="/app?menu=10&view=messages">Direct Messages</Link>

// Navigate to teams
<Link to="/app?menu=10&view=teams">Teams</Link>
```

### Using Discuss Components
```typescript
import { DiscussModule } from '../modules/discuss';

// Use the complete discuss module
<DiscussModule />

// Or use individual components
import { ChannelsView, Sidebar } from '../modules/discuss/components';
```

## 🎨 Design System

The discuss module follows the existing Nexed Web design system:
- Uses theme colors from `useThemeStore`
- Responsive design with Tailwind CSS
- Consistent spacing and typography
- Accessible components with proper ARIA labels

## 🔧 Configuration

The discuss module can be configured through:
- User notification settings
- Channel-specific settings
- Team permissions
- Integration configurations

## 📄 License

Part of the Nexed Web ERP system. All rights reserved.

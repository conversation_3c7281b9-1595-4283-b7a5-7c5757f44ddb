// Mock data generator for discuss module
import type { User, Message, Channel, Team } from '../../modules/discuss/types';

// Sample data for generating realistic content
const sampleNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>\'<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
];

const sampleChannelNames = [
  'general', 'development', 'design', 'marketing', 'sales', 'support',
  'random', 'announcements', 'frontend', 'backend', 'mobile', 'qa',
  'product', 'engineering', 'hr', 'finance', 'legal', 'operations',
];

const sampleTeamNames = [
  'Frontend Team', 'Backend Team', 'Design Team', 'Marketing Team',
  'Sales Team', 'Support Team', 'Product Team', 'Engineering Team',
  'QA Team', 'DevOps Team', 'Data Team', 'Security Team',
];

const sampleMessageContent = [
  'Hey everyone! How\'s the project going?',
  'Just pushed the latest changes to the dev branch.',
  'Can someone review my pull request?',
  'Great work on the new feature! 🎉',
  'I found a bug in the user authentication flow.',
  'Let\'s schedule a meeting to discuss the roadmap.',
  'The design looks amazing! Love the new color scheme.',
  'Has anyone tested the mobile version yet?',
  'I\'ll be working from home today.',
  'Don\'t forget about the team standup at 10 AM.',
  'The client feedback is very positive!',
  'We need to update the documentation.',
  'I\'m having trouble with the API integration.',
  'Coffee break anyone? ☕',
  'The deployment went smoothly!',
  'Can we add this to the next sprint?',
  'I\'ll handle the code review.',
  'The performance improvements are working well.',
  'Let\'s celebrate the successful launch! 🚀',
  'I need help with the database migration.',
];

const sampleEmojis = ['👍', '❤️', '😂', '😮', '😢', '🎉', '🚀', '💯', '🔥', '👏'];

// Generate random users
export function generateUsers(count: number): User[] {
  const users: User[] = [];
  const statuses: User['status'][] = ['online', 'offline', 'away', 'busy'];

  for (let i = 0; i < count; i++) {
    const name = sampleNames[i % sampleNames.length];
    const initials = name.split(' ').map(n => n[0]).join('');
    
    users.push({
      id: `user-${i + 1}`,
      name: `${name} ${i > sampleNames.length - 1 ? i - sampleNames.length + 1 : ''}`.trim(),
      email: `${name.toLowerCase().replace(' ', '.')}@company.com`,
      avatar: initials,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      lastSeen: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000), // Random time in last 24h
    });
  }

  return users;
}

// Generate random channels
export function generateChannels(count: number, users: User[]): Channel[] {
  const channels: Channel[] = [];
  const types: Channel['type'][] = ['public', 'private'];

  for (let i = 0; i < count; i++) {
    const name = sampleChannelNames[i % sampleChannelNames.length];
    const type = types[Math.floor(Math.random() * types.length)];
    const memberCount = Math.floor(Math.random() * users.length) + 1;
    const memberIds = users
      .sort(() => 0.5 - Math.random())
      .slice(0, memberCount)
      .map(u => u.id);

    channels.push({
      id: `channel-${name}-${i}`,
      name: i > sampleChannelNames.length - 1 ? `${name}-${i - sampleChannelNames.length + 1}` : name,
      description: `${type === 'public' ? 'Public' : 'Private'} channel for ${name} discussions`,
      type,
      memberIds,
      createdBy: memberIds[0],
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random time in last 30 days
      lastActivity: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000), // Random time in last 24h
      isArchived: Math.random() < 0.1, // 10% chance of being archived
      settings: {
        notifications: Math.random() > 0.2, // 80% have notifications enabled
        allowFileUploads: Math.random() > 0.1, // 90% allow file uploads
        allowExternalLinks: Math.random() > 0.3, // 70% allow external links
      },
    });
  }

  return channels;
}

// Generate random teams
export function generateTeams(count: number, users: User[], channels: Channel[]): Team[] {
  const teams: Team[] = [];

  for (let i = 0; i < count; i++) {
    const name = sampleTeamNames[i % sampleTeamNames.length];
    const memberCount = Math.floor(Math.random() * users.length * 0.5) + 2; // 2 to 50% of users
    const memberIds = users
      .sort(() => 0.5 - Math.random())
      .slice(0, memberCount)
      .map(u => u.id);

    const teamChannelCount = Math.floor(Math.random() * 3) + 1; // 1-3 channels per team
    const channelIds = channels
      .sort(() => 0.5 - Math.random())
      .slice(0, teamChannelCount)
      .map(c => c.id);

    teams.push({
      id: `team-${i + 1}`,
      name: i > sampleTeamNames.length - 1 ? `${name} ${i - sampleTeamNames.length + 1}` : name,
      description: `${name} responsible for various aspects of the project`,
      memberIds,
      channelIds,
      createdBy: memberIds[0],
      createdAt: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000), // Random time in last 60 days
      settings: {
        visibility: Math.random() > 0.3 ? 'public' : 'private', // 70% public
        joinPolicy: Math.random() > 0.5 ? 'open' : 'invite-only', // 50% open
        allowMemberInvites: Math.random() > 0.2, // 80% allow member invites
      },
    });
  }

  return teams;
}

// Generate random messages
export function generateMessages(
  count: number, 
  users: User[], 
  channels: Channel[]
): Message[] {
  const messages: Message[] = [];

  for (let i = 0; i < count; i++) {
    const isDirectMessage = Math.random() < 0.2; // 20% are direct messages
    const channelId = isDirectMessage ? undefined : channels[Math.floor(Math.random() * channels.length)].id;
    const authorId = users[Math.floor(Math.random() * users.length)].id;
    const content = sampleMessageContent[Math.floor(Math.random() * sampleMessageContent.length)];
    
    // Generate random timestamp within last 7 days
    const timestamp = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
    
    // Generate reactions (30% chance of having reactions)
    const reactions = [];
    if (Math.random() < 0.3) {
      const reactionCount = Math.floor(Math.random() * 3) + 1; // 1-3 different reactions
      for (let j = 0; j < reactionCount; j++) {
        const emoji = sampleEmojis[Math.floor(Math.random() * sampleEmojis.length)];
        const userCount = Math.floor(Math.random() * 5) + 1; // 1-5 users per reaction
        const userIds = users
          .sort(() => 0.5 - Math.random())
          .slice(0, userCount)
          .map(u => u.id);
        
        reactions.push({
          emoji,
          userIds,
          count: userIds.length,
        });
      }
    }

    // Generate attachments (10% chance)
    const attachments = [];
    if (Math.random() < 0.1) {
      const attachmentTypes = ['image', 'document', 'video', 'audio'] as const;
      const type = attachmentTypes[Math.floor(Math.random() * attachmentTypes.length)];
      const extensions = {
        image: ['jpg', 'png', 'gif'],
        document: ['pdf', 'docx', 'txt'],
        video: ['mp4', 'avi', 'mov'],
        audio: ['mp3', 'wav', 'ogg'],
      };
      const ext = extensions[type][Math.floor(Math.random() * extensions[type].length)];
      
      attachments.push({
        id: `att-${i + 1}`,
        name: `file-${i + 1}.${ext}`,
        type,
        url: `/mock-files/file-${i + 1}.${ext}`,
        size: Math.floor(Math.random() * 10000000) + 1000, // 1KB to 10MB
        mimeType: `${type}/${ext}`,
      });
    }

    // Generate mentions (15% chance)
    const mentions = [];
    if (Math.random() < 0.15) {
      const mentionCount = Math.floor(Math.random() * 2) + 1; // 1-2 mentions
      const mentionedUsers = users
        .sort(() => 0.5 - Math.random())
        .slice(0, mentionCount)
        .map(u => u.id);
      mentions.push(...mentionedUsers);
    }

    messages.push({
      id: `msg-${i + 1}`,
      content,
      authorId,
      channelId,
      timestamp,
      editedAt: Math.random() < 0.05 ? new Date(timestamp.getTime() + Math.random() * 60 * 60 * 1000) : undefined, // 5% edited
      reactions,
      attachments,
      mentions,
      isDeleted: Math.random() < 0.02, // 2% deleted
      deliveryStatus: Math.random() < 0.8 ? 'read' : Math.random() < 0.9 ? 'delivered' : 'sent',
    });
  }

  // Sort messages by timestamp
  return messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
}

// Generate complete mock dataset
export function generateCompleteDataset(options: {
  userCount?: number;
  channelCount?: number;
  teamCount?: number;
  messageCount?: number;
} = {}) {
  const {
    userCount = 15,
    channelCount = 12,
    teamCount = 6,
    messageCount = 100,
  } = options;

  const users = generateUsers(userCount);
  const channels = generateChannels(channelCount, users);
  const teams = generateTeams(teamCount, users, channels);
  const messages = generateMessages(messageCount, users, channels);

  return {
    users,
    channels,
    teams,
    messages,
  };
}

// Export individual generators
export {
  sampleNames,
  sampleChannelNames,
  sampleTeamNames,
  sampleMessageContent,
  sampleEmojis,
};

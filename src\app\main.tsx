import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import '../index.css';
import { AppRouter } from '../router';
import { SWRProvider } from '../providers/SWRProvider';
import { ErrorBoundary } from '../components/common';
import { initializeGlobalErrorHandler } from '../utils/globalErrorHandler';
import { useThemeStore } from '../stores/themeStore';
import { initializeAppRegistrations } from '../registry';

// Start MSW in development mode
async function enableMocking() {
  if (import.meta.env.DEV) {
    try {
      const { startWorker } = await import('../mocks/browser');
      await startWorker();
    } catch (error) {
      console.error('Failed to start MSW worker:', error);
      // Continue anyway - don't block app startup
    }
  }
}

console.log('Initializing application...');

// Initialize global error handling
initializeGlobalErrorHandler({
  enableConsoleLogging: import.meta.env.DEV,
  enableReporting: true,
  enableUserNotification: true,
  maxErrorsPerSession: 20,
});

// Initialize theme store
useThemeStore.getState().initialize({
  defaultTheme: 'system',
  enableSystemDetection: true,
  enableCSSVariables: true,
});

// Initialize app component registrations
initializeAppRegistrations();

// Start the app immediately, don't wait for MSW
console.log('Starting React application with router and SWR...');
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ErrorBoundary
      level="page"
      componentName="Application"
      enableAutoRecovery={true}
      enableReporting={true}
    >
      <SWRProvider>
        <AppRouter />
      </SWRProvider>
    </ErrorBoundary>
  </StrictMode>
);
console.log('React application rendered successfully');

// Start MSW in the background
enableMocking().catch(error => {
  console.error('MSW initialization failed:', error);
});

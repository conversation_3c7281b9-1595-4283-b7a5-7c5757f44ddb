import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { User, PresenceInfo } from '../../types';

export interface UserPresenceProps {
  user: User;
  presence?: PresenceInfo;
  showStatus?: boolean;
  showLastSeen?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  'data-testid'?: string;
}

export const UserPresence: React.FC<UserPresenceProps> = ({
  user,
  presence,
  showStatus = true,
  showLastSeen = false,
  size = 'medium',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    small: 'w-6 h-6 text-xs',
    medium: 'w-8 h-8 text-sm',
    large: 'w-12 h-12 text-base',
  };

  const statusSizes = {
    small: 'w-2 h-2',
    medium: 'w-3 h-3',
    large: 'w-4 h-4',
  };

  const getStatusColor = (status: User['status']) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'busy':
        return 'bg-red-500';
      case 'offline':
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: User['status']) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'away':
        return 'Away';
      case 'busy':
        return 'Busy';
      case 'offline':
      default:
        return 'Offline';
    }
  };

  const formatLastSeen = (lastSeen?: Date) => {
    if (!lastSeen) return 'Never';
    
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return lastSeen.toLocaleDateString();
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`} data-testid={testId}>
      {/* Avatar with Status Indicator */}
      <div className="relative">
        <div
          className={`${sizeClasses[size]} rounded-full flex items-center justify-center text-white font-semibold`}
          style={{ backgroundColor: colors.primary }}
        >
          {user.avatar || user.name.charAt(0).toUpperCase()}
        </div>
        
        {/* Status Indicator */}
        <div
          className={`absolute -bottom-0.5 -right-0.5 ${statusSizes[size]} rounded-full border-2 border-white ${getStatusColor(user.status)}`}
        />
      </div>

      {/* User Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <span className="font-medium truncate" style={{ color: colors.text }}>
            {user.name}
          </span>
          
          {showStatus && (
            <span
              className="text-xs px-2 py-0.5 rounded-full"
              style={{
                backgroundColor: `${colors.primary}20`,
                color: colors.primary,
              }}
            >
              {getStatusText(user.status)}
            </span>
          )}
        </div>
        
        {showLastSeen && user.lastSeen && (
          <p className="text-xs" style={{ color: colors.textSecondary }}>
            Last seen {formatLastSeen(user.lastSeen)}
          </p>
        )}
        
        {presence?.isTyping && (
          <p className="text-xs italic" style={{ color: colors.primary }}>
            Typing...
          </p>
        )}
      </div>
    </div>
  );
};

export interface UserAvatarProps {
  user: User;
  size?: 'xs' | 'small' | 'medium' | 'large' | 'xl';
  showStatus?: boolean;
  onClick?: () => void;
  className?: string;
  'data-testid'?: string;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'medium',
  showStatus = true,
  onClick,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const sizeClasses = {
    xs: 'w-4 h-4 text-xs',
    small: 'w-6 h-6 text-xs',
    medium: 'w-8 h-8 text-sm',
    large: 'w-12 h-12 text-base',
    xl: 'w-16 h-16 text-lg',
  };

  const statusSizes = {
    xs: 'w-1 h-1',
    small: 'w-2 h-2',
    medium: 'w-3 h-3',
    large: 'w-4 h-4',
    xl: 'w-5 h-5',
  };

  const getStatusColor = (status: User['status']) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'busy':
        return 'bg-red-500';
      case 'offline':
      default:
        return 'bg-gray-400';
    }
  };

  return (
    <div
      className={`relative ${onClick ? 'cursor-pointer' : ''} ${className}`}
      onClick={onClick}
      data-testid={testId}
    >
      <div
        className={`${sizeClasses[size]} rounded-full flex items-center justify-center text-white font-semibold transition-transform ${
          onClick ? 'hover:scale-105' : ''
        }`}
        style={{ backgroundColor: colors.primary }}
        title={user.name}
      >
        {user.avatar || user.name.charAt(0).toUpperCase()}
      </div>
      
      {showStatus && (
        <div
          className={`absolute -bottom-0.5 -right-0.5 ${statusSizes[size]} rounded-full border-2 border-white ${getStatusColor(user.status)}`}
        />
      )}
    </div>
  );
};

export interface TypingIndicatorProps {
  users: User[];
  className?: string;
  'data-testid'?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  users,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  if (users.length === 0) return null;

  const getTypingText = () => {
    if (users.length === 1) {
      return `${users[0].name} is typing...`;
    } else if (users.length === 2) {
      return `${users[0].name} and ${users[1].name} are typing...`;
    } else {
      return `${users[0].name} and ${users.length - 1} others are typing...`;
    }
  };

  return (
    <div
      className={`flex items-center space-x-2 px-4 py-2 ${className}`}
      data-testid={testId}
    >
      {/* Typing Animation */}
      <div className="flex space-x-1">
        <div
          className="w-2 h-2 rounded-full animate-bounce"
          style={{ backgroundColor: colors.textSecondary, animationDelay: '0ms' }}
        />
        <div
          className="w-2 h-2 rounded-full animate-bounce"
          style={{ backgroundColor: colors.textSecondary, animationDelay: '150ms' }}
        />
        <div
          className="w-2 h-2 rounded-full animate-bounce"
          style={{ backgroundColor: colors.textSecondary, animationDelay: '300ms' }}
        />
      </div>
      
      {/* Typing Text */}
      <span className="text-sm italic" style={{ color: colors.textSecondary }}>
        {getTypingText()}
      </span>
    </div>
  );
};

export interface OnlineUsersListProps {
  users: User[];
  onUserClick?: (user: User) => void;
  maxVisible?: number;
  className?: string;
  'data-testid'?: string;
}

export const OnlineUsersList: React.FC<OnlineUsersListProps> = ({
  users,
  onUserClick,
  maxVisible = 10,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  
  const onlineUsers = users.filter(user => user.status === 'online');
  const visibleUsers = onlineUsers.slice(0, maxVisible);
  const hiddenCount = onlineUsers.length - maxVisible;

  if (onlineUsers.length === 0) return null;

  return (
    <div className={`space-y-2 ${className}`} data-testid={testId}>
      <h3 className="text-sm font-medium" style={{ color: colors.text }}>
        Online ({onlineUsers.length})
      </h3>
      
      <div className="space-y-1">
        {visibleUsers.map((user) => (
          <button
            key={user.id}
            onClick={() => onUserClick?.(user)}
            className="w-full text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <UserPresence user={user} size="small" showStatus={false} />
          </button>
        ))}
        
        {hiddenCount > 0 && (
          <div className="text-xs px-2 py-1" style={{ color: colors.textSecondary }}>
            +{hiddenCount} more online
          </div>
        )}
      </div>
    </div>
  );
};

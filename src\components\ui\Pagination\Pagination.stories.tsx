import type { Meta, StoryObj } from '@storybook/react-vite';
import { Pagination } from './Pagination';
import { useThemeStore } from '../../../stores/themeStore';
import { useEffect } from 'react';

const meta: Meta<typeof Pagination> = {
  title: 'UI/Pagination',
  component: Pagination,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A pagination component with previous/next buttons and current range display.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    currentRange: {
      control: 'text',
      description: 'Current page range display text',
    },
    onNext: {
      action: 'next',
      description: 'Callback when next button is clicked',
    },
    onPrev: {
      action: 'prev', 
      description: 'Callback when previous button is clicked',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const Template = (args: any) => {
  const { colors, setTheme } = useThemeStore();

  useEffect(() => {
    setTheme('dark');
  }, [setTheme]);

  return (
    <div
      className="p-8"
      style={{ backgroundColor: colors.background }}
    >
      <Pagination {...args} />
    </div>
  );
};

export const Default: Story = {
  render: Template,
  args: {
    currentRange: '1-20 of 150',
    onNext: () => console.log('Next clicked'),
    onPrev: () => console.log('Previous clicked'),
  },
};

export const FirstPage: Story = {
  render: Template,
  args: {
    currentRange: '1-20 of 150',
    onNext: () => console.log('Next clicked'),
    onPrev: () => console.log('Previous clicked'),
  },
};

export const MiddlePage: Story = {
  render: Template,
  args: {
    currentRange: '21-40 of 150',
    onNext: () => console.log('Next clicked'),
    onPrev: () => console.log('Previous clicked'),
  },
};

export const LastPage: Story = {
  render: Template,
  args: {
    currentRange: '141-150 of 150',
    onNext: () => console.log('Next clicked'),
    onPrev: () => console.log('Previous clicked'),
  },
};

export const LargeDataset: Story = {
  render: Template,
  args: {
    currentRange: '1,001-1,020 of 10,000',
    onNext: () => console.log('Next clicked'),
    onPrev: () => console.log('Previous clicked'),
  },
};

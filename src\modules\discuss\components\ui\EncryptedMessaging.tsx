import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface EncryptionSettings {
  enabled: boolean;
  algorithm: 'AES-256' | 'RSA-2048' | 'ChaCha20';
  keyRotationInterval: number; // in days
  requireEncryption: boolean;
  allowPlaintext: boolean;
}

export interface EncryptionKey {
  id: string;
  algorithm: string;
  createdAt: Date;
  expiresAt: Date;
  status: 'active' | 'expired' | 'revoked';
  fingerprint: string;
}

export interface EncryptedMessagingProps {
  channelId?: string;
  userId: string;
  onSettingsChange?: (settings: EncryptionSettings) => void;
  className?: string;
  'data-testid'?: string;
}

export const EncryptedMessaging: React.FC<EncryptedMessagingProps> = ({
  channelId,
  userId,
  onSettingsChange,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [settings, setSettings] = useState<EncryptionSettings>({
    enabled: false,
    algorithm: 'AES-256',
    keyRotationInterval: 30,
    requireEncryption: false,
    allowPlaintext: true,
  });
  const [keys, setKeys] = useState<EncryptionKey[]>([]);
  const [isGeneratingKey, setIsGeneratingKey] = useState(false);
  const [showKeyDetails, setShowKeyDetails] = useState<string | null>(null);

  // Load encryption settings and keys
  useEffect(() => {
    const loadEncryptionData = async () => {
      try {
        // Mock data - in real app, this would come from API
        const mockKeys: EncryptionKey[] = [
          {
            id: 'key-1',
            algorithm: 'AES-256',
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
            expiresAt: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000), // 23 days from now
            status: 'active',
            fingerprint: 'SHA256:nThbg6kXUpJWGl7E1IGOCspRomTxdCARLviKw6E5SY8',
          },
          {
            id: 'key-2',
            algorithm: 'AES-256',
            createdAt: new Date(Date.now() - 37 * 24 * 60 * 60 * 1000), // 37 days ago
            expiresAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago (expired)
            status: 'expired',
            fingerprint: 'SHA256:kXUpJWGl7E1IGOCspRomTxdCARLviKw6E5SY8nThbg6',
          },
        ];
        
        setKeys(mockKeys);
      } catch (error) {
        console.error('Failed to load encryption data:', error);
      }
    };

    loadEncryptionData();
  }, [channelId, userId]);

  const handleSettingChange = (key: keyof EncryptionSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onSettingsChange?.(newSettings);
  };

  const generateNewKey = async () => {
    setIsGeneratingKey(true);
    try {
      // Simulate key generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newKey: EncryptionKey = {
        id: `key-${Date.now()}`,
        algorithm: settings.algorithm,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + settings.keyRotationInterval * 24 * 60 * 60 * 1000),
        status: 'active',
        fingerprint: `SHA256:${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
      };
      
      // Mark previous keys as expired
      setKeys(prev => [
        newKey,
        ...prev.map(key => ({ ...key, status: 'expired' as const }))
      ]);
    } catch (error) {
      console.error('Failed to generate key:', error);
    } finally {
      setIsGeneratingKey(false);
    }
  };

  const revokeKey = (keyId: string) => {
    setKeys(prev => 
      prev.map(key => 
        key.id === keyId 
          ? { ...key, status: 'revoked' }
          : key
      )
    );
  };

  const getKeyStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10B981';
      case 'expired': return '#F59E0B';
      case 'revoked': return '#EF4444';
      default: return colors.textSecondary;
    }
  };

  const formatFingerprint = (fingerprint: string) => {
    return fingerprint.replace(/(.{2})/g, '$1:').slice(0, -1);
  };

  const activeKey = keys.find(key => key.status === 'active');

  return (
    <div className={`${className}`} data-testid={testId}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
            Encrypted Messaging
          </h3>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Configure end-to-end encryption for secure communications
          </p>
        </div>

        {/* Encryption Status */}
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border" style={{ borderColor: colors.border }}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${settings.enabled ? 'bg-green-500' : 'bg-gray-400'}`} />
              <h4 className="font-medium" style={{ color: colors.text }}>
                Encryption Status
              </h4>
            </div>
            <span className={`px-2 py-1 text-xs rounded-full ${
              settings.enabled 
                ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
            }`}>
              {settings.enabled ? 'ENABLED' : 'DISABLED'}
            </span>
          </div>
          
          <div className="space-y-3">
            <label className="flex items-center justify-between cursor-pointer">
              <span style={{ color: colors.text }}>Enable end-to-end encryption</span>
              <input
                type="checkbox"
                checked={settings.enabled}
                onChange={(e) => handleSettingChange('enabled', e.target.checked)}
                className="sr-only"
              />
              <div className={`relative w-10 h-6 rounded-full transition-colors ${
                settings.enabled ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
              }`}>
                <div className={`absolute top-1 left-1 w-4 h-4 bg-white rounded-full transition-transform ${
                  settings.enabled ? 'translate-x-4' : 'translate-x-0'
                }`} />
              </div>
            </label>

            {settings.enabled && (
              <>
                <label className="flex items-center justify-between cursor-pointer">
                  <span style={{ color: colors.text }}>Require encryption for all messages</span>
                  <input
                    type="checkbox"
                    checked={settings.requireEncryption}
                    onChange={(e) => handleSettingChange('requireEncryption', e.target.checked)}
                    className="sr-only"
                  />
                  <div className={`relative w-10 h-6 rounded-full transition-colors ${
                    settings.requireEncryption ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
                  }`}>
                    <div className={`absolute top-1 left-1 w-4 h-4 bg-white rounded-full transition-transform ${
                      settings.requireEncryption ? 'translate-x-4' : 'translate-x-0'
                    }`} />
                  </div>
                </label>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1" style={{ color: colors.text }}>
                      Encryption Algorithm
                    </label>
                    <select
                      value={settings.algorithm}
                      onChange={(e) => handleSettingChange('algorithm', e.target.value)}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                      style={{ 
                        borderColor: colors.border,
                        backgroundColor: colors.background,
                        color: colors.text 
                      }}
                    >
                      <option value="AES-256">AES-256</option>
                      <option value="RSA-2048">RSA-2048</option>
                      <option value="ChaCha20">ChaCha20</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1" style={{ color: colors.text }}>
                      Key Rotation (days)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="365"
                      value={settings.keyRotationInterval}
                      onChange={(e) => handleSettingChange('keyRotationInterval', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                      style={{ 
                        borderColor: colors.border,
                        backgroundColor: colors.background,
                        color: colors.text 
                      }}
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Encryption Keys */}
        {settings.enabled && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium" style={{ color: colors.text }}>
                Encryption Keys
              </h4>
              <button
                onClick={generateNewKey}
                disabled={isGeneratingKey}
                className={`px-4 py-2 text-sm rounded-lg transition-colors ${
                  isGeneratingKey
                    ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
              >
                {isGeneratingKey ? 'Generating...' : '🔑 Generate New Key'}
              </button>
            </div>

            <div className="space-y-3">
              {keys.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-4xl mb-2">🔐</div>
                  <p className="text-lg font-medium mb-1" style={{ color: colors.text }}>
                    No encryption keys
                  </p>
                  <p className="text-sm" style={{ color: colors.textSecondary }}>
                    Generate your first encryption key to start secure messaging
                  </p>
                </div>
              ) : (
                keys.map(key => (
                  <div
                    key={key.id}
                    className="p-4 bg-white dark:bg-gray-800 rounded-lg border"
                    style={{ borderColor: colors.border }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">🔑</div>
                        <div>
                          <h5 className="font-medium" style={{ color: colors.text }}>
                            {key.algorithm} Key
                          </h5>
                          <p className="text-sm" style={{ color: colors.textSecondary }}>
                            Created {key.createdAt.toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <span
                          className="px-2 py-1 text-xs rounded-full text-white"
                          style={{ backgroundColor: getKeyStatusColor(key.status) }}
                        >
                          {key.status.toUpperCase()}
                        </span>
                        
                        {key.status === 'active' && (
                          <button
                            onClick={() => revokeKey(key.id)}
                            className="px-2 py-1 text-xs text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                          >
                            Revoke
                          </button>
                        )}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium" style={{ color: colors.text }}>
                          Fingerprint:
                        </span>
                        <button
                          onClick={() => setShowKeyDetails(showKeyDetails === key.id ? null : key.id)}
                          className="text-sm text-blue-500 hover:text-blue-600"
                        >
                          {showKeyDetails === key.id ? 'Hide' : 'Show'}
                        </button>
                      </div>
                      
                      {showKeyDetails === key.id && (
                        <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs font-mono break-all">
                          {formatFingerprint(key.fingerprint)}
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between text-sm" style={{ color: colors.textSecondary }}>
                        <span>Expires: {key.expiresAt.toLocaleDateString()}</span>
                        {key.status === 'active' && (
                          <span className="text-green-600 dark:text-green-400">
                            ✓ Currently in use
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}

        {/* Security Notice */}
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className="text-blue-500 text-xl">🛡️</div>
            <div>
              <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                Security Information
              </h5>
              <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <p>• Messages are encrypted end-to-end using industry-standard algorithms</p>
                <p>• Encryption keys are automatically rotated based on your settings</p>
                <p>• Only participants in the conversation can decrypt messages</p>
                <p>• Server administrators cannot read encrypted content</p>
              </div>
            </div>
          </div>
        </div>

        {/* Current Session Info */}
        {activeKey && (
          <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="text-green-500 text-xl">🔒</div>
              <div>
                <h5 className="font-medium text-green-900 dark:text-green-100 mb-1">
                  Secure Session Active
                </h5>
                <p className="text-sm text-green-800 dark:text-green-200">
                  Your messages are protected with {activeKey.algorithm} encryption
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

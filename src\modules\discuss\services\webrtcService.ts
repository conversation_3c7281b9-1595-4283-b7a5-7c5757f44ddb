// WebRTC service for peer-to-peer voice/video communication
import Peer from 'simple-peer';
import type { Instance } from 'simple-peer';

export interface WebRTCConfig {
  iceServers: RTCIceServer[];
  enableVideo: boolean;
  enableAudio: boolean;
}

export interface PeerConnection {
  id: string;
  peer: Instance;
  userId: string;
  stream?: MediaStream;
  isInitiator: boolean;
}

export interface MediaConstraints {
  audio: boolean | MediaTrackConstraints;
  video: boolean | MediaTrackConstraints;
}

export interface ScreenShareOptions {
  audio: boolean;
  video: boolean;
  displaySurface?: 'monitor' | 'window' | 'application' | 'browser';
}

export type WebRTCEventType = 
  | 'peer-connected'
  | 'peer-disconnected'
  | 'stream-received'
  | 'stream-removed'
  | 'data-received'
  | 'error';

export type WebRTCEventHandler = (data: any) => void;

class WebRTCService {
  private peers: Map<string, PeerConnection> = new Map();
  private localStream: MediaStream | null = null;
  private screenStream: MediaStream | null = null;
  private eventHandlers: Map<WebRTCEventType, WebRTCEventHandler[]> = new Map();
  private config: WebRTCConfig;

  constructor() {
    this.config = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
      enableVideo: true,
      enableAudio: true,
    };
  }

  // Initialize local media stream
  async initializeMedia(constraints: MediaConstraints = { audio: true, video: true }): Promise<MediaStream> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      return this.localStream;
    } catch (error) {
      console.error('Failed to get user media:', error);
      throw new Error('Failed to access camera/microphone');
    }
  }

  // Start screen sharing
  async startScreenShare(options: ScreenShareOptions = { audio: false, video: true }): Promise<MediaStream> {
    try {
      const constraints: DisplayMediaStreamConstraints = {
        video: options.video,
        audio: options.audio,
      };

      this.screenStream = await navigator.mediaDevices.getDisplayMedia(constraints);
      
      // Replace video track in all peer connections
      if (this.screenStream.getVideoTracks().length > 0) {
        const videoTrack = this.screenStream.getVideoTracks()[0];
        
        this.peers.forEach((connection) => {
          const sender = connection.peer._pc?.getSenders().find(
            s => s.track && s.track.kind === 'video'
          );
          if (sender) {
            sender.replaceTrack(videoTrack);
          }
        });

        // Handle screen share end
        videoTrack.onended = () => {
          this.stopScreenShare();
        };
      }

      return this.screenStream;
    } catch (error) {
      console.error('Failed to start screen sharing:', error);
      throw new Error('Failed to start screen sharing');
    }
  }

  // Stop screen sharing
  async stopScreenShare(): Promise<void> {
    if (this.screenStream) {
      this.screenStream.getTracks().forEach(track => track.stop());
      this.screenStream = null;

      // Restore camera video if available
      if (this.localStream && this.localStream.getVideoTracks().length > 0) {
        const videoTrack = this.localStream.getVideoTracks()[0];
        
        this.peers.forEach((connection) => {
          const sender = connection.peer._pc?.getSenders().find(
            s => s.track && s.track.kind === 'video'
          );
          if (sender) {
            sender.replaceTrack(videoTrack);
          }
        });
      }
    }
  }

  // Create a new peer connection
  createPeer(userId: string, isInitiator: boolean, stream?: MediaStream): PeerConnection {
    const peerId = `${userId}-${Date.now()}`;
    
    const peer = new Peer({
      initiator: isInitiator,
      trickle: false,
      stream: stream || this.localStream || undefined,
      config: {
        iceServers: this.config.iceServers,
      },
    });

    const connection: PeerConnection = {
      id: peerId,
      peer,
      userId,
      isInitiator,
      stream,
    };

    // Set up peer event handlers
    peer.on('signal', (data) => {
      this.emit('peer-signal', { peerId, userId, signal: data });
    });

    peer.on('connect', () => {
      this.emit('peer-connected', { peerId, userId });
    });

    peer.on('stream', (remoteStream) => {
      connection.stream = remoteStream;
      this.emit('stream-received', { peerId, userId, stream: remoteStream });
    });

    peer.on('data', (data) => {
      this.emit('data-received', { peerId, userId, data });
    });

    peer.on('close', () => {
      this.peers.delete(peerId);
      this.emit('peer-disconnected', { peerId, userId });
    });

    peer.on('error', (error) => {
      console.error('Peer error:', error);
      this.emit('error', { peerId, userId, error });
    });

    this.peers.set(peerId, connection);
    return connection;
  }

  // Signal a peer with offer/answer/ice candidate
  signalPeer(peerId: string, signal: any): void {
    const connection = this.peers.get(peerId);
    if (connection) {
      connection.peer.signal(signal);
    }
  }

  // Send data to a specific peer
  sendData(peerId: string, data: any): void {
    const connection = this.peers.get(peerId);
    if (connection && connection.peer.connected) {
      connection.peer.send(JSON.stringify(data));
    }
  }

  // Send data to all connected peers
  broadcastData(data: any): void {
    this.peers.forEach((connection) => {
      if (connection.peer.connected) {
        connection.peer.send(JSON.stringify(data));
      }
    });
  }

  // Mute/unmute local audio
  toggleAudio(enabled: boolean): void {
    if (this.localStream) {
      this.localStream.getAudioTracks().forEach(track => {
        track.enabled = enabled;
      });
    }
  }

  // Enable/disable local video
  toggleVideo(enabled: boolean): void {
    if (this.localStream) {
      this.localStream.getVideoTracks().forEach(track => {
        track.enabled = enabled;
      });
    }
  }

  // Get local stream
  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  // Get screen share stream
  getScreenStream(): MediaStream | null {
    return this.screenStream;
  }

  // Get all peer connections
  getPeers(): Map<string, PeerConnection> {
    return this.peers;
  }

  // Disconnect a specific peer
  disconnectPeer(peerId: string): void {
    const connection = this.peers.get(peerId);
    if (connection) {
      connection.peer.destroy();
      this.peers.delete(peerId);
    }
  }

  // Disconnect all peers and cleanup
  disconnect(): void {
    this.peers.forEach((connection) => {
      connection.peer.destroy();
    });
    this.peers.clear();

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.screenStream) {
      this.screenStream.getTracks().forEach(track => track.stop());
      this.screenStream = null;
    }
  }

  // Event handling
  on(type: WebRTCEventType, handler: WebRTCEventHandler): void {
    if (!this.eventHandlers.has(type)) {
      this.eventHandlers.set(type, []);
    }
    this.eventHandlers.get(type)!.push(handler);
  }

  off(type: WebRTCEventType, handler: WebRTCEventHandler): void {
    const handlers = this.eventHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emit(type: WebRTCEventType, data: any): void {
    const handlers = this.eventHandlers.get(type);
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }
}

// Export singleton instance
export const webrtcService = new WebRTCService();

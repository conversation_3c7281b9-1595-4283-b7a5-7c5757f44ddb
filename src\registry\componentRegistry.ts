import React from 'react';

/**
 * Component Registry for App-Specific Components
 * 
 * This registry allows apps to use custom components instead of the default DynamicAppView.
 * Each app can register a specific component that will be used when the app is accessed.
 * 
 * Usage:
 * 1. Register a component: componentRegistry.register('10', DiscussModule)
 * 2. Get a component: componentRegistry.get('10')
 * 3. Check if registered: componentRegistry.has('10')
 */

export interface AppComponentProps {
  className?: string;
  'data-testid'?: string;
}

export type AppComponent = React.ComponentType<AppComponentProps>;

class ComponentRegistry {
  private registry = new Map<string, AppComponent>();

  /**
   * Register a component for a specific app ID
   * @param appId - The app ID (menu ID)
   * @param component - The React component to use for this app
   */
  register(appId: string, component: AppComponent): void {
    this.registry.set(appId, component);
    console.log(`Component registered for app ID: ${appId}`);
  }

  /**
   * Get the registered component for an app ID
   * @param appId - The app ID (menu ID)
   * @returns The registered component or undefined if not found
   */
  get(appId: string): AppComponent | undefined {
    return this.registry.get(appId);
  }

  /**
   * Check if a component is registered for an app ID
   * @param appId - The app ID (menu ID)
   * @returns True if a component is registered, false otherwise
   */
  has(appId: string): boolean {
    return this.registry.has(appId);
  }

  /**
   * Unregister a component for an app ID
   * @param appId - The app ID (menu ID)
   * @returns True if the component was removed, false if it wasn't registered
   */
  unregister(appId: string): boolean {
    const result = this.registry.delete(appId);
    if (result) {
      console.log(`Component unregistered for app ID: ${appId}`);
    }
    return result;
  }

  /**
   * Get all registered app IDs
   * @returns Array of registered app IDs
   */
  getRegisteredAppIds(): string[] {
    return Array.from(this.registry.keys());
  }

  /**
   * Clear all registered components
   */
  clear(): void {
    this.registry.clear();
    console.log('Component registry cleared');
  }

  /**
   * Get the number of registered components
   * @returns Number of registered components
   */
  size(): number {
    return this.registry.size;
  }
}

// Create and export a singleton instance
export const componentRegistry = new ComponentRegistry();

// Export the class for testing purposes
export { ComponentRegistry };

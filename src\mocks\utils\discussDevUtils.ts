// Development utilities for discuss module mock data
import { generateCompleteDataset } from './discussDataGenerator';
import type { User, Message, Channel, Team } from '../../modules/discuss/types';

// Store original mock data for reset functionality
let originalMockData: {
  users: User[];
  channels: Channel[];
  teams: Team[];
  messages: Message[];
} | null = null;

// Current mock data (mutable for testing)
export let currentMockData = {
  users: [] as User[],
  channels: [] as Channel[],
  teams: [] as Team[],
  messages: [] as Message[],
};

// Initialize mock data
export function initializeMockData(options?: {
  userCount?: number;
  channelCount?: number;
  teamCount?: number;
  messageCount?: number;
  useGenerated?: boolean;
}) {
  const { useGenerated = false, ...generateOptions } = options || {};

  if (useGenerated) {
    // Generate new mock data
    const generated = generateCompleteDataset(generateOptions);
    currentMockData = { ...generated };
  } else {
    // Use predefined mock data from discuss.ts
    const { mockUsers, mockChannels, mockTeams, mockMessages } = require('../data/discuss');
    currentMockData = {
      users: [...mockUsers],
      channels: [...mockChannels],
      teams: [...mockTeams],
      messages: [...mockMessages],
    };
  }

  // Store original data for reset
  if (!originalMockData) {
    originalMockData = {
      users: [...currentMockData.users],
      channels: [...currentMockData.channels],
      teams: [...currentMockData.teams],
      messages: [...currentMockData.messages],
    };
  }

  console.log('Mock data initialized:', {
    users: currentMockData.users.length,
    channels: currentMockData.channels.length,
    teams: currentMockData.teams.length,
    messages: currentMockData.messages.length,
  });
}

// Reset mock data to original state
export function resetMockData() {
  if (originalMockData) {
    currentMockData = {
      users: [...originalMockData.users],
      channels: [...originalMockData.channels],
      teams: [...originalMockData.teams],
      messages: [...originalMockData.messages],
    };
    console.log('Mock data reset to original state');
  }
}

// Add new mock data
export function addMockUser(user: Omit<User, 'id'>): User {
  const newUser: User = {
    ...user,
    id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  };
  currentMockData.users.push(newUser);
  return newUser;
}

export function addMockChannel(channel: Omit<Channel, 'id' | 'createdAt' | 'lastActivity'>): Channel {
  const newChannel: Channel = {
    ...channel,
    id: `channel-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    createdAt: new Date(),
    lastActivity: new Date(),
  };
  currentMockData.channels.push(newChannel);
  return newChannel;
}

export function addMockMessage(message: Omit<Message, 'id' | 'timestamp'>): Message {
  const newMessage: Message = {
    ...message,
    id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date(),
  };
  currentMockData.messages.push(newMessage);
  return newMessage;
}

export function addMockTeam(team: Omit<Team, 'id' | 'createdAt'>): Team {
  const newTeam: Team = {
    ...team,
    id: `team-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    createdAt: new Date(),
  };
  currentMockData.teams.push(newTeam);
  return newTeam;
}

// Update mock data
export function updateMockUser(userId: string, updates: Partial<User>): User | null {
  const userIndex = currentMockData.users.findIndex(u => u.id === userId);
  if (userIndex === -1) return null;

  currentMockData.users[userIndex] = { ...currentMockData.users[userIndex], ...updates };
  return currentMockData.users[userIndex];
}

export function updateMockChannel(channelId: string, updates: Partial<Channel>): Channel | null {
  const channelIndex = currentMockData.channels.findIndex(c => c.id === channelId);
  if (channelIndex === -1) return null;

  currentMockData.channels[channelIndex] = { ...currentMockData.channels[channelIndex], ...updates };
  return currentMockData.channels[channelIndex];
}

export function updateMockMessage(messageId: string, updates: Partial<Message>): Message | null {
  const messageIndex = currentMockData.messages.findIndex(m => m.id === messageId);
  if (messageIndex === -1) return null;

  currentMockData.messages[messageIndex] = { ...currentMockData.messages[messageIndex], ...updates };
  return currentMockData.messages[messageIndex];
}

// Delete mock data
export function deleteMockUser(userId: string): boolean {
  const userIndex = currentMockData.users.findIndex(u => u.id === userId);
  if (userIndex === -1) return false;

  currentMockData.users.splice(userIndex, 1);
  return true;
}

export function deleteMockChannel(channelId: string): boolean {
  const channelIndex = currentMockData.channels.findIndex(c => c.id === channelId);
  if (channelIndex === -1) return false;

  currentMockData.channels.splice(channelIndex, 1);
  return true;
}

export function deleteMockMessage(messageId: string): boolean {
  const messageIndex = currentMockData.messages.findIndex(m => m.id === messageId);
  if (messageIndex === -1) return false;

  currentMockData.messages.splice(messageIndex, 1);
  return true;
}

// Query mock data
export function getMockUserById(id: string): User | undefined {
  return currentMockData.users.find(user => user.id === id);
}

export function getMockChannelById(id: string): Channel | undefined {
  return currentMockData.channels.find(channel => channel.id === id);
}

export function getMockMessagesByChannelId(channelId: string): Message[] {
  return currentMockData.messages.filter(message => message.channelId === channelId);
}

export function getMockDirectMessagesByUserId(userId: string): Message[] {
  return currentMockData.messages.filter(message => 
    !message.channelId && (
      message.authorId === userId || 
      message.mentions?.includes(userId)
    )
  );
}

export function getMockTeamById(id: string): Team | undefined {
  return currentMockData.teams.find(team => team.id === id);
}

// Statistics and analytics
export function getMockDataStats() {
  const now = new Date();
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  const stats = {
    users: {
      total: currentMockData.users.length,
      online: currentMockData.users.filter(u => u.status === 'online').length,
      offline: currentMockData.users.filter(u => u.status === 'offline').length,
      away: currentMockData.users.filter(u => u.status === 'away').length,
      busy: currentMockData.users.filter(u => u.status === 'busy').length,
    },
    channels: {
      total: currentMockData.channels.length,
      public: currentMockData.channels.filter(c => c.type === 'public').length,
      private: currentMockData.channels.filter(c => c.type === 'private').length,
      archived: currentMockData.channels.filter(c => c.isArchived).length,
    },
    messages: {
      total: currentMockData.messages.length,
      today: currentMockData.messages.filter(m => m.timestamp > oneDayAgo).length,
      thisWeek: currentMockData.messages.filter(m => m.timestamp > oneWeekAgo).length,
      withAttachments: currentMockData.messages.filter(m => m.attachments.length > 0).length,
      withReactions: currentMockData.messages.filter(m => m.reactions.length > 0).length,
      deleted: currentMockData.messages.filter(m => m.isDeleted).length,
    },
    teams: {
      total: currentMockData.teams.length,
      public: currentMockData.teams.filter(t => t.settings.visibility === 'public').length,
      private: currentMockData.teams.filter(t => t.settings.visibility === 'private').length,
    },
  };

  return stats;
}

// Development helpers
export function logMockDataStats() {
  const stats = getMockDataStats();
  console.table(stats);
}

export function exportMockData() {
  return {
    timestamp: new Date().toISOString(),
    data: currentMockData,
    stats: getMockDataStats(),
  };
}

export function importMockData(data: typeof currentMockData) {
  currentMockData = { ...data };
  console.log('Mock data imported successfully');
}

// Initialize on module load
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  // Only initialize in browser and development mode
  initializeMockData();
  
  // Expose utilities to window for debugging
  (window as any).discussDevUtils = {
    initializeMockData,
    resetMockData,
    addMockUser,
    addMockChannel,
    addMockMessage,
    addMockTeam,
    updateMockUser,
    updateMockChannel,
    updateMockMessage,
    deleteMockUser,
    deleteMockChannel,
    deleteMockMessage,
    getMockDataStats,
    logMockDataStats,
    exportMockData,
    importMockData,
    currentMockData,
  };
  
  console.log('Discuss dev utils available at window.discussDevUtils');
}

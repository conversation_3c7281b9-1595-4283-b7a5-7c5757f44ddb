import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FilterDropdown } from './FilterDropdown';
import { vi } from 'vitest';

// Mock the theme store
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#2563eb',
      secondary: '#4f46e5',
      accent: '#7c3aed',
      warning: '#d97706',
      background: '#ffffff',
      surface: '#f9fafb',
      border: '#e5e7eb',
      text: '#111827',
      textSecondary: '#6b7280',
      textMuted: '#9ca3af',
      mutedForeground: '#64748b',
      hover: '#f1f5f9',
      shadow: 'rgba(0, 0, 0, 0.1)',
      error: '#ef4444',
    },
  }),
}));

const mockFilterItems = [
  { id: 'status', label: 'Status', selected: true },
  { id: 'priority', label: 'Priority', selected: false },
];

const mockGroupByItems = [
  { id: 'customer', label: 'Customer' },
  { id: 'salesperson', label: 'Salesperson' },
];

const mockFavoriteItems = [
  { id: 'fav1', label: 'My Favorites', selected: true },
  { id: 'fav2', label: 'Shared Favorites', selected: false },
];

describe('FilterDropdown', () => {
  const defaultProps = {
    isOpen: true,
    filterItems: mockFilterItems,
    groupByItems: mockGroupByItems,
    favoriteItems: mockFavoriteItems,
    onFilterSelect: vi.fn(),
    onGroupBySelect: vi.fn(),
    onFavoriteSelect: vi.fn(),
    onFavoriteDelete: vi.fn(),
    onAddCustomFilter: vi.fn(),
    onAddCustomGroup: vi.fn(),
    onSaveCurrentSearch: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('does not render when isOpen is false', () => {
    render(<FilterDropdown {...defaultProps} isOpen={false} />);
    expect(screen.queryByText('Filters')).not.toBeInTheDocument();
  });

  it('renders all sections when open', () => {
    render(<FilterDropdown {...defaultProps} />);
    
    expect(screen.getByText('Filters')).toBeInTheDocument();
    expect(screen.getByText('Group By')).toBeInTheDocument();
    expect(screen.getByText('Favorites')).toBeInTheDocument();
  });

  it('renders filter items correctly', () => {
    render(<FilterDropdown {...defaultProps} />);
    
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Priority')).toBeInTheDocument();
  });

  it('calls onFilterSelect when filter item is clicked', async () => {
    const user = userEvent.setup();
    render(<FilterDropdown {...defaultProps} />);

    await user.click(screen.getByText('Priority'));
    expect(defaultProps.onFilterSelect).toHaveBeenCalledWith('priority');
  });

  it('calls onGroupBySelect when group by item is clicked', async () => {
    const user = userEvent.setup();
    render(<FilterDropdown {...defaultProps} />);

    await user.click(screen.getByText('Customer'));
    expect(defaultProps.onGroupBySelect).toHaveBeenCalledWith('customer');
  });

  it('calls onFavoriteSelect when favorite item is clicked', async () => {
    const user = userEvent.setup();
    render(<FilterDropdown {...defaultProps} />);

    await user.click(screen.getByText('My Favorites'));
    expect(defaultProps.onFavoriteSelect).toHaveBeenCalledWith('fav1');
  });

  it('calls onFavoriteDelete when delete button is clicked', async () => {
    const user = userEvent.setup();
    render(<FilterDropdown {...defaultProps} />);

    // Find the favorite item container and hover to show delete button
    const favoriteItem = screen.getByText('My Favorites').closest('li');
    expect(favoriteItem).toBeInTheDocument();
    
    // The delete button should be present but might be hidden
    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(btn => 
      btn.getAttribute('type') === 'button' && 
      btn !== screen.getByText('My Favorites') &&
      btn !== screen.getByText('Shared Favorites')
    );
    
    if (deleteButton) {
      await user.click(deleteButton);
      expect(defaultProps.onFavoriteDelete).toHaveBeenCalled();
    }
  });

  it('filters items based on search query', () => {
    render(<FilterDropdown {...defaultProps} searchQuery="stat" />);
    
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.queryByText('Priority')).not.toBeInTheDocument();
  });

  it('calls action buttons when clicked', async () => {
    const user = userEvent.setup();
    render(<FilterDropdown {...defaultProps} />);

    await user.click(screen.getByText('Add Custom Filter'));
    expect(defaultProps.onAddCustomFilter).toHaveBeenCalled();

    await user.click(screen.getByText('Add Custom Group'));
    expect(defaultProps.onAddCustomGroup).toHaveBeenCalled();

    await user.click(screen.getByText('Save current search'));
    expect(defaultProps.onSaveCurrentSearch).toHaveBeenCalled();
  });

  it('applies compact styling when compact prop is true', () => {
    render(<FilterDropdown {...defaultProps} compact={true} data-testid="dropdown" />);
    
    const dropdown = screen.getByTestId('dropdown');
    expect(dropdown).toBeInTheDocument();
    // The component should render with compact dimensions
  });

  it('renders with custom className', () => {
    render(<FilterDropdown {...defaultProps} className="custom-class" data-testid="dropdown" />);
    
    const dropdown = screen.getByTestId('dropdown');
    expect(dropdown).toHaveClass('custom-class');
  });
});

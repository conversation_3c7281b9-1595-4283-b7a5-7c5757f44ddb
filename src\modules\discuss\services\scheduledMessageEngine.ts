// Enhanced scheduled message engine with advanced scheduling and templating
import type { 
  ScheduledMessage, 
  RecurrenceConfig, 
  MessageTemplate,
  TemplateVariable,
  Message,
  User,
  Channel
} from '../types';
import { messageService } from './messageService';

export interface ScheduledMessageRequest {
  content: string;
  channelId: string;
  authorId: string;
  scheduledFor: Date;
  recurring?: RecurrenceConfig;
  templateId?: string;
  templateVariables?: Record<string, any>;
  conditions?: ScheduleCondition[];
  timezone?: string;
}

export interface ScheduleCondition {
  type: 'user_online' | 'channel_active' | 'time_window' | 'custom';
  config: Record<string, any>;
}

export interface ScheduleExecution {
  id: string;
  messageId: string;
  scheduledFor: Date;
  actualSentAt?: Date;
  status: 'pending' | 'sent' | 'failed' | 'skipped' | 'cancelled';
  error?: string;
  retryCount: number;
  nextRetry?: Date;
}

export class ScheduledMessageEngine {
  private scheduledMessages: Map<string, ScheduledMessage> = new Map();
  private executions: Map<string, ScheduleExecution> = new Map();
  private templates: Map<string, MessageTemplate> = new Map();
  private processingInterval: NodeJS.Timeout | null = null;
  private isProcessing = false;

  constructor() {
    this.startScheduleProcessor();
  }

  // Create a new scheduled message
  async createScheduledMessage(request: ScheduledMessageRequest): Promise<ScheduledMessage> {
    const id = this.generateId();
    const now = new Date();

    // Process template if specified
    let content = request.content;
    if (request.templateId && request.templateVariables) {
      content = await this.processTemplate(request.templateId, request.templateVariables);
    }

    const scheduledMessage: ScheduledMessage = {
      id,
      content,
      channelId: request.channelId,
      authorId: request.authorId,
      scheduledFor: request.scheduledFor,
      status: 'pending',
      recurring: request.recurring,
      createdAt: now
    };

    this.scheduledMessages.set(id, scheduledMessage);

    // Create initial execution
    if (request.recurring) {
      await this.createRecurringExecutions(scheduledMessage);
    } else {
      await this.createSingleExecution(scheduledMessage);
    }

    return scheduledMessage;
  }

  // Update a scheduled message
  async updateScheduledMessage(id: string, updates: Partial<ScheduledMessage>): Promise<ScheduledMessage | null> {
    const message = this.scheduledMessages.get(id);
    if (!message) return null;

    const updatedMessage = { ...message, ...updates };
    this.scheduledMessages.set(id, updatedMessage);

    // Update executions if schedule changed
    if (updates.scheduledFor || updates.recurring) {
      await this.updateExecutions(updatedMessage);
    }

    return updatedMessage;
  }

  // Cancel a scheduled message
  async cancelScheduledMessage(id: string): Promise<boolean> {
    const message = this.scheduledMessages.get(id);
    if (!message) return false;

    message.status = 'cancelled';
    this.scheduledMessages.set(id, message);

    // Cancel all pending executions
    for (const [execId, execution] of this.executions) {
      if (execution.messageId === id && execution.status === 'pending') {
        execution.status = 'cancelled';
        this.executions.set(execId, execution);
      }
    }

    return true;
  }

  // Get scheduled messages
  getScheduledMessages(filters?: {
    status?: ScheduledMessage['status'];
    channelId?: string;
    authorId?: string;
  }): ScheduledMessage[] {
    let messages = Array.from(this.scheduledMessages.values());

    if (filters) {
      if (filters.status) {
        messages = messages.filter(m => m.status === filters.status);
      }
      if (filters.channelId) {
        messages = messages.filter(m => m.channelId === filters.channelId);
      }
      if (filters.authorId) {
        messages = messages.filter(m => m.authorId === filters.authorId);
      }
    }

    return messages.sort((a, b) => a.scheduledFor.getTime() - b.scheduledFor.getTime());
  }

  // Get executions for a message
  getExecutions(messageId: string): ScheduleExecution[] {
    return Array.from(this.executions.values())
      .filter(e => e.messageId === messageId)
      .sort((a, b) => a.scheduledFor.getTime() - b.scheduledFor.getTime());
  }

  // Process template with variables
  private async processTemplate(templateId: string, variables: Record<string, any>): Promise<string> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    let content = template.content;

    // Replace template variables
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      content = content.replace(regex, String(value));
    }

    // Process built-in variables
    content = await this.processBuiltInVariables(content);

    return content;
  }

  // Process built-in template variables
  private async processBuiltInVariables(content: string): Promise<string> {
    const now = new Date();
    
    // Date/time variables
    content = content.replace(/{{now}}/g, now.toISOString());
    content = content.replace(/{{date}}/g, now.toDateString());
    content = content.replace(/{{time}}/g, now.toTimeString());
    content = content.replace(/{{timestamp}}/g, now.getTime().toString());

    // Random variables
    content = content.replace(/{{random_number}}/g, Math.floor(Math.random() * 1000).toString());
    content = content.replace(/{{uuid}}/g, this.generateId());

    return content;
  }

  // Create single execution for non-recurring message
  private async createSingleExecution(message: ScheduledMessage): Promise<void> {
    const execution: ScheduleExecution = {
      id: this.generateId(),
      messageId: message.id,
      scheduledFor: message.scheduledFor,
      status: 'pending',
      retryCount: 0
    };

    this.executions.set(execution.id, execution);
  }

  // Create recurring executions
  private async createRecurringExecutions(message: ScheduledMessage): Promise<void> {
    if (!message.recurring) return;

    const executions = this.calculateRecurringDates(message.scheduledFor, message.recurring);
    
    for (const date of executions) {
      const execution: ScheduleExecution = {
        id: this.generateId(),
        messageId: message.id,
        scheduledFor: date,
        status: 'pending',
        retryCount: 0
      };

      this.executions.set(execution.id, execution);
    }
  }

  // Calculate recurring dates
  private calculateRecurringDates(startDate: Date, config: RecurrenceConfig): Date[] {
    const dates: Date[] = [];
    let currentDate = new Date(startDate);
    const maxDates = config.maxOccurrences || 100; // Limit to prevent infinite loops
    const endDate = config.endDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year default

    for (let i = 0; i < maxDates && currentDate <= endDate; i++) {
      dates.push(new Date(currentDate));

      switch (config.type) {
        case 'daily':
          currentDate.setDate(currentDate.getDate() + config.interval);
          break;
        case 'weekly':
          currentDate.setDate(currentDate.getDate() + (7 * config.interval));
          break;
        case 'monthly':
          currentDate.setMonth(currentDate.getMonth() + config.interval);
          break;
        case 'yearly':
          currentDate.setFullYear(currentDate.getFullYear() + config.interval);
          break;
        case 'cron':
          // For cron expressions, would need a cron parser library
          // For now, fallback to daily
          currentDate.setDate(currentDate.getDate() + 1);
          break;
        default:
          // Custom interval in days
          currentDate.setDate(currentDate.getDate() + config.interval);
      }

      // Skip weekends if configured
      if (config.skipWeekends) {
        while (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // Filter by days of week if specified
      if (config.daysOfWeek && config.daysOfWeek.length > 0) {
        while (!config.daysOfWeek.includes(currentDate.getDay())) {
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // Filter by days of month if specified
      if (config.daysOfMonth && config.daysOfMonth.length > 0) {
        while (!config.daysOfMonth.includes(currentDate.getDate())) {
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }
    }

    return dates;
  }

  // Update executions when message is modified
  private async updateExecutions(message: ScheduledMessage): Promise<void> {
    // Remove old pending executions
    for (const [execId, execution] of this.executions) {
      if (execution.messageId === message.id && execution.status === 'pending') {
        this.executions.delete(execId);
      }
    }

    // Create new executions
    if (message.recurring) {
      await this.createRecurringExecutions(message);
    } else {
      await this.createSingleExecution(message);
    }
  }

  // Start the schedule processor
  private startScheduleProcessor(): void {
    this.processingInterval = setInterval(async () => {
      if (!this.isProcessing) {
        await this.processScheduledMessages();
      }
    }, 30000); // Check every 30 seconds
  }

  // Process scheduled messages that are due
  private async processScheduledMessages(): Promise<void> {
    this.isProcessing = true;

    try {
      const now = new Date();
      const dueExecutions = Array.from(this.executions.values())
        .filter(e => e.status === 'pending' && e.scheduledFor <= now)
        .sort((a, b) => a.scheduledFor.getTime() - b.scheduledFor.getTime());

      for (const execution of dueExecutions) {
        await this.executeScheduledMessage(execution);
      }
    } catch (error) {
      console.error('Error processing scheduled messages:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  // Execute a scheduled message
  private async executeScheduledMessage(execution: ScheduleExecution): Promise<void> {
    const message = this.scheduledMessages.get(execution.messageId);
    if (!message) {
      execution.status = 'failed';
      execution.error = 'Message not found';
      this.executions.set(execution.id, execution);
      return;
    }

    try {
      // Check conditions before sending
      const shouldSend = await this.checkScheduleConditions(message);
      if (!shouldSend) {
        execution.status = 'skipped';
        execution.actualSentAt = new Date();
        this.executions.set(execution.id, execution);
        return;
      }

      // Send the message
      const response = await messageService.sendMessage({
        content: message.content,
        channelId: message.channelId
      });

      if (response.success) {
        execution.status = 'sent';
        execution.actualSentAt = new Date();
        
        // Update message status if this was the last execution
        if (!message.recurring || this.isLastExecution(message.id)) {
          message.status = 'sent';
          message.sentAt = new Date();
          this.scheduledMessages.set(message.id, message);
        }
      } else {
        throw new Error(response.error || 'Failed to send message');
      }
    } catch (error) {
      execution.status = 'failed';
      execution.error = error instanceof Error ? error.message : 'Unknown error';
      execution.retryCount++;

      // Schedule retry if under retry limit
      if (execution.retryCount < 3) {
        execution.status = 'pending';
        execution.nextRetry = new Date(Date.now() + (execution.retryCount * 60000)); // Exponential backoff
        execution.scheduledFor = execution.nextRetry;
      } else {
        message.status = 'failed';
        message.error = execution.error;
        this.scheduledMessages.set(message.id, message);
      }
    }

    this.executions.set(execution.id, execution);
  }

  // Check if conditions are met for sending
  private async checkScheduleConditions(message: ScheduledMessage): Promise<boolean> {
    // For now, always return true
    // In a real implementation, check user online status, channel activity, etc.
    return true;
  }

  // Check if this is the last execution for a recurring message
  private isLastExecution(messageId: string): boolean {
    const pendingExecutions = Array.from(this.executions.values())
      .filter(e => e.messageId === messageId && e.status === 'pending');
    
    return pendingExecutions.length === 0;
  }

  // Generate unique ID
  private generateId(): string {
    return `sched_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Stop the schedule processor
  stop(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
  }

  // Load templates
  loadTemplates(templates: MessageTemplate[]): void {
    for (const template of templates) {
      this.templates.set(template.id, template);
    }
  }

  // Get template by ID
  getTemplate(id: string): MessageTemplate | undefined {
    return this.templates.get(id);
  }

  // Get all templates
  getTemplates(): MessageTemplate[] {
    return Array.from(this.templates.values());
  }
}

// Export singleton instance
export const scheduledMessageEngine = new ScheduledMessageEngine();

import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { faqService } from '../../services';
import type { FAQEntry, FAQCategory } from '../../types';

export interface FAQManagementProps {
  className?: string;
  'data-testid'?: string;
}

export const FAQManagement: React.FC<FAQManagementProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [entries, setEntries] = useState<FAQEntry[]>([]);
  const [categories, setCategories] = useState<FAQCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedEntry, setSelectedEntry] = useState<FAQEntry | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [entriesResponse, categoriesResponse] = await Promise.all([
        faqService.getFAQEntries(),
        faqService.getCategories(),
      ]);
      setEntries(entriesResponse.data || []);
      setCategories(categoriesResponse.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load FAQ data');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredEntries = entries.filter(entry => {
    const matchesCategory = selectedCategory === 'all' || entry.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      entry.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const handleCreateEntry = () => {
    setSelectedEntry({
      id: '',
      question: '',
      answer: '',
      keywords: [],
      category: selectedCategory !== 'all' ? selectedCategory : categories[0]?.id || '',
      tags: [],
      confidence: 0,
      usage: 0,
      createdBy: '1', // Current user ID
      createdAt: new Date(),
      isActive: true,
      relatedEntries: [],
      metadata: {},
    });
    setIsEditing(true);
  };

  const handleEditEntry = (entry: FAQEntry) => {
    setSelectedEntry(entry);
    setIsEditing(true);
  };

  const handleSaveEntry = async (entry: FAQEntry) => {
    try {
      if (entry.id) {
        // Update existing entry
        await faqService.updateFAQEntry(entry.id, {
          question: entry.question,
          answer: entry.answer,
          keywords: entry.keywords,
          category: entry.category,
          tags: entry.tags,
          isActive: entry.isActive,
          metadata: entry.metadata,
        });
        setEntries(prev => prev.map(e => e.id === entry.id ? entry : e));
      } else {
        // Create new entry
        const response = await faqService.createFAQEntry({
          question: entry.question,
          answer: entry.answer,
          keywords: entry.keywords,
          category: entry.category,
          tags: entry.tags,
          metadata: entry.metadata,
        });
        setEntries(prev => [...prev, response.data]);
      }
      setIsEditing(false);
      setSelectedEntry(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save entry');
    }
  };

  const handleDeleteEntry = async (entryId: string) => {
    if (!confirm('Are you sure you want to delete this FAQ entry?')) return;
    
    try {
      await faqService.deleteFAQEntry(entryId);
      setEntries(prev => prev.filter(e => e.id !== entryId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete entry');
    }
  };

  const handleToggleActive = async (entry: FAQEntry) => {
    try {
      await faqService.updateFAQEntry(entry.id, { isActive: !entry.isActive });
      setEntries(prev => prev.map(e => 
        e.id === entry.id ? { ...e, isActive: !e.isActive } : e
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update entry');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className={`h-full flex flex-col ${className}`} data-testid={testId}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: colors.border }}>
        <div>
          <h1 className="text-2xl font-bold" style={{ color: colors.text }}>FAQ Management</h1>
          <p className="text-sm mt-1" style={{ color: colors.textSecondary }}>
            Manage your knowledge base and FAQ entries
          </p>
        </div>
        <button
          onClick={handleCreateEntry}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          style={{ backgroundColor: colors.primary }}
        >
          + Add Entry
        </button>
      </div>

      {error && (
        <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="p-6 border-b" style={{ borderColor: colors.border }}>
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search FAQ entries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text,
              }}
            />
          </div>
          <div className="sm:w-48">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text,
              }}
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto p-6">
          {filteredEntries.length === 0 ? (
            <div className="text-center py-12" style={{ color: colors.textSecondary }}>
              <div className="text-4xl mb-4">📚</div>
              <h3 className="text-lg font-medium mb-2">No FAQ entries found</h3>
              <p>Create your first FAQ entry to get started.</p>
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredEntries.map(entry => (
                <div
                  key={entry.id}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  style={{
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium" style={{ color: colors.text }}>
                          {entry.question}
                        </h3>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            entry.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {entry.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <p className="text-sm mb-3 line-clamp-2" style={{ color: colors.textSecondary }}>
                        {entry.answer}
                      </p>
                      <div className="flex items-center gap-4 text-xs" style={{ color: colors.textSecondary }}>
                        <span>Category: {categories.find(c => c.id === entry.category)?.name || entry.category}</span>
                        <span>Usage: {entry.usage}</span>
                        <span>Confidence: {Math.round(entry.confidence * 100)}%</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => handleToggleActive(entry)}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                        title={entry.isActive ? 'Deactivate' : 'Activate'}
                      >
                        {entry.isActive ? '👁️' : '👁️‍🗨️'}
                      </button>
                      <button
                        onClick={() => handleEditEntry(entry)}
                        className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                        title="Edit"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handleDeleteEntry(entry.id)}
                        className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Edit Modal would go here - simplified for now */}
      {isEditing && selectedEntry && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">
              {selectedEntry.id ? 'Edit FAQ Entry' : 'Create FAQ Entry'}
            </h2>
            {/* Edit form would go here */}
            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 text-gray-600 border rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleSaveEntry(selectedEntry)}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FAQManagement;

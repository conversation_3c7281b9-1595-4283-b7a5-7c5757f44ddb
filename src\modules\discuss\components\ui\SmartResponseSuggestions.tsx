import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { faqService, aiAssistantService } from '../../services';
import type { ResponseSuggestion, Message } from '../../types';

export interface SmartResponseSuggestionsProps {
  message: Message;
  onSuggestionSelect: (suggestion: ResponseSuggestion) => void;
  onClose: () => void;
  className?: string;
  'data-testid'?: string;
}

export const SmartResponseSuggestions: React.FC<SmartResponseSuggestionsProps> = ({
  message,
  onSuggestionSelect,
  onClose,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [suggestions, setSuggestions] = useState<ResponseSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'faq' | 'ai' | 'quick'>('faq');

  useEffect(() => {
    loadSuggestions();
  }, [message.id, activeTab]);

  const loadSuggestions = async () => {
    try {
      setIsLoading(true);
      setError(null);

      let response;
      switch (activeTab) {
        case 'faq':
          response = await faqService.getResponseSuggestions(message.id, {
            query: message.content,
            channelId: message.channelId,
          });
          break;
        case 'ai':
          response = await aiAssistantService.getSmartSuggestions(message.id, {
            messageContent: message.content,
            channelId: message.channelId,
            authorId: message.author.id,
          });
          break;
        case 'quick':
          const quickReplies = await aiAssistantService.generateQuickReplies(
            message.content,
            [] // Previous messages would be passed here
          );
          response = {
            data: quickReplies.data?.map((reply, index) => ({
              id: `quick-${index}`,
              title: reply,
              content: reply,
              type: 'quick_reply' as const,
              confidence: 0.8,
              source: 'ai_generated',
            })) || [],
          };
          break;
        default:
          response = { data: [] };
      }

      setSuggestions(response.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load suggestions');
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion: ResponseSuggestion) => {
    onSuggestionSelect(suggestion);
    onClose();
  };

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'faq': return '📚';
      case 'ai': return '🤖';
      case 'quick': return '⚡';
      default: return '💡';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#10B981'; // Green
    if (confidence >= 0.6) return '#F59E0B'; // Yellow
    return '#EF4444'; // Red
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-lg border max-w-md ${className}`}
      style={{ 
        backgroundColor: colors.background,
        borderColor: colors.border,
        color: colors.text,
      }}
      data-testid={testId}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: colors.border }}>
        <h3 className="font-medium">Smart Suggestions</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
          style={{ color: colors.textSecondary }}
        >
          ✕
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b" style={{ borderColor: colors.border }}>
        {[
          { key: 'faq', label: 'FAQ' },
          { key: 'ai', label: 'AI' },
          { key: 'quick', label: 'Quick' },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === tab.key
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            style={{
              color: activeTab === tab.key ? colors.primary : colors.textSecondary,
              borderBottomColor: activeTab === tab.key ? colors.primary : 'transparent',
            }}
          >
            {getTabIcon(tab.key)} {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="max-h-80 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 text-center" style={{ color: colors.textSecondary }}>
            <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
            Loading suggestions...
          </div>
        ) : error ? (
          <div className="p-4 text-center text-red-500">
            <div className="mb-2">⚠️</div>
            {error}
          </div>
        ) : suggestions.length === 0 ? (
          <div className="p-4 text-center" style={{ color: colors.textSecondary }}>
            <div className="mb-2">🤔</div>
            No suggestions available for this message.
          </div>
        ) : (
          <div className="p-2">
            {suggestions.map((suggestion, index) => (
              <button
                key={suggestion.id || index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors mb-2 last:mb-0"
                style={{
                  backgroundColor: 'transparent',
                  ':hover': { backgroundColor: colors.backgroundSecondary },
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.backgroundSecondary;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <div className="flex items-start justify-between mb-1">
                  <span className="font-medium text-sm">{suggestion.title}</span>
                  {suggestion.confidence && (
                    <span
                      className="text-xs px-2 py-1 rounded-full"
                      style={{
                        backgroundColor: getConfidenceColor(suggestion.confidence) + '20',
                        color: getConfidenceColor(suggestion.confidence),
                      }}
                    >
                      {Math.round(suggestion.confidence * 100)}%
                    </span>
                  )}
                </div>
                {suggestion.content && suggestion.content !== suggestion.title && (
                  <p className="text-sm opacity-75 line-clamp-2">
                    {suggestion.content.length > 100
                      ? `${suggestion.content.substring(0, 100)}...`
                      : suggestion.content
                    }
                  </p>
                )}
                {suggestion.source && (
                  <div className="flex items-center mt-2 text-xs" style={{ color: colors.textSecondary }}>
                    <span className="mr-1">
                      {suggestion.source === 'faq' ? '📚' : 
                       suggestion.source === 'ai_generated' ? '🤖' : '💡'}
                    </span>
                    {suggestion.source.replace('_', ' ')}
                  </div>
                )}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-3 border-t text-xs" style={{ 
        borderColor: colors.border,
        color: colors.textSecondary,
      }}>
        💡 Click a suggestion to use it as your response
      </div>
    </div>
  );
};

export default SmartResponseSuggestions;

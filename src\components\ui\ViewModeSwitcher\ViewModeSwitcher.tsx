import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

/**
 * @deprecated Use ViewModeSelector from components/global instead
 * This component will be removed in a future version.
 *
 * Migration:
 * import { ViewModeSelector } from '../../global';
 *
 * Replace:
 * <ViewModeSwitcher viewModes={modes} activeViewMode={active} onViewModeChange={onChange} />
 *
 * With:
 * <ViewModeSelector options={modes} value={active} onChange={onChange} variant="switcher" />
 */

export interface ViewMode {
  name: string;
  icon: React.ReactNode;
}

export interface ViewModeSwitcherProps {
  viewModes: ViewMode[];
  activeViewMode: string;
  onViewModeChange?: (modeName: string) => void;
  className?: string;
  'data-testid'?: string;
}

const ViewModeSwitcher: React.FC<ViewModeSwitcherProps> = ({
  viewModes,
  activeViewMode,
  onViewModeChange,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const handleViewModeClick = (modeName: string) => {
    if (modeName !== activeViewMode) {
      onViewModeChange?.(modeName);
    }
  };

  return (
    <div
      className={cn('flex items-center p-1 rounded-lg border', className)}
      style={{
        backgroundColor: colors.surface,
        borderColor: colors.border,
      }}
      data-testid={testId}
    >
      {viewModes.map(mode => {
        const isActive = mode.name === activeViewMode;
        
        return (
          <button
            key={mode.name}
            onClick={() => handleViewModeClick(mode.name)}
            className="p-2 rounded-md transition-all duration-200 relative"
            style={{
              backgroundColor: isActive
                ? colors.primary
                : 'transparent',
              color: isActive
                ? colors.primaryForeground
                : colors.mutedForeground,
              boxShadow: isActive
                ? `0 1px 3px ${colors.shadow}`
                : 'none',
            }}
            onMouseEnter={e => {
              if (!isActive) {
                e.currentTarget.style.backgroundColor = `${colors.hover}15`;
                e.currentTarget.style.color = colors.text;
              }
            }}
            onMouseLeave={e => {
              if (!isActive) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = colors.mutedForeground;
              }
            }}
            aria-label={`Switch to ${mode.name} view`}
            aria-pressed={isActive}
            title={`${mode.name} view`}
            disabled={isActive}
          >
            {mode.icon}
            {/* Active indicator */}
            {isActive && (
              <div
                className="absolute inset-0 rounded-md border-2"
                style={{
                  borderColor: colors.primary,
                  backgroundColor: 'transparent',
                }}
              />
            )}
          </button>
        );
      })}
    </div>
  );
};

export default ViewModeSwitcher;

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Message } from '../../../components/core/Message';
import type { Message as MessageType, User } from '../../../types';

// Mock the theme store
vi.mock('../../../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#3b82f6',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      backgroundSecondary: '#f1f5f9',
      error: '#ef4444',
    },
  }),
}));

// Mock UI components
vi.mock('../../../components/ui', () => ({
  ContentRenderer: ({ content }: { content: string }) => <div data-testid="content-renderer">{content}</div>,
  MessagePin: ({ onPin, onUnpin, isPinned }: any) => (
    <button
      data-testid="message-pin"
      onClick={() => isPinned ? onUnpin('test-id') : onPin('test-id', 'test reason')}
    >
      {isPinned ? 'Unpin' : 'Pin'}
    </button>
  ),
}));

describe('Message Component', () => {
  const mockUser: User = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'JD',
    status: 'online',
    lastSeen: new Date(),
  };

  const mockMessage: MessageType = {
    id: 'msg-1',
    content: 'Hello, this is a test message!',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date('2024-01-10T10:30:00'),
    reactions: [
      { emoji: '👍', userIds: ['2', '3'], count: 2 },
      { emoji: '❤️', userIds: ['4'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  };

  const defaultProps = {
    message: mockMessage,
    author: mockUser,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders message content correctly', () => {
    render(<Message {...defaultProps} />);
    
    expect(screen.getByTestId('content-renderer')).toBeInTheDocument();
    expect(screen.getByText('Hello, this is a test message!')).toBeInTheDocument();
  });

  it('displays author information when not compact', () => {
    render(<Message {...defaultProps} isCompact={false} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('10:30 AM')).toBeInTheDocument();
  });

  it('hides author information in compact mode', () => {
    render(<Message {...defaultProps} isCompact={true} />);
    
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });

  it('shows avatar when showAvatar is true', () => {
    render(<Message {...defaultProps} showAvatar={true} />);
    
    const avatar = screen.getByText('JD');
    expect(avatar).toBeInTheDocument();
  });

  it('hides avatar when showAvatar is false', () => {
    render(<Message {...defaultProps} showAvatar={false} />);
    
    expect(screen.queryByText('JD')).not.toBeInTheDocument();
  });

  it('displays reactions correctly', () => {
    render(<Message {...defaultProps} />);
    
    expect(screen.getByText('👍')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('❤️')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('handles reaction clicks', async () => {
    const onReaction = vi.fn();
    render(<Message {...defaultProps} onReaction={onReaction} />);
    
    const thumbsUpReaction = screen.getByText('👍').closest('button');
    fireEvent.click(thumbsUpReaction!);
    
    expect(onReaction).toHaveBeenCalledWith('msg-1', '👍');
  });

  it('shows message actions on hover', async () => {
    const onReply = vi.fn();
    const onEdit = vi.fn();
    const onDelete = vi.fn();
    
    render(
      <Message
        {...defaultProps}
        onReply={onReply}
        onEdit={onEdit}
        onDelete={onDelete}
      />
    );
    
    const messageContainer = screen.getByTestId('content-renderer').closest('.group');
    fireEvent.mouseEnter(messageContainer!);
    
    await waitFor(() => {
      expect(screen.getByTitle('Add reaction')).toBeInTheDocument();
      expect(screen.getByTitle('Reply')).toBeInTheDocument();
      expect(screen.getByTitle('Edit')).toBeInTheDocument();
      expect(screen.getByTitle('Delete')).toBeInTheDocument();
    });
  });

  it('handles reply action', async () => {
    const onReply = vi.fn();
    render(<Message {...defaultProps} onReply={onReply} />);
    
    const messageContainer = screen.getByTestId('content-renderer').closest('.group');
    fireEvent.mouseEnter(messageContainer!);
    
    await waitFor(() => {
      const replyButton = screen.getByTitle('Reply');
      fireEvent.click(replyButton);
      expect(onReply).toHaveBeenCalledWith('msg-1');
    });
  });

  it('handles edit action', async () => {
    const onEdit = vi.fn();
    render(<Message {...defaultProps} onEdit={onEdit} />);
    
    const messageContainer = screen.getByTestId('content-renderer').closest('.group');
    fireEvent.mouseEnter(messageContainer!);
    
    await waitFor(() => {
      const editButton = screen.getByTitle('Edit');
      fireEvent.click(editButton);
      expect(onEdit).toHaveBeenCalledWith('msg-1');
    });
  });

  it('handles delete action', async () => {
    const onDelete = vi.fn();
    render(<Message {...defaultProps} onDelete={onDelete} />);
    
    const messageContainer = screen.getByTestId('content-renderer').closest('.group');
    fireEvent.mouseEnter(messageContainer!);
    
    await waitFor(() => {
      const deleteButton = screen.getByTitle('Delete');
      fireEvent.click(deleteButton);
      expect(onDelete).toHaveBeenCalledWith('msg-1');
    });
  });

  it('shows pin component when pin handlers are provided', () => {
    const onPin = vi.fn();
    const onUnpin = vi.fn();
    
    render(
      <Message
        {...defaultProps}
        onPin={onPin}
        onUnpin={onUnpin}
        canPin={true}
        isPinned={false}
      />
    );
    
    expect(screen.getByTestId('message-pin')).toBeInTheDocument();
    expect(screen.getByText('Pin')).toBeInTheDocument();
  });

  it('handles pin action', () => {
    const onPin = vi.fn();
    const onUnpin = vi.fn();
    
    render(
      <Message
        {...defaultProps}
        onPin={onPin}
        onUnpin={onUnpin}
        canPin={true}
        isPinned={false}
      />
    );
    
    const pinButton = screen.getByTestId('message-pin');
    fireEvent.click(pinButton);
    
    expect(onPin).toHaveBeenCalledWith('test-id', 'test reason');
  });

  it('handles unpin action', () => {
    const onPin = vi.fn();
    const onUnpin = vi.fn();
    
    render(
      <Message
        {...defaultProps}
        onPin={onPin}
        onUnpin={onUnpin}
        canPin={true}
        isPinned={true}
      />
    );
    
    const unpinButton = screen.getByTestId('message-pin');
    fireEvent.click(unpinButton);
    
    expect(onUnpin).toHaveBeenCalledWith('test-id');
  });

  it('shows deleted message state', () => {
    const deletedMessage = { ...mockMessage, isDeleted: true };
    render(<Message {...defaultProps} message={deletedMessage} />);
    
    expect(screen.getByText('This message was deleted')).toBeInTheDocument();
    expect(screen.queryByTestId('content-renderer')).not.toBeInTheDocument();
  });

  it('shows edited indicator when message is edited', () => {
    const editedMessage = { ...mockMessage, editedAt: new Date() };
    render(<Message {...defaultProps} message={editedMessage} />);
    
    expect(screen.getByText('(edited)')).toBeInTheDocument();
  });

  it('shows thread indicator for threaded messages', () => {
    const threadedMessage = { ...mockMessage, parentMessageId: 'parent-msg' };
    render(<Message {...defaultProps} message={threadedMessage} />);
    
    expect(screen.getByText('View thread')).toBeInTheDocument();
  });

  it('applies compact styling correctly', () => {
    const { container } = render(<Message {...defaultProps} isCompact={true} />);
    
    const messageContainer = container.querySelector('.py-1');
    expect(messageContainer).toBeInTheDocument();
  });

  it('applies comfortable styling correctly', () => {
    const { container } = render(<Message {...defaultProps} isCompact={false} />);
    
    const messageContainer = container.querySelector('.py-3');
    expect(messageContainer).toBeInTheDocument();
  });

  it('shows user status indicator', () => {
    render(<Message {...defaultProps} showAvatar={true} />);
    
    const statusIndicator = screen.getByText('JD').parentElement?.querySelector('.bg-green-500');
    expect(statusIndicator).toBeInTheDocument();
  });
});

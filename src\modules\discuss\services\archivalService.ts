// Archival and compliance service for message management
import type { 
  MessageArchive,
  RetentionPolicyRule,
  LegalHold,
  ExportRequest,
  ComplianceReport,
  ApiResponse,
  PaginatedResponse,
  Message 
} from '../types';

const API_BASE = '/api/discuss/archival';

export interface ArchiveSearchQuery {
  query?: string;
  channelIds?: string[];
  userIds?: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
  tags?: string[];
  legalHold?: boolean;
  page?: number;
  pageSize?: number;
}

export interface CreateRetentionPolicyRequest {
  name: string;
  description?: string;
  scope: RetentionPolicyRule['scope'];
  duration: number;
  action: RetentionPolicyRule['action'];
}

export interface CreateLegalHoldRequest {
  name: string;
  description?: string;
  scope: LegalHold['scope'];
  reason: string;
  expiresAt?: Date;
}

export interface CreateExportRequest {
  name: string;
  format: ExportRequest['format'];
  scope: ExportRequest['scope'];
  options: ExportRequest['options'];
}

export const archivalService = {
  // Search archived messages
  async searchArchive(query: ArchiveSearchQuery): Promise<PaginatedResponse<MessageArchive>> {
    const params = new URLSearchParams();
    
    if (query.query) params.append('q', query.query);
    if (query.channelIds) params.append('channels', query.channelIds.join(','));
    if (query.userIds) params.append('users', query.userIds.join(','));
    if (query.dateRange) {
      params.append('from', query.dateRange.from.toISOString());
      params.append('to', query.dateRange.to.toISOString());
    }
    if (query.tags) params.append('tags', query.tags.join(','));
    if (query.legalHold !== undefined) params.append('legalHold', query.legalHold.toString());
    params.append('page', (query.page || 1).toString());
    params.append('pageSize', (query.pageSize || 50).toString());

    const response = await fetch(`${API_BASE}/search?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error('Failed to search archive');
    }
    
    return response.json();
  },

  // Get archived message by ID
  async getArchivedMessage(archiveId: string): Promise<ApiResponse<MessageArchive>> {
    const response = await fetch(`${API_BASE}/messages/${archiveId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch archived message');
    }
    
    return response.json();
  },

  // Archive messages manually
  async archiveMessages(messageIds: string[], reason?: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/archive`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messageIds, reason }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to archive messages');
    }
    
    return response.json();
  },

  // Get archive statistics
  async getArchiveStats(): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/stats`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch archive statistics');
    }
    
    return response.json();
  },
};

export const retentionService = {
  // Get retention policies
  async getRetentionPolicies(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<RetentionPolicyRule>> {
    const response = await fetch(`${API_BASE}/retention?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch retention policies');
    }
    
    return response.json();
  },

  // Create retention policy
  async createRetentionPolicy(request: CreateRetentionPolicyRequest): Promise<ApiResponse<RetentionPolicyRule>> {
    const response = await fetch(`${API_BASE}/retention`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create retention policy');
    }
    
    return response.json();
  },

  // Update retention policy
  async updateRetentionPolicy(policyId: string, updates: Partial<CreateRetentionPolicyRequest>): Promise<ApiResponse<RetentionPolicyRule>> {
    const response = await fetch(`${API_BASE}/retention/${policyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update retention policy');
    }
    
    return response.json();
  },

  // Delete retention policy
  async deleteRetentionPolicy(policyId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/retention/${policyId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete retention policy');
    }
    
    return response.json();
  },

  // Apply retention policy
  async applyRetentionPolicy(policyId: string): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/retention/${policyId}/apply`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to apply retention policy');
    }
    
    return response.json();
  },
};

export const legalHoldService = {
  // Get legal holds
  async getLegalHolds(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<LegalHold>> {
    const response = await fetch(`${API_BASE}/legal-holds?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch legal holds');
    }
    
    return response.json();
  },

  // Create legal hold
  async createLegalHold(request: CreateLegalHoldRequest): Promise<ApiResponse<LegalHold>> {
    const response = await fetch(`${API_BASE}/legal-holds`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create legal hold');
    }
    
    return response.json();
  },

  // Release legal hold
  async releaseLegalHold(holdId: string, reason: string): Promise<ApiResponse<LegalHold>> {
    const response = await fetch(`${API_BASE}/legal-holds/${holdId}/release`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reason }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to release legal hold');
    }
    
    return response.json();
  },

  // Get messages under legal hold
  async getLegalHoldMessages(holdId: string, page: number = 1, pageSize: number = 50): Promise<PaginatedResponse<MessageArchive>> {
    const response = await fetch(`${API_BASE}/legal-holds/${holdId}/messages?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch legal hold messages');
    }
    
    return response.json();
  },
};

export const exportService = {
  // Get export requests
  async getExportRequests(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<ExportRequest>> {
    const response = await fetch(`${API_BASE}/exports?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch export requests');
    }
    
    return response.json();
  },

  // Create export request
  async createExportRequest(request: CreateExportRequest): Promise<ApiResponse<ExportRequest>> {
    const response = await fetch(`${API_BASE}/exports`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create export request');
    }
    
    return response.json();
  },

  // Get export status
  async getExportStatus(exportId: string): Promise<ApiResponse<ExportRequest>> {
    const response = await fetch(`${API_BASE}/exports/${exportId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch export status');
    }
    
    return response.json();
  },

  // Download export
  async downloadExport(exportId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE}/exports/${exportId}/download`);
    
    if (!response.ok) {
      throw new Error('Failed to download export');
    }
    
    return response.blob();
  },

  // Cancel export
  async cancelExport(exportId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/exports/${exportId}/cancel`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to cancel export');
    }
    
    return response.json();
  },
};

export const complianceService = {
  // Generate compliance report
  async generateComplianceReport(
    type: ComplianceReport['type'],
    period: { from: Date; to: Date }
  ): Promise<ApiResponse<ComplianceReport>> {
    const response = await fetch(`${API_BASE}/compliance/reports`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ type, period }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate compliance report');
    }
    
    return response.json();
  },

  // Get compliance reports
  async getComplianceReports(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<ComplianceReport>> {
    const response = await fetch(`${API_BASE}/compliance/reports?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch compliance reports');
    }
    
    return response.json();
  },

  // Download compliance report
  async downloadComplianceReport(reportId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE}/compliance/reports/${reportId}/download`);
    
    if (!response.ok) {
      throw new Error('Failed to download compliance report');
    }
    
    return response.blob();
  },
};

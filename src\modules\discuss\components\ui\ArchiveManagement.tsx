import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { archivalService, retentionService, legalHoldService, exportService } from '../../services';
import type { MessageArchive, RetentionPolicyRule, LegalHold, ExportRequest } from '../../types';

export interface ArchiveManagementProps {
  className?: string;
  'data-testid'?: string;
}

export const ArchiveManagement: React.FC<ArchiveManagementProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [activeTab, setActiveTab] = useState<'archive' | 'retention' | 'legal' | 'export'>('archive');
  const [archives, setArchives] = useState<MessageArchive[]>([]);
  const [retentionPolicies, setRetentionPolicies] = useState<RetentionPolicyRule[]>([]);
  const [legalHolds, setLegalHolds] = useState<LegalHold[]>([]);
  const [exportRequests, setExportRequests] = useState<ExportRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      switch (activeTab) {
        case 'archive':
          const archiveResponse = await archivalService.searchArchive({ query: searchQuery });
          setArchives(archiveResponse.data);
          break;
        case 'retention':
          const retentionResponse = await retentionService.getRetentionPolicies();
          setRetentionPolicies(retentionResponse.data);
          break;
        case 'legal':
          const legalResponse = await legalHoldService.getLegalHolds();
          setLegalHolds(legalResponse.data);
          break;
        case 'export':
          const exportResponse = await exportService.getExportRequests();
          setExportRequests(exportResponse.data);
          break;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (activeTab === 'archive') {
      try {
        setIsLoading(true);
        const response = await archivalService.searchArchive({ query: searchQuery });
        setArchives(response.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Search failed');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleExportRequest = async () => {
    try {
      const exportData = {
        name: `Archive Export ${new Date().toISOString().split('T')[0]}`,
        format: 'json' as const,
        scope: {
          channels: [],
          users: [],
          dateRange: {
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            to: new Date(),
          },
          includeAttachments: true,
          includeDeleted: false,
        },
        options: {
          anonymizeUsers: false,
          includeMetadata: true,
          compression: true,
        },
      };

      await exportService.createExportRequest(exportData);
      loadData(); // Refresh the export list
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create export request');
    }
  };

  const tabs = [
    { id: 'archive', label: 'Archive Search', icon: '📁' },
    { id: 'retention', label: 'Retention Policies', icon: '⏰' },
    { id: 'legal', label: 'Legal Holds', icon: '⚖️' },
    { id: 'export', label: 'Export Requests', icon: '📤' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
      case 'enabled':
        return colors.success;
      case 'pending':
      case 'processing':
        return colors.warning;
      case 'failed':
      case 'released':
      case 'disabled':
        return colors.error;
      default:
        return colors.muted;
    }
  };

  return (
    <div className={`p-6 ${className}`} data-testid={testId}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: colors.text }}>
            Archive & Compliance
          </h2>
          <p className="text-sm mt-1" style={{ color: colors.muted }}>
            Manage message archives, retention policies, and compliance requirements
          </p>
        </div>
        {activeTab === 'export' && (
          <button
            onClick={handleExportRequest}
            className="px-4 py-2 rounded-lg text-white font-medium hover:opacity-90 transition-opacity"
            style={{ backgroundColor: colors.primary }}
          >
            New Export
          </button>
        )}
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 border-b" style={{ borderColor: colors.border }}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`px-4 py-2 font-medium text-sm rounded-t-lg transition-colors ${
              activeTab === tab.id
                ? 'border-b-2'
                : 'hover:bg-opacity-50'
            }`}
            style={{
              color: activeTab === tab.id ? colors.primary : colors.muted,
              borderColor: activeTab === tab.id ? colors.primary : 'transparent',
              backgroundColor: activeTab === tab.id ? colors.primary + '10' : 'transparent',
            }}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 rounded-lg" style={{ backgroundColor: colors.error + '20', color: colors.error }}>
          {error}
        </div>
      )}

      {/* Content */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: colors.primary }}></div>
        </div>
      ) : (
        <div>
          {/* Archive Search Tab */}
          {activeTab === 'archive' && (
            <div>
              {/* Search Bar */}
              <div className="flex space-x-4 mb-6">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search archived messages..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="w-full px-4 py-2 rounded-lg border focus:outline-none focus:ring-2"
                    style={{
                      borderColor: colors.border,
                      backgroundColor: colors.background,
                      color: colors.text,
                      focusRingColor: colors.primary,
                    }}
                  />
                </div>
                <button
                  onClick={handleSearch}
                  className="px-6 py-2 rounded-lg text-white font-medium hover:opacity-90 transition-opacity"
                  style={{ backgroundColor: colors.primary }}
                >
                  Search
                </button>
              </div>

              {/* Archive Results */}
              <div className="space-y-4">
                {archives.map((archive) => (
                  <div
                    key={archive.id}
                    className="p-4 rounded-lg border"
                    style={{ borderColor: colors.border, backgroundColor: colors.background }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-sm font-medium" style={{ color: colors.text }}>
                            Channel: {archive.channelId}
                          </span>
                          <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: colors.muted + '20', color: colors.muted }}>
                            {archive.timestamp.toLocaleDateString()}
                          </span>
                          {archive.metadata.legalHold && (
                            <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: colors.warning + '20', color: colors.warning }}>
                              Legal Hold
                            </span>
                          )}
                        </div>
                        <p className="text-sm mb-2" style={{ color: colors.text }}>
                          {archive.content}
                        </p>
                        <div className="flex items-center space-x-4 text-xs" style={{ color: colors.muted }}>
                          <span>Archived: {archive.archivedAt.toLocaleDateString()}</span>
                          <span>Policy: {archive.retentionPolicy}</span>
                          <span>Checksum: {archive.metadata.checksum.substring(0, 16)}...</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {archives.length === 0 && (
                  <div className="text-center py-12" style={{ color: colors.muted }}>
                    <div className="text-4xl mb-2">📁</div>
                    <div>No archived messages found</div>
                    <div className="text-sm">Try adjusting your search criteria</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Retention Policies Tab */}
          {activeTab === 'retention' && (
            <div className="space-y-4">
              {retentionPolicies.map((policy) => (
                <div
                  key={policy.id}
                  className="p-4 rounded-lg border"
                  style={{ borderColor: colors.border, backgroundColor: colors.background }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium" style={{ color: colors.text }}>
                      {policy.name}
                    </h3>
                    <span
                      className="text-xs px-2 py-1 rounded"
                      style={{
                        backgroundColor: getStatusColor(policy.enabled ? 'enabled' : 'disabled') + '20',
                        color: getStatusColor(policy.enabled ? 'enabled' : 'disabled'),
                      }}
                    >
                      {policy.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <p className="text-sm mb-3" style={{ color: colors.muted }}>
                    {policy.description}
                  </p>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span style={{ color: colors.muted }}>Duration:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {policy.duration} days
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Action:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {policy.action}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Last Applied:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {policy.lastApplied?.toLocaleDateString() || 'Never'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Legal Holds Tab */}
          {activeTab === 'legal' && (
            <div className="space-y-4">
              {legalHolds.map((hold) => (
                <div
                  key={hold.id}
                  className="p-4 rounded-lg border"
                  style={{ borderColor: colors.border, backgroundColor: colors.background }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium" style={{ color: colors.text }}>
                      {hold.name}
                    </h3>
                    <span
                      className="text-xs px-2 py-1 rounded"
                      style={{
                        backgroundColor: getStatusColor(hold.status) + '20',
                        color: getStatusColor(hold.status),
                      }}
                    >
                      {hold.status}
                    </span>
                  </div>
                  <p className="text-sm mb-3" style={{ color: colors.muted }}>
                    {hold.description}
                  </p>
                  <div className="text-sm space-y-1">
                    <div>
                      <span style={{ color: colors.muted }}>Reason:</span>
                      <span className="ml-2" style={{ color: colors.text }}>
                        {hold.reason}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Created:</span>
                      <span className="ml-2" style={{ color: colors.text }}>
                        {hold.createdAt.toLocaleDateString()}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Date Range:</span>
                      <span className="ml-2" style={{ color: colors.text }}>
                        {hold.scope.dateRange.from.toLocaleDateString()} - {hold.scope.dateRange.to?.toLocaleDateString() || 'Ongoing'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Export Requests Tab */}
          {activeTab === 'export' && (
            <div className="space-y-4">
              {exportRequests.map((request) => (
                <div
                  key={request.id}
                  className="p-4 rounded-lg border"
                  style={{ borderColor: colors.border, backgroundColor: colors.background }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium" style={{ color: colors.text }}>
                      {request.name}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <span
                        className="text-xs px-2 py-1 rounded"
                        style={{
                          backgroundColor: getStatusColor(request.status) + '20',
                          color: getStatusColor(request.status),
                        }}
                      >
                        {request.status}
                      </span>
                      {request.status === 'completed' && request.downloadUrl && (
                        <button
                          onClick={() => window.open(request.downloadUrl, '_blank')}
                          className="text-xs px-2 py-1 rounded text-white"
                          style={{ backgroundColor: colors.primary }}
                        >
                          Download
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span style={{ color: colors.muted }}>Format:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {request.format.toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Requested:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {request.requestedAt.toLocaleDateString()}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Completed:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {request.completedAt?.toLocaleDateString() || 'Pending'}
                      </span>
                    </div>
                    <div>
                      <span style={{ color: colors.muted }}>Expires:</span>
                      <span className="ml-2 font-medium" style={{ color: colors.text }}>
                        {request.expiresAt?.toLocaleDateString() || 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

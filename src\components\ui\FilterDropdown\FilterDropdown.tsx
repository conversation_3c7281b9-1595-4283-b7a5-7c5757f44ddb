import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import {
  FilterIcon,
  GroupIcon,
  StarFilledIcon,
  CheckIcon,
  ChevronDownIcon,
  TrashIcon,
} from '../../icons';

/**
 * @deprecated Use FilterDropdown from components/global instead
 * This component will be removed in a future version.
 *
 * Migration:
 * import { FilterDropdown } from '../../global';
 *
 * The new FilterDropdown provides better composition, improved performance,
 * and more flexible layouts using the enhanced dropdown system.
 */

// Custom StarIcon component to handle filled state
const CustomStarIcon = ({ filled = false }: { filled?: boolean }) => {
  return filled ? (
    <StarFilledIcon className="w-4 h-4" />
  ) : (
    <StarFilledIcon className="w-4 h-4" />
  );
};

export interface FilterItem {
  id: string;
  label: string;
  selected?: boolean;
  hasDropdown?: boolean;
}

export interface GroupByItem {
  id: string;
  label: string;
  hasDropdown?: boolean;
}

export interface FavoriteItem {
  id: string;
  label: string;
  selected?: boolean;
}

export interface FilterDropdownProps {
  isOpen: boolean;
  filterItems?: FilterItem[];
  groupByItems?: GroupByItem[];
  favoriteItems?: FavoriteItem[];
  searchQuery?: string;
  onFilterSelect?: (filterId: string) => void;
  onGroupBySelect?: (groupId: string) => void;
  onFavoriteSelect?: (favoriteId: string) => void;
  onFavoriteDelete?: (favoriteId: string) => void;
  onAddCustomFilter?: () => void;
  onAddCustomGroup?: () => void;
  onSaveCurrentSearch?: () => void;
  className?: string;
  compact?: boolean;
  'data-testid'?: string;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  isOpen,
  filterItems = [],
  groupByItems = [],
  favoriteItems = [],
  searchQuery = '',
  onFilterSelect,
  onGroupBySelect,
  onFavoriteSelect,
  onFavoriteDelete,
  onAddCustomFilter,
  onAddCustomGroup,
  onSaveCurrentSearch,
  className = '',
  compact = false,
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  if (!isOpen) return null;

  // Compact color scheme
  const dropdownColors = {
    primaryBg: colors.surface,
    primaryText: colors.text,
    secondaryText: colors.textMuted,
    filtersIcon: '#E91E63', // pink
    groupByIcon: '#00BCD4', // cyan
    favoritesIcon: '#FFC107', // yellow
    hoverBg: colors.hover,
    borderColor: colors.border,
    trashHover: colors.error,
  };

  // Filter items based on search query
  const getFilteredItems = (items: FilterItem[]) => {
    if (!searchQuery) return items;
    return items.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getFilteredGroupByItems = (items: GroupByItem[]) => {
    if (!searchQuery) return items;
    return items.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getFilteredFavoriteItems = (items: FavoriteItem[]) => {
    if (!searchQuery) return items;
    return items.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const dropdownHeight = compact ? '250px' : '300px';
  const dropdownWidth = compact ? '600px' : '750px';
  const sectionPadding = compact ? 'px-3 pt-3 pb-0' : 'px-4 pt-4 pb-0';
  const headerHeight = compact ? '32px' : '36px';
  const addButtonHeight = compact ? '28px' : '32px';

  return (
    <div
      className={cn('absolute top-full left-0 right-0 mt-1 border rounded z-50 overflow-hidden', className)}
      style={{
        backgroundColor: dropdownColors.primaryBg,
        borderColor: dropdownColors.borderColor,
        boxShadow: `0 4px 6px -1px ${colors.shadow}`,
        width: dropdownWidth,
        maxWidth: '100vw',
        height: dropdownHeight,
      }}
      data-testid={testId}
    >
      {/* Three-column layout */}
      <div className="flex h-full">
        {/* Filters Section */}
        <div
          className="flex-1 border-r flex flex-col"
          style={{
            borderColor: dropdownColors.borderColor,
            width: '33.333%',
          }}
        >
          {/* Header */}
          <div
            className={cn('flex items-center gap-2 flex-shrink-0', compact ? 'px-3 pt-3 pb-2' : 'px-4 pt-4 pb-3')}
            style={{
              minHeight: headerHeight,
              borderBottom: `1px solid ${dropdownColors.borderColor}`,
            }}
          >
            <div style={{ color: dropdownColors.filtersIcon }}>
              <FilterIcon className="w-4 h-4" />
            </div>
            <h3
              className="text-sm font-bold"
              style={{ color: dropdownColors.primaryText }}
            >
              Filters
            </h3>
          </div>
          
          {/* Scrollable Content */}
          <div
            className="flex-1 overflow-y-auto px-3 pb-2"
            style={{
              maxHeight: `calc(${dropdownHeight} - ${headerHeight} - ${addButtonHeight} - 8px)`,
            }}
          >
            <ul className="space-y-0.5">
              {getFilteredItems(filterItems).map(item => (
                <li key={item.id}>
                  <button
                    type="button"
                    onClick={() => onFilterSelect?.(item.id)}
                    className="w-full text-left px-2 py-1 text-sm rounded transition-colors flex items-center justify-between group"
                    style={{
                      color: dropdownColors.primaryText,
                      backgroundColor: item.selected
                        ? dropdownColors.hoverBg
                        : 'transparent',
                    }}
                    onMouseEnter={e => {
                      if (!item.selected) {
                        e.currentTarget.style.backgroundColor = dropdownColors.hoverBg;
                      }
                    }}
                    onMouseLeave={e => {
                      if (!item.selected) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    <span className="flex items-center gap-2">
                      {item.selected && (
                        <div style={{ color: dropdownColors.filtersIcon }}>
                          <CheckIcon className="w-3 h-3" />
                        </div>
                      )}
                      {item.label}
                    </span>
                    {item.hasDropdown && (
                      <div style={{ color: dropdownColors.secondaryText }}>
                        <ChevronDownIcon className="w-3 h-3" />
                      </div>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Sticky Add Button */}
          {onAddCustomFilter && (
            <div
              className="flex-shrink-0 px-3 pb-3 pt-2"
              style={{
                minHeight: addButtonHeight,
                backgroundColor: dropdownColors.primaryBg,
                borderTop: `1px solid ${dropdownColors.borderColor}`,
              }}
            >
              <button
                type="button"
                onClick={onAddCustomFilter}
                className="text-xs transition-colors"
                style={{ color: dropdownColors.filtersIcon }}
              >
                Add Custom Filter
              </button>
            </div>
          )}
        </div>

        {/* Group By Section */}
        <div
          className="flex-1 border-r flex flex-col"
          style={{
            borderColor: dropdownColors.borderColor,
            width: '33.333%',
          }}
        >
          {/* Header */}
          <div
            className={cn('flex items-center gap-2 flex-shrink-0', compact ? 'px-3 pt-3 pb-2' : 'px-4 pt-4 pb-3')}
            style={{
              minHeight: headerHeight,
              borderBottom: `1px solid ${dropdownColors.borderColor}`,
            }}
          >
            <div style={{ color: dropdownColors.groupByIcon }}>
              <GroupIcon className="w-4 h-4" />
            </div>
            <h3
              className="text-sm font-bold"
              style={{ color: dropdownColors.primaryText }}
            >
              Group By
            </h3>
          </div>
          
          {/* Scrollable Content */}
          <div
            className="flex-1 overflow-y-auto px-3 pb-2"
            style={{
              maxHeight: `calc(${dropdownHeight} - ${headerHeight} - ${addButtonHeight} - 8px)`,
            }}
          >
            <ul className="space-y-0.5">
              {getFilteredGroupByItems(groupByItems).map(item => (
                <li key={item.id}>
                  <button
                    type="button"
                    onClick={() => onGroupBySelect?.(item.id)}
                    className="w-full text-left px-2 py-1 text-sm rounded transition-colors flex items-center justify-between group"
                    style={{ color: dropdownColors.primaryText }}
                    onMouseEnter={e => {
                      e.currentTarget.style.backgroundColor = dropdownColors.hoverBg;
                    }}
                    onMouseLeave={e => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    <span>{item.label}</span>
                    {item.hasDropdown && (
                      <div style={{ color: dropdownColors.secondaryText }}>
                        <ChevronDownIcon className="w-3 h-3" />
                      </div>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Sticky Add Button */}
          {onAddCustomGroup && (
            <div
              className="flex-shrink-0 px-3 pb-3 pt-2"
              style={{
                minHeight: addButtonHeight,
                backgroundColor: dropdownColors.primaryBg,
                borderTop: `1px solid ${dropdownColors.borderColor}`,
              }}
            >
              <button
                type="button"
                onClick={onAddCustomGroup}
                className="text-xs transition-colors"
                style={{ color: dropdownColors.groupByIcon }}
              >
                Add Custom Group
              </button>
            </div>
          )}
        </div>

        {/* Favorites Section */}
        <div
          className="flex-1 flex flex-col"
          style={{ width: '33.333%' }}
        >
          {/* Header */}
          <div
            className={cn('flex items-center gap-2 flex-shrink-0', compact ? 'px-3 pt-3 pb-2' : 'px-4 pt-4 pb-3')}
            style={{
              minHeight: headerHeight,
              borderBottom: `1px solid ${dropdownColors.borderColor}`,
            }}
          >
            <div style={{ color: dropdownColors.favoritesIcon }}>
              <CustomStarIcon filled />
            </div>
            <h3
              className="text-sm font-bold"
              style={{ color: dropdownColors.primaryText }}
            >
              Favorites
            </h3>
          </div>
          
          {/* Scrollable Content */}
          <div
            className="flex-1 overflow-y-auto px-3 pb-2"
            style={{
              maxHeight: `calc(${dropdownHeight} - ${headerHeight} - ${addButtonHeight} - 8px)`,
            }}
          >
            <ul className="space-y-0.5">
              {getFilteredFavoriteItems(favoriteItems).map(item => (
                <li key={item.id}>
                  <div className="flex items-center justify-between group">
                    <button
                      type="button"
                      onClick={() => onFavoriteSelect?.(item.id)}
                      className="flex-1 text-left px-2 py-1 text-sm rounded transition-colors flex items-center gap-2"
                      style={{
                        color: dropdownColors.primaryText,
                        backgroundColor: item.selected
                          ? dropdownColors.hoverBg
                          : 'transparent',
                      }}
                      onMouseEnter={e => {
                        if (!item.selected) {
                          e.currentTarget.style.backgroundColor = dropdownColors.hoverBg;
                        }
                      }}
                      onMouseLeave={e => {
                        if (!item.selected) {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }
                      }}
                    >
                      {item.selected && (
                        <div style={{ color: dropdownColors.favoritesIcon }}>
                          <CheckIcon className="w-3 h-3" />
                        </div>
                      )}
                      <span>{item.label}</span>
                    </button>
                    {onFavoriteDelete && (
                      <button
                        type="button"
                        onClick={() => onFavoriteDelete(item.id)}
                        className="p-1 rounded transition-colors opacity-0 group-hover:opacity-100"
                        style={{ color: dropdownColors.secondaryText }}
                        onMouseEnter={e => {
                          e.currentTarget.style.color = dropdownColors.trashHover;
                        }}
                        onMouseLeave={e => {
                          e.currentTarget.style.color = dropdownColors.secondaryText;
                        }}
                      >
                        <TrashIcon className="w-3 h-3" />
                      </button>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Sticky Add Button */}
          {onSaveCurrentSearch && (
            <div
              className="flex-shrink-0 px-3 pb-3 pt-2"
              style={{
                minHeight: addButtonHeight,
                backgroundColor: dropdownColors.primaryBg,
                borderTop: `1px solid ${dropdownColors.borderColor}`,
              }}
            >
              <button
                type="button"
                onClick={onSaveCurrentSearch}
                className="text-xs transition-colors"
                style={{ color: dropdownColors.favoritesIcon }}
              >
                Save current search
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FilterDropdown;

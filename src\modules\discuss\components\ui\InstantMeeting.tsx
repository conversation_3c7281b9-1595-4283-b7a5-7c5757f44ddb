import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { useCall } from '../../hooks/useCall';
import { getMockUserById } from '../../../../mocks/data/discuss';
import type { User } from '../../types';

export interface InstantMeetingProps {
  currentUserId: string;
  availableUsers: User[];
  onMeetingStarted?: (meetingId: string) => void;
  onCancel?: () => void;
  className?: string;
  'data-testid'?: string;
}

export const InstantMeeting: React.FC<InstantMeetingProps> = ({
  currentUserId,
  availableUsers,
  onMeetingStarted,
  onCancel,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const { startCall } = useCall({ userId: currentUserId });
  
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [meetingType, setMeetingType] = useState<'voice' | 'video'>('video');
  const [isStarting, setIsStarting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredUsers = availableUsers.filter(user => 
    user.id !== currentUserId &&
    user.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleStartMeeting = async () => {
    if (selectedUsers.length === 0) return;

    try {
      setIsStarting(true);
      const call = await startCall(meetingType, selectedUsers);
      onMeetingStarted?.(call.id);
    } catch (error) {
      console.error('Failed to start meeting:', error);
    } finally {
      setIsStarting(false);
    }
  };

  return (
    <div 
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md ${className}`}
      style={{ borderColor: colors.border }}
      data-testid={testId}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold" style={{ color: colors.text }}>
          Start Instant Meeting
        </h3>
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ✕
          </button>
        )}
      </div>

      {/* Meeting Type Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
          Meeting Type
        </label>
        <div className="flex space-x-2">
          <button
            onClick={() => setMeetingType('voice')}
            className={`flex-1 py-2 px-4 rounded-lg border transition-colors ${
              meetingType === 'voice'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
            style={meetingType !== 'voice' ? { color: colors.text } : {}}
          >
            🎤 Voice Only
          </button>
          <button
            onClick={() => setMeetingType('video')}
            className={`flex-1 py-2 px-4 rounded-lg border transition-colors ${
              meetingType === 'video'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
            style={meetingType !== 'video' ? { color: colors.text } : {}}
          >
            📹 Video Call
          </button>
        </div>
      </div>

      {/* User Search */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
          Invite Participants
        </label>
        <input
          type="text"
          placeholder="Search users..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
          style={{ 
            borderColor: colors.border,
            backgroundColor: colors.background,
            color: colors.text 
          }}
        />
      </div>

      {/* User List */}
      <div className="mb-4 max-h-48 overflow-y-auto">
        {filteredUsers.length === 0 ? (
          <p className="text-center py-4" style={{ color: colors.textSecondary }}>
            No users found
          </p>
        ) : (
          <div className="space-y-2">
            {filteredUsers.map(user => (
              <div
                key={user.id}
                className={`flex items-center p-2 rounded-lg cursor-pointer transition-colors ${
                  selectedUsers.includes(user.id)
                    ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
                onClick={() => toggleUserSelection(user.id)}
              >
                <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center mr-3">
                  {user.name.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1">
                  <p className="font-medium" style={{ color: colors.text }}>
                    {user.name}
                  </p>
                  <p className="text-sm" style={{ color: colors.textSecondary }}>
                    {user.presence?.status || 'offline'}
                  </p>
                </div>
                {selectedUsers.includes(user.id) && (
                  <div className="text-blue-500">✓</div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Selected Users Count */}
      {selectedUsers.length > 0 && (
        <div className="mb-4 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            {selectedUsers.length} participant{selectedUsers.length !== 1 ? 's' : ''} selected
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-3">
        {onCancel && (
          <button
            onClick={onCancel}
            className="flex-1 py-2 px-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            style={{ 
              borderColor: colors.border,
              color: colors.textSecondary 
            }}
          >
            Cancel
          </button>
        )}
        <button
          onClick={handleStartMeeting}
          disabled={selectedUsers.length === 0 || isStarting}
          className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
            selectedUsers.length === 0 || isStarting
              ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          {isStarting ? 'Starting...' : `Start ${meetingType === 'video' ? 'Video' : 'Voice'} Meeting`}
        </button>
      </div>
    </div>
  );
};

// Quick meeting button component
export interface QuickMeetingButtonProps {
  currentUserId: string;
  availableUsers: User[];
  onMeetingStarted?: (meetingId: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const QuickMeetingButton: React.FC<QuickMeetingButtonProps> = ({
  currentUserId,
  availableUsers,
  onMeetingStarted,
  className = '',
  size = 'md',
}) => {
  const [showMeetingDialog, setShowMeetingDialog] = useState(false);

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'p-2 text-sm';
      case 'lg':
        return 'p-4 text-lg';
      default:
        return 'p-3';
    }
  };

  return (
    <>
      <button
        onClick={() => setShowMeetingDialog(true)}
        className={`${getSizeClasses()} bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors ${className}`}
        title="Start instant meeting"
      >
        📹 Quick Meeting
      </button>

      {showMeetingDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <InstantMeeting
            currentUserId={currentUserId}
            availableUsers={availableUsers}
            onMeetingStarted={(meetingId) => {
              onMeetingStarted?.(meetingId);
              setShowMeetingDialog(false);
            }}
            onCancel={() => setShowMeetingDialog(false)}
          />
        </div>
      )}
    </>
  );
};

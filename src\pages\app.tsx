import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { DynamicAppView } from '../components/DynamicAppView';
import { componentRegistry } from '../registry';

const AppPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const menuId = searchParams.get('menu');

  // Check if there's a registered component for this app
  if (menuId && componentRegistry.has(menuId)) {
    const RegisteredComponent = componentRegistry.get(menuId)!;
    return <RegisteredComponent data-testid={`app-${menuId}`} />;
  }

  // Use DynamicAppView for all other apps
  return <DynamicAppView data-testid="app-page" />;
};

export default AppPage;

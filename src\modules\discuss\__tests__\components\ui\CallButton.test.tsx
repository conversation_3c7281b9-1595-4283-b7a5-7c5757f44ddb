import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { CallButton, VoiceCallButton, VideoCallButton, CallButtonGroup } from '../../../components/ui/CallButton';

// Mock the useCall hook
const mockStartCall = vi.fn();
vi.mock('../../../hooks/useCall', () => ({
  useCall: () => ({
    startCall: mockStartCall,
    isConnecting: false,
    callError: null,
  }),
}));

describe('CallButton', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders voice call button correctly', () => {
    render(
      <CallButton
        type="voice"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
      />
    );

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('title', 'Start voice call');
  });

  it('renders video call button correctly', () => {
    render(
      <CallButton
        type="video"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
      />
    );

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('title', 'Start video call');
  });

  it('calls startCall when clicked', async () => {
    const mockCall = { id: 'call-123' };
    mockStartCall.mockResolvedValue(mockCall);
    const onCallStarted = vi.fn();

    render(
      <CallButton
        type="voice"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
        onCallStarted={onCallStarted}
      />
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    await waitFor(() => {
      expect(mockStartCall).toHaveBeenCalledWith('voice', ['user1', 'user2']);
      expect(onCallStarted).toHaveBeenCalledWith('call-123');
    });
  });

  it('is disabled when disabled prop is true', () => {
    render(
      <CallButton
        type="voice"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
        disabled={true}
      />
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('title', 'Call not available');
  });

  it('shows loading state when starting call', async () => {
    mockStartCall.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(
      <CallButton
        type="voice"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
      />
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    // Should show loading spinner
    expect(button.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { rerender } = render(
      <CallButton
        type="voice"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
        size="sm"
      />
    );

    let button = screen.getByRole('button');
    expect(button).toHaveClass('p-1.5');

    rerender(
      <CallButton
        type="voice"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
        size="lg"
      />
    );

    button = screen.getByRole('button');
    expect(button).toHaveClass('p-4');
  });

  it('applies correct variant classes', () => {
    const { rerender } = render(
      <CallButton
        type="voice"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
        variant="secondary"
      />
    );

    let button = screen.getByRole('button');
    expect(button).toHaveClass('bg-gray-200');

    rerender(
      <CallButton
        type="voice"
        participantIds={['user1', 'user2']}
        currentUserId="user1"
        variant="ghost"
      />
    );

    button = screen.getByRole('button');
    expect(button).toHaveClass('bg-transparent');
  });
});

describe('VoiceCallButton', () => {
  it('renders as voice call button', () => {
    render(
      <VoiceCallButton
        participantIds={['user1', 'user2']}
        currentUserId="user1"
      />
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('title', 'Start voice call');
  });
});

describe('VideoCallButton', () => {
  it('renders as video call button', () => {
    render(
      <VideoCallButton
        participantIds={['user1', 'user2']}
        currentUserId="user1"
      />
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('title', 'Start video call');
  });
});

describe('CallButtonGroup', () => {
  it('renders both voice and video buttons', () => {
    render(
      <CallButtonGroup
        participantIds={['user1', 'user2']}
        currentUserId="user1"
      />
    );

    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(2);
    expect(buttons[0]).toHaveAttribute('title', 'Start voice call');
    expect(buttons[1]).toHaveAttribute('title', 'Start video call');
  });

  it('shows labels when showLabels is true', () => {
    render(
      <CallButtonGroup
        participantIds={['user1', 'user2']}
        currentUserId="user1"
        showLabels={true}
      />
    );

    expect(screen.getByText('Voice')).toBeInTheDocument();
    expect(screen.getByText('Video')).toBeInTheDocument();
  });

  it('calls onCallStarted for both buttons', async () => {
    const mockCall = { id: 'call-123' };
    mockStartCall.mockResolvedValue(mockCall);
    const onCallStarted = vi.fn();

    render(
      <CallButtonGroup
        participantIds={['user1', 'user2']}
        currentUserId="user1"
        onCallStarted={onCallStarted}
      />
    );

    const buttons = screen.getAllByRole('button');
    
    // Test voice button
    fireEvent.click(buttons[0]);
    await waitFor(() => {
      expect(mockStartCall).toHaveBeenCalledWith('voice', ['user1', 'user2']);
      expect(onCallStarted).toHaveBeenCalledWith('call-123');
    });

    // Test video button
    fireEvent.click(buttons[1]);
    await waitFor(() => {
      expect(mockStartCall).toHaveBeenCalledWith('video', ['user1', 'user2']);
      expect(onCallStarted).toHaveBeenCalledWith('call-123');
    });
  });
});

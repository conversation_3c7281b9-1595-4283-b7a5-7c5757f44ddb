// Bot initialization service for setting up and registering bots
import { botFramework } from './botFramework';
import { mockBots } from '../../../mocks/data/discuss';
import type { Bot } from '../types';

export class BotInitializationService {
  private initialized = false;

  async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('Bot framework already initialized');
      return;
    }

    try {
      console.log('Initializing bot framework...');
      
      // Register all mock bots with the framework
      for (const bot of mockBots) {
        await this.registerBot(bot);
      }

      this.initialized = true;
      console.log(`Bot framework initialized with ${mockBots.length} bots`);
    } catch (error) {
      console.error('Failed to initialize bot framework:', error);
      throw error;
    }
  }

  private async registerBot(bot: Bot): Promise<void> {
    try {
      // Get the appropriate handler for the bot type
      const handler = this.getBotHandler(bot.type);
      
      if (!handler) {
        console.warn(`No handler found for bot type: ${bot.type}`);
        return;
      }

      // Register the bot with the framework
      botFramework.registerBot(bot, handler);
      
      console.log(`Registered bot: ${bot.name} (${bot.type})`);
    } catch (error) {
      console.error(`Failed to register bot ${bot.name}:`, error);
    }
  }

  private getBotHandler(botType: Bot['type']): any {
    // Import handlers dynamically to avoid circular dependencies
    switch (botType) {
      case 'faq':
        return new (require('./botFramework').FAQBotHandler)();
      case 'ai_assistant':
        return new (require('./botFramework').AIAssistantHandler)();
      case 'workflow':
        return new (require('./botFramework').WorkflowBotHandler)();
      default:
        return null;
    }
  }

  async registerCustomBot(bot: Bot, handler: any): Promise<void> {
    try {
      botFramework.registerBot(bot, handler);
      console.log(`Registered custom bot: ${bot.name}`);
    } catch (error) {
      console.error(`Failed to register custom bot ${bot.name}:`, error);
      throw error;
    }
  }

  async unregisterBot(botId: string): Promise<void> {
    try {
      botFramework.unregisterBot(botId);
      console.log(`Unregistered bot: ${botId}`);
    } catch (error) {
      console.error(`Failed to unregister bot ${botId}:`, error);
      throw error;
    }
  }

  getRegisteredBots(): string[] {
    return Array.from(botFramework['registeredBots'].keys());
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  async reinitialize(): Promise<void> {
    this.initialized = false;
    await this.initialize();
  }
}

// Export singleton instance
export const botInitializationService = new BotInitializationService();

// Auto-initialize when the module is loaded
if (typeof window !== 'undefined') {
  // Only initialize in browser environment
  botInitializationService.initialize().catch(error => {
    console.error('Failed to auto-initialize bot framework:', error);
  });
}

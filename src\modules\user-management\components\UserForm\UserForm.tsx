import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { Button, Input, Label } from '../../../../components/ui';
import { useUserForm } from '../../hooks';
import type { UserFormData, UserRole } from '../../types';

export interface UserFormProps {
  onSubmit: (data: UserFormData) => Promise<void>;
  onCancel?: () => void;
  initialData?: Partial<UserFormData>;
  submitLabel?: string;
  className?: string;
  'data-testid'?: string;
}

const roleOptions: { value: UserRole; label: string }[] = [
  { value: 'user', label: 'User' },
  { value: 'admin', label: 'Admin' },
  { value: 'moderator', label: 'Moderator' },
  { value: 'viewer', label: 'Viewer' },
];

export const UserForm: React.FC<UserFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  submitLabel = 'Save User',
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const {
    formData,
    errors,
    isSubmitting,
    isValid,
    handleChange,
    handleSubmit,
  } = useUserForm(onSubmit);

  // Set initial data if provided
  React.useEffect(() => {
    if (initialData) {
      Object.entries(initialData).forEach(([key, value]) => {
        if (value !== undefined) {
          handleChange(key as keyof UserFormData, value as string);
        }
      });
    }
  }, [initialData, handleChange]);

  return (
    <form 
      onSubmit={handleSubmit} 
      className={`space-y-4 ${className}`}
      data-testid={testId}
    >
      {errors.general && (
        <div className="p-3 rounded-md bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
          <p className="text-sm text-red-800 dark:text-red-200">{errors.general}</p>
        </div>
      )}

      <div>
        <Label htmlFor="name" required>
          Name
        </Label>
        <Input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => handleChange('name', e.target.value)}
          placeholder="Enter user name"
          error={errors.name}
          required
          data-testid="user-form-name"
        />
      </div>

      <div>
        <Label htmlFor="email" required>
          Email
        </Label>
        <Input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => handleChange('email', e.target.value)}
          placeholder="Enter email address"
          error={errors.email}
          required
          data-testid="user-form-email"
        />
      </div>

      <div>
        <Label htmlFor="role" required>
          Role
        </Label>
        <select
          id="role"
          value={formData.role}
          onChange={(e) => handleChange('role', e.target.value)}
          className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
          data-testid="user-form-role"
        >
          {roleOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {errors.role && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.role}</p>
        )}
      </div>

      <div className="flex space-x-3 pt-2">
        <Button
          type="submit"
          variant="primary"
          disabled={!isValid || isSubmitting}
          loading={isSubmitting}
          className="flex-1"
          data-testid="user-form-submit"
        >
          {isSubmitting ? 'Saving...' : submitLabel}
        </Button>
        
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
            data-testid="user-form-cancel"
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
};

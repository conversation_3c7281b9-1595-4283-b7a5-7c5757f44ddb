// Configuration handlers for MSW
import { http, HttpResponse } from 'msw';
import {
  getFeatureFlags,
  getConfiguration,
  getSystemSettings,
  getEnvironmentConfig,
  type FeatureFlags,
  type AppConfiguration,
  type SystemSettings,
} from '../data/configuration';
import { getUserById, type User } from '../data/users';
import {
  delay,
  randomDelay,
  simulateError,
  commonErrors,
  successResponse,
  errorResponse,
  unauthorizedResponse,
  extractBearerToken,
  validateToken,
  getEnvironment,
} from '../utils';

// Mutable configuration state
let currentFeatureFlags = getFeatureFlags();
let currentConfiguration = getConfiguration();
let currentSystemSettings = getSystemSettings();

// Helper to check permissions
const hasPermission = (user: User, permission: string): boolean => {
  return (
    user.permissions.includes(permission) || user.permissions.includes('admin')
  );
};

// Helper to get authenticated user
const getAuthenticatedUser = async (
  authHeader: string | null
): Promise<User | null> => {
  const token = extractBearerToken(authHeader);
  if (!token) return null;

  const userId = validateToken(token);
  if (!userId) return null;

  return getUserById(userId) || null;
};

export const configurationHandlers = [
  // Get feature flags
  http.get('/api/config/features', async ({ request }) => {
    await delay(100);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    return successResponse(currentFeatureFlags);
  }),

  // Update feature flags
  http.put('/api/config/features', async ({ request }) => {
    await delay(300);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'admin')) {
      return HttpResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    try {
      const updates = (await request.json()) as Partial<FeatureFlags>;

      currentFeatureFlags = {
        ...currentFeatureFlags,
        ...updates,
      };

      return successResponse(currentFeatureFlags);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Get application configuration
  http.get('/api/config/app', async ({ request }) => {
    await delay(100);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    return successResponse(currentConfiguration);
  }),

  // Update application configuration
  http.put('/api/config/app', async ({ request }) => {
    await delay(400);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'admin')) {
      return HttpResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    try {
      const updates = (await request.json()) as Partial<AppConfiguration>;

      // Validate critical settings
      if (updates.maxFileUploadSize && updates.maxFileUploadSize > 104857600) {
        // 100MB
        return errorResponse('Max file upload size cannot exceed 100MB', 400);
      }

      if (updates.sessionTimeout && updates.sessionTimeout < 300000) {
        // 5 minutes
        return errorResponse(
          'Session timeout cannot be less than 5 minutes',
          400
        );
      }

      currentConfiguration = {
        ...currentConfiguration,
        ...updates,
      };

      return successResponse(currentConfiguration);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Get system settings
  http.get('/api/config/system', async ({ request }) => {
    await delay(150);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    return successResponse(currentSystemSettings);
  }),

  // Update system settings
  http.put('/api/config/system', async ({ request }) => {
    await delay(500);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'admin')) {
      return HttpResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    try {
      const updates = (await request.json()) as Partial<SystemSettings>;

      // Validate security settings
      if (
        updates.security?.passwordMinLength &&
        updates.security.passwordMinLength < 4
      ) {
        return errorResponse(
          'Password minimum length cannot be less than 4',
          400
        );
      }

      if (
        updates.security?.maxLoginAttempts &&
        updates.security.maxLoginAttempts < 1
      ) {
        return errorResponse('Max login attempts must be at least 1', 400);
      }

      // Deep merge for nested objects
      currentSystemSettings = {
        ...currentSystemSettings,
        ...updates,
        security: updates.security
          ? {
              ...currentSystemSettings.security,
              ...updates.security,
            }
          : currentSystemSettings.security,
        ui: updates.ui
          ? {
              ...currentSystemSettings.ui,
              ...updates.ui,
            }
          : currentSystemSettings.ui,
        performance: updates.performance
          ? {
              ...currentSystemSettings.performance,
              ...updates.performance,
            }
          : currentSystemSettings.performance,
      };

      return successResponse(currentSystemSettings);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Get all configuration
  http.get('/api/config', async ({ request }) => {
    await delay(200);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    return successResponse({
      features: currentFeatureFlags,
      app: currentConfiguration,
      system: currentSystemSettings,
      environment: getEnvironment(),
    });
  }),

  // Reset configuration to defaults
  http.post('/api/config/reset', async ({ request }) => {
    await delay(600);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'admin')) {
      return HttpResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    try {
      const { section } = (await request.json()) as {
        section?: 'features' | 'app' | 'system' | 'all';
      };

      const environment = getEnvironment();
      const defaultConfig = getEnvironmentConfig(environment);

      switch (section) {
        case 'features':
          currentFeatureFlags = defaultConfig.featureFlags;
          break;
        case 'app':
          currentConfiguration = defaultConfig.configuration;
          break;
        case 'system':
          currentSystemSettings = defaultConfig.systemSettings;
          break;
        case 'all':
        default:
          currentFeatureFlags = defaultConfig.featureFlags;
          currentConfiguration = defaultConfig.configuration;
          currentSystemSettings = defaultConfig.systemSettings;
          break;
      }

      return successResponse({
        message: `Configuration ${section || 'all'} reset to defaults`,
        features: currentFeatureFlags,
        app: currentConfiguration,
        system: currentSystemSettings,
      });
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Get environment-specific configuration
  http.get('/api/config/environment/:env', async ({ params, request }) => {
    await delay(150);

    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);

    if (!currentUser) {
      return unauthorizedResponse();
    }

    if (!hasPermission(currentUser, 'admin')) {
      return HttpResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    const environment = params.env as string;
    const config = getEnvironmentConfig(environment);

    return successResponse({
      environment,
      ...config,
    });
  }),

  // Health check endpoint
  http.get('/api/config/health', async () => {
    await delay(50);

    return successResponse({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: getEnvironment(),
      version: currentConfiguration.version,
    });
  }),
];

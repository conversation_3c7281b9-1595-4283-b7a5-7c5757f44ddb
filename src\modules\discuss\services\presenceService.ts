// Presence service for managing user presence and activity status
import type { ApiResponse, User, PresenceInfo } from '../types';

const API_BASE = '/api/discuss';

export interface UpdatePresenceRequest {
  status: User['status'];
  customMessage?: string;
  channelId?: string;
}

export interface TypingRequest {
  channelId: string;
  isTyping: boolean;
}

export const presenceService = {
  // Update user presence status
  async updatePresence(request: UpdatePresenceRequest): Promise<ApiResponse<PresenceInfo>> {
    const response = await fetch(`${API_BASE}/presence`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update presence');
    }
    
    return response.json();
  },

  // Get presence info for a user
  async getUserPresence(userId: string): Promise<ApiResponse<PresenceInfo>> {
    const response = await fetch(`${API_BASE}/presence/${userId}`);
    
    if (!response.ok) {
      throw new Error('Failed to get user presence');
    }
    
    return response.json();
  },

  // Get presence info for multiple users
  async getMultipleUserPresence(userIds: string[]): Promise<ApiResponse<Record<string, PresenceInfo>>> {
    const response = await fetch(`${API_BASE}/presence/bulk`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userIds }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to get user presence');
    }
    
    return response.json();
  },

  // Send typing indicator
  async sendTypingIndicator(request: TypingRequest): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/presence/typing`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to send typing indicator');
    }
    
    return response.json();
  },

  // Get online users in a channel
  async getOnlineUsers(channelId?: string): Promise<ApiResponse<User[]>> {
    const url = channelId 
      ? `${API_BASE}/presence/online?channelId=${channelId}`
      : `${API_BASE}/presence/online`;
      
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error('Failed to get online users');
    }
    
    return response.json();
  },

  // Get typing users in a channel
  async getTypingUsers(channelId: string): Promise<ApiResponse<User[]>> {
    const response = await fetch(`${API_BASE}/presence/typing/${channelId}`);
    
    if (!response.ok) {
      throw new Error('Failed to get typing users');
    }
    
    return response.json();
  },

  // Set user as away (auto-away after inactivity)
  async setAway(customMessage?: string): Promise<ApiResponse<void>> {
    return this.updatePresence({
      status: 'away',
      customMessage,
    });
  },

  // Set user as online
  async setOnline(): Promise<ApiResponse<void>> {
    return this.updatePresence({
      status: 'online',
    });
  },

  // Set user as busy
  async setBusy(customMessage?: string): Promise<ApiResponse<void>> {
    return this.updatePresence({
      status: 'busy',
      customMessage,
    });
  },

  // Set user as offline
  async setOffline(): Promise<ApiResponse<void>> {
    return this.updatePresence({
      status: 'offline',
    });
  },

  // Subscribe to presence updates for a channel
  subscribeToChannelPresence(channelId: string, callback: (users: User[]) => void): () => void {
    // This would typically use WebSocket or Server-Sent Events
    // For now, we'll use polling as a fallback
    const interval = setInterval(async () => {
      try {
        const response = await this.getOnlineUsers(channelId);
        if (response.success && response.data) {
          callback(response.data);
        }
      } catch (error) {
        console.error('Failed to fetch online users:', error);
      }
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  },

  // Subscribe to typing indicators for a channel
  subscribeToTypingIndicators(channelId: string, callback: (users: User[]) => void): () => void {
    // This would typically use WebSocket or Server-Sent Events
    // For now, we'll use polling as a fallback
    const interval = setInterval(async () => {
      try {
        const response = await this.getTypingUsers(channelId);
        if (response.success && response.data) {
          callback(response.data);
        }
      } catch (error) {
        console.error('Failed to fetch typing users:', error);
      }
    }, 2000); // Poll every 2 seconds for typing indicators

    return () => clearInterval(interval);
  },

  // Auto-away functionality
  startAutoAwayTimer(inactivityThreshold: number = 5 * 60 * 1000): () => void {
    let lastActivity = Date.now();
    let isAway = false;
    let awayTimer: NodeJS.Timeout;

    const resetTimer = () => {
      lastActivity = Date.now();
      
      if (isAway) {
        // User is back, set them as online
        this.setOnline().catch(console.error);
        isAway = false;
      }

      clearTimeout(awayTimer);
      awayTimer = setTimeout(() => {
        if (!isAway) {
          this.setAway('Away due to inactivity').catch(console.error);
          isAway = true;
        }
      }, inactivityThreshold);
    };

    // Listen for user activity
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    const throttledResetTimer = this.throttle(resetTimer, 1000); // Throttle to once per second

    activityEvents.forEach(event => {
      document.addEventListener(event, throttledResetTimer, true);
    });

    // Start the timer
    resetTimer();

    // Return cleanup function
    return () => {
      clearTimeout(awayTimer);
      activityEvents.forEach(event => {
        document.removeEventListener(event, throttledResetTimer, true);
      });
    };
  },

  // Utility function to throttle function calls
  throttle<T extends (...args: any[]) => any>(func: T, limit: number): T {
    let inThrottle: boolean;
    return ((...args: any[]) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }) as T;
  },

  // Get presence status color
  getPresenceColor(status: User['status']): string {
    switch (status) {
      case 'online':
        return '#10B981'; // green-500
      case 'away':
        return '#F59E0B'; // yellow-500
      case 'busy':
        return '#EF4444'; // red-500
      case 'offline':
      default:
        return '#6B7280'; // gray-500
    }
  },

  // Get presence status text
  getPresenceText(status: User['status']): string {
    switch (status) {
      case 'online':
        return 'Online';
      case 'away':
        return 'Away';
      case 'busy':
        return 'Busy';
      case 'offline':
      default:
        return 'Offline';
    }
  },

  // Check if user is active (online or away)
  isUserActive(status: User['status']): boolean {
    return status === 'online' || status === 'away';
  },

  // Format last seen time
  formatLastSeen(lastSeen?: Date): string {
    if (!lastSeen) return 'Never';
    
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return lastSeen.toLocaleDateString();
  },
};

// Voice/Video call service for Discuss module
import type { Call, CallParticipant } from '../types';
import type { ApiResponse } from '../../../types/api';

// Base API URL for call endpoints
const API_BASE = '/api/discuss/calls';

export interface StartCallRequest {
  type: 'voice' | 'video';
  channelId?: string;
  participantIds: string[];
}

export interface JoinCallRequest {
  callId: string;
  userId: string;
  mediaConstraints: {
    audio: boolean;
    video: boolean;
  };
}

export interface CallSettings {
  isMuted: boolean;
  isVideoEnabled: boolean;
  isScreenSharing: boolean;
}

export interface CallStats {
  duration: number;
  participantCount: number;
  quality: 'poor' | 'fair' | 'good' | 'excellent';
  bandwidth: {
    upload: number;
    download: number;
  };
}

export const callService = {
  // Start a new call
  async startCall(request: StartCallRequest): Promise<ApiResponse<Call>> {
    const response = await fetch(`${API_BASE}/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to start call');
    }
    
    return response.json();
  },

  // Join an existing call
  async joinCall(request: JoinCallRequest): Promise<ApiResponse<CallParticipant>> {
    const response = await fetch(`${API_BASE}/${request.callId}/join`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to join call');
    }
    
    return response.json();
  },

  // Leave a call
  async leaveCall(callId: string, userId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/${callId}/leave`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to leave call');
    }
    
    return response.json();
  },

  // End a call (only call initiator can do this)
  async endCall(callId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/${callId}/end`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to end call');
    }
    
    return response.json();
  },

  // Update call settings (mute/unmute, video on/off, screen sharing)
  async updateCallSettings(
    callId: string, 
    userId: string, 
    settings: Partial<CallSettings>
  ): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/${callId}/settings`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId, ...settings }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update call settings');
    }
    
    return response.json();
  },

  // Get call details
  async getCall(callId: string): Promise<ApiResponse<Call>> {
    const response = await fetch(`${API_BASE}/${callId}`);
    
    if (!response.ok) {
      throw new Error('Failed to get call details');
    }
    
    return response.json();
  },

  // Get call participants
  async getCallParticipants(callId: string): Promise<ApiResponse<CallParticipant[]>> {
    const response = await fetch(`${API_BASE}/${callId}/participants`);
    
    if (!response.ok) {
      throw new Error('Failed to get call participants');
    }
    
    return response.json();
  },

  // Get call history for a user or channel
  async getCallHistory(
    channelId?: string, 
    userId?: string, 
    page: number = 1, 
    pageSize: number = 20
  ): Promise<ApiResponse<Call[]>> {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
    });
    
    if (channelId) params.append('channelId', channelId);
    if (userId) params.append('userId', userId);
    
    const response = await fetch(`${API_BASE}/history?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to get call history');
    }
    
    return response.json();
  },

  // Start call recording
  async startRecording(callId: string): Promise<ApiResponse<{ recordingId: string }>> {
    const response = await fetch(`${API_BASE}/${callId}/recording/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to start recording');
    }
    
    return response.json();
  },

  // Stop call recording
  async stopRecording(callId: string, recordingId: string): Promise<ApiResponse<{ recordingUrl: string }>> {
    const response = await fetch(`${API_BASE}/${callId}/recording/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ recordingId }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to stop recording');
    }
    
    return response.json();
  },

  // Get call statistics
  async getCallStats(callId: string): Promise<ApiResponse<CallStats>> {
    const response = await fetch(`${API_BASE}/${callId}/stats`);
    
    if (!response.ok) {
      throw new Error('Failed to get call statistics');
    }
    
    return response.json();
  },
};

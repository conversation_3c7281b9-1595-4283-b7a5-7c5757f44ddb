// Notification service for managing desktop and mobile notifications
import type { ApiResponse, Message, User, Channel, NotificationSettings } from '../types';

const API_BASE = '/api/discuss';

export interface NotificationPayload {
  id: string;
  type: 'message' | 'mention' | 'reaction' | 'channel_invite' | 'direct_message';
  title: string;
  body: string;
  icon?: string;
  image?: string;
  badge?: string;
  tag?: string;
  data?: any;
  actions?: NotificationAction[];
  requireInteraction?: boolean;
  silent?: boolean;
  timestamp: Date;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export interface NotificationPreferences {
  desktop: boolean;
  sound: boolean;
  mentions: boolean;
  directMessages: boolean;
  channelMessages: boolean;
  reactions: boolean;
  soundFile?: string;
  quietHours?: {
    enabled: boolean;
    start: string; // HH:MM format
    end: string;   // HH:MM format
  };
}

export const notificationService = {
  // Request notification permission
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('This browser does not support notifications');
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission === 'denied') {
      return 'denied';
    }

    const permission = await Notification.requestPermission();
    return permission;
  },

  // Check if notifications are supported and permitted
  isNotificationSupported(): boolean {
    return 'Notification' in window;
  },

  isNotificationPermitted(): boolean {
    return this.isNotificationSupported() && Notification.permission === 'granted';
  },

  // Show desktop notification
  async showNotification(payload: NotificationPayload): Promise<Notification | null> {
    if (!this.isNotificationPermitted()) {
      console.warn('Notifications not permitted');
      return null;
    }

    try {
      const notification = new Notification(payload.title, {
        body: payload.body,
        icon: payload.icon || '/favicon.ico',
        image: payload.image,
        badge: payload.badge,
        tag: payload.tag,
        data: payload.data,
        requireInteraction: payload.requireInteraction,
        silent: payload.silent,
        timestamp: payload.timestamp.getTime(),
      });

      // Handle notification click
      notification.onclick = (event) => {
        event.preventDefault();
        window.focus();
        
        // Handle navigation based on notification type
        if (payload.data?.channelId) {
          // Navigate to channel
          window.location.hash = `#/discuss/channels/${payload.data.channelId}`;
        } else if (payload.data?.userId) {
          // Navigate to direct message
          window.location.hash = `#/discuss/dm/${payload.data.userId}`;
        }
        
        notification.close();
      };

      // Auto-close after 5 seconds unless requireInteraction is true
      if (!payload.requireInteraction) {
        setTimeout(() => {
          notification.close();
        }, 5000);
      }

      return notification;
    } catch (error) {
      console.error('Failed to show notification:', error);
      return null;
    }
  },

  // Play notification sound
  async playNotificationSound(soundFile?: string): Promise<void> {
    try {
      const audio = new Audio(soundFile || '/sounds/notification.mp3');
      audio.volume = 0.5;
      await audio.play();
    } catch (error) {
      console.warn('Failed to play notification sound:', error);
    }
  },

  // Create notification for new message
  async notifyNewMessage(
    message: Message,
    author: User,
    channel: Channel,
    preferences: NotificationPreferences
  ): Promise<void> {
    if (!preferences.desktop) return;

    const isDirectMessage = channel.type === 'direct';
    const isMention = message.mentions.length > 0;

    // Check if we should notify based on preferences
    if (isDirectMessage && !preferences.directMessages) return;
    if (!isDirectMessage && !isMention && !preferences.channelMessages) return;
    if (isMention && !preferences.mentions) return;

    // Check quiet hours
    if (this.isQuietHours(preferences.quietHours)) return;

    const payload: NotificationPayload = {
      id: `message-${message.id}`,
      type: isDirectMessage ? 'direct_message' : isMention ? 'mention' : 'message',
      title: isDirectMessage 
        ? `${author.name} sent you a message`
        : `${author.name} in #${channel.name}`,
      body: message.content.length > 100 
        ? message.content.substring(0, 100) + '...'
        : message.content,
      icon: author.avatar,
      tag: isDirectMessage ? `dm-${author.id}` : `channel-${channel.id}`,
      data: {
        messageId: message.id,
        channelId: channel.id,
        authorId: author.id,
        isDirectMessage,
        isMention,
      },
      timestamp: message.timestamp,
    };

    await this.showNotification(payload);

    if (preferences.sound) {
      await this.playNotificationSound(preferences.soundFile);
    }
  },

  // Create notification for reaction
  async notifyReaction(
    message: Message,
    reactor: User,
    emoji: string,
    channel: Channel,
    preferences: NotificationPreferences
  ): Promise<void> {
    if (!preferences.desktop || !preferences.reactions) return;
    if (this.isQuietHours(preferences.quietHours)) return;

    const payload: NotificationPayload = {
      id: `reaction-${message.id}-${reactor.id}`,
      type: 'reaction',
      title: `${reactor.name} reacted to your message`,
      body: `${emoji} "${message.content.substring(0, 50)}${message.content.length > 50 ? '...' : ''}"`,
      icon: reactor.avatar,
      tag: `reaction-${message.id}`,
      data: {
        messageId: message.id,
        channelId: channel.id,
        reactorId: reactor.id,
        emoji,
      },
      timestamp: new Date(),
    };

    await this.showNotification(payload);

    if (preferences.sound) {
      await this.playNotificationSound(preferences.soundFile);
    }
  },

  // Get notification settings
  async getNotificationSettings(): Promise<ApiResponse<NotificationSettings>> {
    const response = await fetch(`${API_BASE}/notifications/settings`);
    
    if (!response.ok) {
      throw new Error('Failed to get notification settings');
    }
    
    return response.json();
  },

  // Update notification settings
  async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<ApiResponse<NotificationSettings>> {
    const response = await fetch(`${API_BASE}/notifications/settings`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update notification settings');
    }
    
    return response.json();
  },

  // Mute/unmute channel notifications
  async muteChannel(channelId: string, duration?: number): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/notifications/mute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ channelId, duration }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to mute channel');
    }
    
    return response.json();
  },

  async unmuteChannel(channelId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/notifications/unmute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ channelId }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to unmute channel');
    }
    
    return response.json();
  },

  // Get muted channels
  async getMutedChannels(): Promise<ApiResponse<Array<{ channelId: string; mutedUntil?: Date }>>> {
    const response = await fetch(`${API_BASE}/notifications/muted`);
    
    if (!response.ok) {
      throw new Error('Failed to get muted channels');
    }
    
    return response.json();
  },

  // Mark notifications as read
  async markNotificationsAsRead(notificationIds: string[]): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/notifications/read`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ notificationIds }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to mark notifications as read');
    }
    
    return response.json();
  },

  // Get unread notification count
  async getUnreadCount(): Promise<ApiResponse<{ count: number; mentions: number }>> {
    const response = await fetch(`${API_BASE}/notifications/unread-count`);
    
    if (!response.ok) {
      throw new Error('Failed to get unread count');
    }
    
    return response.json();
  },

  // Check if current time is within quiet hours
  isQuietHours(quietHours?: NotificationPreferences['quietHours']): boolean {
    if (!quietHours?.enabled) return false;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMin] = quietHours.start.split(':').map(Number);
    const [endHour, endMin] = quietHours.end.split(':').map(Number);
    
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      // Same day range (e.g., 9:00 to 17:00)
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // Overnight range (e.g., 22:00 to 6:00)
      return currentTime >= startTime || currentTime <= endTime;
    }
  },

  // Clear all notifications
  clearAllNotifications(): void {
    if ('serviceWorker' in navigator && 'getNotifications' in ServiceWorkerRegistration.prototype) {
      navigator.serviceWorker.ready.then(registration => {
        registration.getNotifications().then(notifications => {
          notifications.forEach(notification => notification.close());
        });
      });
    }
  },

  // Test notification
  async testNotification(): Promise<void> {
    const payload: NotificationPayload = {
      id: 'test-notification',
      type: 'message',
      title: 'Test Notification',
      body: 'This is a test notification from Nexed Discuss',
      timestamp: new Date(),
    };

    await this.showNotification(payload);
  },
};

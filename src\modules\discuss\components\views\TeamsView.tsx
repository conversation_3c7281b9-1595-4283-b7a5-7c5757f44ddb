import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface TeamsViewProps {
  className?: string;
  'data-testid'?: string;
}

export const TeamsView: React.FC<TeamsViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const teams = [
    {
      id: 'frontend-team',
      name: 'Frontend Team',
      description: 'UI/UX development and design implementation',
      members: [
        { id: '1', name: '<PERSON>', role: 'Lead Developer', avatar: 'AJ', status: 'online' },
        { id: '2', name: '<PERSON>', role: 'React Developer', avatar: 'BS', status: 'away' },
        { id: '3', name: '<PERSON>', role: 'UI Designer', avatar: 'CD', status: 'online' },
      ],
      channels: ['#frontend-general', '#react-help', '#design-review'],
      activity: 'Active 2 hours ago',
    },
    {
      id: 'backend-team',
      name: 'Backend Team',
      description: 'Server-side development and API management',
      members: [
        { id: '4', name: '<PERSON>', role: 'Backend Lead', avatar: 'D<PERSON>', status: 'online' },
        { id: '5', name: 'Eva <PERSON>', role: 'API Developer', avatar: 'EB', status: 'offline' },
        { id: '6', name: 'Frank <PERSON>', role: 'DevOps Engineer', avatar: 'FM', status: 'online' },
      ],
      channels: ['#backend-general', '#api-discussions', '#deployment'],
      activity: 'Active 30 minutes ago',
    },
  ];

  return (
    <div
      className={`flex-1 overflow-y-auto p-6 ${className}`}
      data-testid={testId}
    >
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold mb-2" style={{ color: colors.text }}>
            Teams
          </h1>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Manage your teams and collaborate with team members
          </p>
        </div>

        {/* Teams Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {teams.map(team => (
            <div
              key={team.id}
              className="border rounded-lg p-6 hover:shadow-lg transition-shadow"
              style={{
                backgroundColor: colors.surface,
                borderColor: colors.border,
              }}
            >
              {/* Team Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-12 h-12 rounded-lg flex items-center justify-center text-white text-lg font-semibold"
                    style={{ backgroundColor: colors.primary }}
                  >
                    👥
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold" style={{ color: colors.text }}>
                      {team.name}
                    </h3>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      {team.activity}
                    </p>
                  </div>
                </div>
                <button
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  style={{ color: colors.textSecondary }}
                >
                  ⋯
                </button>
              </div>

              {/* Team Description */}
              <p className="text-sm mb-4" style={{ color: colors.text }}>
                {team.description}
              </p>

              {/* Team Members */}
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2" style={{ color: colors.text }}>
                  Members ({team.members.length})
                </h4>
                <div className="space-y-2">
                  {team.members.map(member => (
                    <div key={member.id} className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold">
                          {member.avatar}
                        </div>
                        <div
                          className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                            member.status === 'online'
                              ? 'bg-green-500'
                              : member.status === 'away'
                              ? 'bg-yellow-500'
                              : 'bg-gray-400'
                          }`}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
                          {member.name}
                        </p>
                        <p className="text-xs truncate" style={{ color: colors.textSecondary }}>
                          {member.role}
                        </p>
                      </div>
                      <button
                        className="text-xs px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                        style={{ color: colors.textSecondary }}
                      >
                        💬
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Team Channels */}
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2" style={{ color: colors.text }}>
                  Channels
                </h4>
                <div className="flex flex-wrap gap-2">
                  {team.channels.map(channel => (
                    <span
                      key={channel}
                      className="text-xs px-2 py-1 rounded-full cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600"
                      style={{
                        backgroundColor: colors.backgroundSecondary,
                        color: colors.textSecondary,
                      }}
                    >
                      {channel}
                    </span>
                  ))}
                </div>
              </div>

              {/* Team Actions */}
              <div className="flex items-center space-x-2 pt-4 border-t" style={{ borderTopColor: colors.border }}>
                <button
                  className="flex-1 px-3 py-2 text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  style={{ color: colors.text }}
                >
                  📢 Join Team
                </button>
                <button
                  className="flex-1 px-3 py-2 text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  style={{ color: colors.text }}
                >
                  👥 View Members
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Create Team Button */}
        <div className="mt-8 text-center">
          <button
            className="px-6 py-3 rounded-lg font-medium"
            style={{
              backgroundColor: colors.primary,
              color: 'white',
            }}
          >
            ➕ Create New Team
          </button>
        </div>

        {/* Team Statistics */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div
            className="text-center p-6 rounded-lg"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
            }}
          >
            <div className="text-2xl font-bold mb-2" style={{ color: colors.text }}>
              {teams.length}
            </div>
            <div className="text-sm" style={{ color: colors.textSecondary }}>
              Active Teams
            </div>
          </div>
          <div
            className="text-center p-6 rounded-lg"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
            }}
          >
            <div className="text-2xl font-bold mb-2" style={{ color: colors.text }}>
              {teams.reduce((total, team) => total + team.members.length, 0)}
            </div>
            <div className="text-sm" style={{ color: colors.textSecondary }}>
              Total Members
            </div>
          </div>
          <div
            className="text-center p-6 rounded-lg"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
            }}
          >
            <div className="text-2xl font-bold mb-2" style={{ color: colors.text }}>
              {teams.reduce((total, team) => total + team.channels.length, 0)}
            </div>
            <div className="text-sm" style={{ color: colors.textSecondary }}>
              Team Channels
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

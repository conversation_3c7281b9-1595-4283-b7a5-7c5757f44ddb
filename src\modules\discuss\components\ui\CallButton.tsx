import React, { useState } from 'react';
import { useCall } from '../../hooks/useCall';

export interface CallButtonProps {
  type: 'voice' | 'video';
  channelId?: string;
  participantIds: string[];
  currentUserId: string;
  onCallStarted?: (callId: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
  disabled?: boolean;
}

export const CallButton: React.FC<CallButtonProps> = ({
  type,
  channelId,
  participantIds,
  currentUserId,
  onCallStarted,
  className = '',
  size = 'md',
  variant = 'primary',
  disabled = false,
}) => {
  const [isStarting, setIsStarting] = useState(false);
  const { startCall } = useCall({ userId: currentUserId, channelId });

  const handleStartCall = async () => {
    if (disabled || isStarting) return;

    try {
      setIsStarting(true);
      const call = await startCall(type, participantIds);
      onCallStarted?.(call.id);
    } catch (error) {
      console.error('Failed to start call:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'p-1.5';
      case 'lg':
        return 'p-4';
      default:
        return 'p-2.5';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-8 h-8';
      default:
        return 'w-5 h-5';
    }
  };

  const getVariantClasses = () => {
    if (disabled) {
      return 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed';
    }

    switch (variant) {
      case 'secondary':
        return 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300';
      case 'ghost':
        return 'bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400';
      default:
        return type === 'video' 
          ? 'bg-blue-500 hover:bg-blue-600 text-white'
          : 'bg-green-500 hover:bg-green-600 text-white';
    }
  };

  const getIcon = () => {
    if (isStarting) {
      return (
        <svg className={`${getIconSize()} animate-spin`} fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      );
    }

    if (type === 'video') {
      return (
        <svg className={getIconSize()} fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
        </svg>
      );
    }

    return (
      <svg className={getIconSize()} fill="currentColor" viewBox="0 0 20 20">
        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
      </svg>
    );
  };

  const getTooltip = () => {
    if (disabled) {
      return 'Call not available';
    }
    return `Start ${type} call`;
  };

  return (
    <button
      onClick={handleStartCall}
      disabled={disabled || isStarting}
      className={`
        ${getSizeClasses()}
        ${getVariantClasses()}
        rounded-full
        transition-colors
        duration-200
        focus:outline-none
        focus:ring-2
        focus:ring-offset-2
        focus:ring-blue-500
        ${className}
      `}
      title={getTooltip()}
      aria-label={getTooltip()}
    >
      {getIcon()}
    </button>
  );
};

// Preset components for common use cases
export const VoiceCallButton: React.FC<Omit<CallButtonProps, 'type'>> = (props) => (
  <CallButton {...props} type="voice" />
);

export const VideoCallButton: React.FC<Omit<CallButtonProps, 'type'>> = (props) => (
  <CallButton {...props} type="video" />
);

// Call button group for showing both voice and video options
export interface CallButtonGroupProps {
  channelId?: string;
  participantIds: string[];
  currentUserId: string;
  onCallStarted?: (callId: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
  disabled?: boolean;
  showLabels?: boolean;
}

export const CallButtonGroup: React.FC<CallButtonGroupProps> = ({
  channelId,
  participantIds,
  currentUserId,
  onCallStarted,
  className = '',
  size = 'md',
  variant = 'primary',
  disabled = false,
  showLabels = false,
}) => {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <VoiceCallButton
        channelId={channelId}
        participantIds={participantIds}
        currentUserId={currentUserId}
        onCallStarted={onCallStarted}
        size={size}
        variant={variant}
        disabled={disabled}
      />
      <VideoCallButton
        channelId={channelId}
        participantIds={participantIds}
        currentUserId={currentUserId}
        onCallStarted={onCallStarted}
        size={size}
        variant={variant}
        disabled={disabled}
      />
      {showLabels && (
        <div className="flex flex-col text-xs text-gray-500 dark:text-gray-400 ml-2">
          <span>Voice</span>
          <span>Video</span>
        </div>
      )}
    </div>
  );
};

// Dropdown call button with options
export interface CallDropdownProps {
  channelId?: string;
  participantIds: string[];
  currentUserId: string;
  onCallStarted?: (callId: string) => void;
  className?: string;
  disabled?: boolean;
}

export const CallDropdown: React.FC<CallDropdownProps> = ({
  channelId,
  participantIds,
  currentUserId,
  onCallStarted,
  className = '',
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          p-2.5 rounded-full transition-colors duration-200
          focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
          ${disabled 
            ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
            : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
          }
        `}
        title="Call options"
      >
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
        </svg>
      </button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-20 border border-gray-200 dark:border-gray-700">
            <div className="py-1">
              <VoiceCallButton
                channelId={channelId}
                participantIds={participantIds}
                currentUserId={currentUserId}
                onCallStarted={(callId) => {
                  onCallStarted?.(callId);
                  setIsOpen(false);
                }}
                variant="ghost"
                className="w-full justify-start px-4 py-2 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700"
                disabled={disabled}
              />
              <VideoCallButton
                channelId={channelId}
                participantIds={participantIds}
                currentUserId={currentUserId}
                onCallStarted={(callId) => {
                  onCallStarted?.(callId);
                  setIsOpen(false);
                }}
                variant="ghost"
                className="w-full justify-start px-4 py-2 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700"
                disabled={disabled}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

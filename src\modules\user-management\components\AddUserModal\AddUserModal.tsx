import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { Button } from '../../../../components/ui';
import { UserForm } from '../UserForm';
import { useUserActions } from '../../hooks';
import type { UserFormData } from '../../types';

export interface AddUserModalProps {
  onUserAdded?: () => void;
  className?: string;
  'data-testid'?: string;
}

export const AddUserModal: React.FC<AddUserModalProps> = ({
  onUserAdded,
  className = '',
  'data-testid': testId,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { colors } = useThemeStore();
  const { createUser, isLoading } = useUserActions();

  const handleSubmit = async (formData: UserFormData) => {
    try {
      await createUser({
        name: formData.name,
        email: formData.email,
        role: formData.role,
        sendInvitation: true,
      });
      
      setIsOpen(false);
      onUserAdded?.();
    } catch (error) {
      // Error is handled by the form component
      throw error;
    }
  };

  const handleCancel = () => {
    setIsOpen(false);
  };

  return (
    <div className={`mb-6 ${className}`} data-testid={testId}>
      {!isOpen ? (
        <Button
          onClick={() => setIsOpen(true)}
          variant="primary"
          className="inline-flex items-center"
          data-testid="add-user-trigger"
        >
          <span className="mr-2">+</span>
          Add New User
        </Button>
      ) : (
        <div className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
              Add New User
            </h3>
            <button
              onClick={handleCancel}
              className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
              data-testid="add-user-close"
            >
              ✕
            </button>
          </div>

          <UserForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            submitLabel="Add User"
            data-testid="add-user-form"
          />
        </div>
      )}
    </div>
  );
};

/**
 * App Component Registrations
 * 
 * This file registers all app-specific components with the component registry.
 * Import this file early in your application to ensure all components are registered.
 */

import { componentRegistry } from './componentRegistry';
import { DiscussModule } from '../modules/discuss';

/**
 * Initialize all app component registrations
 * Call this function early in your application startup
 */
export function initializeAppRegistrations(): void {
  // Register Discuss Module for app ID 10
  componentRegistry.register('10', DiscussModule);

  // Future app registrations can be added here
  // Example:
  // componentRegistry.register('11', CustomSalesModule);
  // componentRegistry.register('12', CustomInventoryModule);

  console.log(`Initialized ${componentRegistry.size()} app component registrations`);
}

/**
 * Get all registered app IDs for debugging
 */
export function getRegisteredApps(): string[] {
  return componentRegistry.getRegisteredAppIds();
}

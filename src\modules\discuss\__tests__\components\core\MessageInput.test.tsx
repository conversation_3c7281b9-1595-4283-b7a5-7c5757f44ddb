import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MessageInput } from '../../../components/core/MessageInput';

// Mock the theme store
vi.mock('../../../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#3b82f6',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      backgroundSecondary: '#f1f5f9',
      error: '#ef4444',
    },
  }),
}));

// Mock UI components
vi.mock('../../../components/ui', () => ({
  RichTextEditor: ({ value, onChange, onSubmit, placeholder }: any) => (
    <div data-testid="rich-text-editor">
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            onSubmit();
          }
        }}
        placeholder={placeholder}
        data-testid="message-textarea"
      />
    </div>
  ),
  FileUpload: ({ onFileSelect }: any) => (
    <button
      data-testid="file-upload"
      onClick={() => onFileSelect([{ id: '1', name: 'test.jpg', size: 1024, type: 'image' }])}
    >
      Upload File
    </button>
  ),
  EmojiPicker: ({ onEmojiSelect }: any) => (
    <button
      data-testid="emoji-picker"
      onClick={() => onEmojiSelect('😀')}
    >
      😀
    </button>
  ),
  MentionSuggestions: ({ onMentionSelect }: any) => (
    <div data-testid="mention-suggestions">
      <button onClick={() => onMentionSelect({ id: '1', username: 'john' })}>
        @john
      </button>
    </div>
  ),
}));

describe('MessageInput Component', () => {
  const defaultProps = {
    onSendMessage: vi.fn(),
    placeholder: 'Type a message...',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders message input correctly', () => {
    render(<MessageInput {...defaultProps} />);
    
    expect(screen.getByTestId('rich-text-editor')).toBeInTheDocument();
    expect(screen.getByTestId('message-textarea')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument();
  });

  it('handles text input', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'Hello world!');
    
    expect(textarea).toHaveValue('Hello world!');
  });

  it('sends message on Enter key', async () => {
    const onSendMessage = vi.fn();
    const user = userEvent.setup();
    
    render(<MessageInput {...defaultProps} onSendMessage={onSendMessage} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'Hello world!');
    await user.keyboard('{Enter}');
    
    expect(onSendMessage).toHaveBeenCalledWith({
      content: 'Hello world!',
      attachments: [],
      mentions: [],
    });
  });

  it('does not send empty messages', async () => {
    const onSendMessage = vi.fn();
    const user = userEvent.setup();
    
    render(<MessageInput {...defaultProps} onSendMessage={onSendMessage} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.keyboard('{Enter}');
    
    expect(onSendMessage).not.toHaveBeenCalled();
  });

  it('allows new line with Shift+Enter', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'Line 1');
    await user.keyboard('{Shift>}{Enter}{/Shift}');
    await user.type(textarea, 'Line 2');
    
    expect(textarea).toHaveValue('Line 1\nLine 2');
  });

  it('shows send button when message is not empty', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'Hello');
    
    expect(screen.getByTitle('Send message')).toBeInTheDocument();
  });

  it('handles file upload', async () => {
    const onSendMessage = vi.fn();
    render(<MessageInput {...defaultProps} onSendMessage={onSendMessage} />);
    
    const fileUploadButton = screen.getByTestId('file-upload');
    fireEvent.click(fileUploadButton);
    
    await waitFor(() => {
      expect(screen.getByText('test.jpg')).toBeInTheDocument();
    });
  });

  it('sends message with attachments', async () => {
    const onSendMessage = vi.fn();
    const user = userEvent.setup();
    
    render(<MessageInput {...defaultProps} onSendMessage={onSendMessage} />);
    
    // Add file
    const fileUploadButton = screen.getByTestId('file-upload');
    fireEvent.click(fileUploadButton);
    
    // Add text and send
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'Check this file!');
    await user.keyboard('{Enter}');
    
    expect(onSendMessage).toHaveBeenCalledWith({
      content: 'Check this file!',
      attachments: [{ id: '1', name: 'test.jpg', size: 1024, type: 'image' }],
      mentions: [],
    });
  });

  it('handles emoji selection', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);
    
    const emojiButton = screen.getByTestId('emoji-picker');
    fireEvent.click(emojiButton);
    
    const textarea = screen.getByTestId('message-textarea');
    expect(textarea).toHaveValue('😀');
  });

  it('handles mention suggestions', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, '@j');
    
    // Mention suggestions should appear
    expect(screen.getByTestId('mention-suggestions')).toBeInTheDocument();
    
    const mentionButton = screen.getByText('@john');
    fireEvent.click(mentionButton);
    
    expect(textarea).toHaveValue('@john ');
  });

  it('handles typing indicators', async () => {
    const onTyping = vi.fn();
    const user = userEvent.setup();
    
    render(<MessageInput {...defaultProps} onTyping={onTyping} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'Hello');
    
    expect(onTyping).toHaveBeenCalledWith(true);
    
    // Stop typing after delay
    await waitFor(() => {
      expect(onTyping).toHaveBeenCalledWith(false);
    }, { timeout: 3000 });
  });

  it('clears input after sending message', async () => {
    const onSendMessage = vi.fn();
    const user = userEvent.setup();
    
    render(<MessageInput {...defaultProps} onSendMessage={onSendMessage} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'Hello world!');
    await user.keyboard('{Enter}');
    
    expect(textarea).toHaveValue('');
  });

  it('shows character count when approaching limit', async () => {
    const user = userEvent.setup();
    render(<MessageInput {...defaultProps} maxLength={50} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'This is a long message that approaches the limit');
    
    expect(screen.getByText(/\d+\/50/)).toBeInTheDocument();
  });

  it('prevents sending when over character limit', async () => {
    const onSendMessage = vi.fn();
    const user = userEvent.setup();
    
    render(<MessageInput {...defaultProps} onSendMessage={onSendMessage} maxLength={10} />);
    
    const textarea = screen.getByTestId('message-textarea');
    await user.type(textarea, 'This message is too long');
    await user.keyboard('{Enter}');
    
    expect(onSendMessage).not.toHaveBeenCalled();
  });

  it('handles disabled state', () => {
    render(<MessageInput {...defaultProps} disabled={true} />);
    
    const textarea = screen.getByTestId('message-textarea');
    expect(textarea).toBeDisabled();
  });

  it('shows loading state when sending', () => {
    render(<MessageInput {...defaultProps} isLoading={true} />);
    
    expect(screen.getByText('Sending...')).toBeInTheDocument();
  });

  it('handles reply mode', () => {
    const replyToMessage = {
      id: 'msg-1',
      content: 'Original message',
      authorId: '2',
      channelId: 'general',
      timestamp: new Date(),
      reactions: [],
      attachments: [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'read' as const,
    };

    render(<MessageInput {...defaultProps} replyTo={replyToMessage} />);
    
    expect(screen.getByText('Replying to:')).toBeInTheDocument();
    expect(screen.getByText('Original message')).toBeInTheDocument();
  });

  it('cancels reply mode', () => {
    const onCancelReply = vi.fn();
    const replyToMessage = {
      id: 'msg-1',
      content: 'Original message',
      authorId: '2',
      channelId: 'general',
      timestamp: new Date(),
      reactions: [],
      attachments: [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'read' as const,
    };

    render(
      <MessageInput
        {...defaultProps}
        replyTo={replyToMessage}
        onCancelReply={onCancelReply}
      />
    );
    
    const cancelButton = screen.getByTitle('Cancel reply');
    fireEvent.click(cancelButton);
    
    expect(onCancelReply).toHaveBeenCalled();
  });

  it('handles edit mode', () => {
    const editMessage = {
      id: 'msg-1',
      content: 'Original message',
      authorId: '1',
      channelId: 'general',
      timestamp: new Date(),
      reactions: [],
      attachments: [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'read' as const,
    };

    render(<MessageInput {...defaultProps} editMessage={editMessage} />);
    
    const textarea = screen.getByTestId('message-textarea');
    expect(textarea).toHaveValue('Original message');
    expect(screen.getByText('Editing message')).toBeInTheDocument();
  });
});

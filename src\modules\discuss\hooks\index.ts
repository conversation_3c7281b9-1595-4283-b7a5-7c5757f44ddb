// Discuss module custom hooks
// These provide reusable logic for the discuss module components

// Message hooks
export { useMessages } from './useMessages';
export type {
  UseMessagesOptions,
  UseMessagesReturn
} from './useMessages';

// Real-time hooks
export { useWebSocket } from './useWebSocket';
export type {
  UseWebSocketOptions,
  UseWebSocketReturn
} from './useWebSocket';

export { useTypingIndicator } from './useTypingIndicator';
export type {
  TypingUser,
  UseTypingIndicatorOptions,
  UseTypingIndicatorReturn
} from './useTypingIndicator';

// Channel hooks
// export { useChannels } from './useChannels';
// export { useChannel } from './useChannel';
// export { useChannelMembers } from './useChannelMembers';

// User hooks
// export { useUsers } from './useUsers';
// export { useUserPresence } from './useUserPresence';
// export { useCurrentUser } from './useCurrentUser';

// Notification hooks
export { useNotifications, useReadReceipts } from './useNotifications';
export type {
  UseNotificationsOptions,
  UseNotificationsReturn,
  UseReadReceiptsOptions,
  UseReadReceiptsReturn
} from './useNotifications';

// File hooks
// export { useFileUpload } from './useFileUpload';
// export { useFileDownload } from './useFileDownload';

// Search hooks
// export { useSearch } from './useSearch';
// export { useSearchHistory } from './useSearchHistory';

// TODO: Implement remaining hooks

import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { Message, User } from '../../types';

export interface Task {
  id: string;
  title: string;
  description: string;
  assigneeId?: string;
  dueDate?: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'todo' | 'in_progress' | 'review' | 'done';
  createdBy: string;
  createdAt: Date;
  messageId?: string;
  channelId?: string;
}

export interface MessageToTaskProps {
  message: Message;
  author: User;
  onCreateTask: (task: Omit<Task, 'id' | 'createdAt'>) => void;
  onCancel: () => void;
  availableUsers: User[];
  className?: string;
  'data-testid'?: string;
}

export const MessageToTask: React.FC<MessageToTaskProps> = ({
  message,
  author,
  onCreateTask,
  onCancel,
  availableUsers,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState(message.content);
  const [assigneeId, setAssigneeId] = useState<string>('');
  const [dueDate, setDueDate] = useState('');
  const [priority, setPriority] = useState<Task['priority']>('medium');

  const handleSubmit = () => {
    if (title.trim().length === 0) return;

    onCreateTask({
      title: title.trim(),
      description: description.trim(),
      assigneeId: assigneeId || undefined,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      priority,
      status: 'todo',
      createdBy: '1', // Current user ID
      messageId: message.id,
      channelId: message.channelId,
    });
  };

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'low': return '#10b981';
      case 'medium': return '#f59e0b';
      case 'high': return '#f97316';
      case 'urgent': return '#ef4444';
      default: return colors.textSecondary;
    }
  };

  return (
    <div
      className={`border rounded-lg p-4 ${className}`}
      style={{
        borderColor: colors.border,
        backgroundColor: colors.background,
      }}
      data-testid={testId}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center space-x-2" style={{ color: colors.text }}>
            <span>📋</span>
            <span>Create Task from Message</span>
          </h3>
          <button
            onClick={onCancel}
            className="text-sm px-3 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            style={{ color: colors.textSecondary }}
          >
            Cancel
          </button>
        </div>

        {/* Original Message */}
        <div
          className="p-3 border rounded-lg"
          style={{
            borderColor: colors.border,
            backgroundColor: colors.backgroundSecondary,
          }}
        >
          <div className="flex items-start space-x-2 mb-2">
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold"
              style={{ backgroundColor: colors.primary }}
            >
              {author.name.charAt(0)}
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <span className="font-medium text-sm" style={{ color: colors.text }}>
                  {author.name}
                </span>
                <span className="text-xs" style={{ color: colors.textSecondary }}>
                  {new Date(message.timestamp).toLocaleString()}
                </span>
              </div>
              <p className="text-sm" style={{ color: colors.text }}>
                {message.content}
              </p>
            </div>
          </div>
        </div>

        {/* Task Form */}
        <div className="space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
              Task Title *
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter task title..."
              className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none"
              style={{
                borderColor: colors.border,
                color: colors.text,
              }}
              maxLength={100}
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Task description..."
              rows={3}
              className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none resize-none"
              style={{
                borderColor: colors.border,
                color: colors.text,
              }}
              maxLength={500}
            />
          </div>

          {/* Assignee */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
              Assignee
            </label>
            <select
              value={assigneeId}
              onChange={(e) => setAssigneeId(e.target.value)}
              className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none"
              style={{
                borderColor: colors.border,
                color: colors.text,
              }}
            >
              <option value="">Unassigned</option>
              {availableUsers.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name}
                </option>
              ))}
            </select>
          </div>

          {/* Due Date and Priority */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
                Due Date
              </label>
              <input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none"
                style={{
                  borderColor: colors.border,
                  color: colors.text,
                }}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2" style={{ color: colors.text }}>
                Priority
              </label>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value as Task['priority'])}
                className="w-full px-3 py-2 border rounded-lg bg-transparent outline-none"
                style={{
                  borderColor: colors.border,
                  color: colors.text,
                }}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t" style={{ borderTopColor: colors.border }}>
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            style={{ color: colors.textSecondary }}
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={title.trim().length === 0}
            className="px-4 py-2 text-sm rounded-lg text-white disabled:opacity-50 transition-colors"
            style={{ backgroundColor: colors.primary }}
          >
            Create Task
          </button>
        </div>
      </div>
    </div>
  );
};

export interface TaskDisplayProps {
  task: Task;
  assignee?: User;
  onUpdateStatus: (taskId: string, status: Task['status']) => void;
  onViewTask: (taskId: string) => void;
  className?: string;
  'data-testid'?: string;
}

export const TaskDisplay: React.FC<TaskDisplayProps> = ({
  task,
  assignee,
  onUpdateStatus,
  onViewTask,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'low': return '#10b981';
      case 'medium': return '#f59e0b';
      case 'high': return '#f97316';
      case 'urgent': return '#ef4444';
      default: return colors.textSecondary;
    }
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'todo': return colors.textSecondary;
      case 'in_progress': return '#3b82f6';
      case 'review': return '#f59e0b';
      case 'done': return '#10b981';
      default: return colors.textSecondary;
    }
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'todo': return '⏳';
      case 'in_progress': return '🔄';
      case 'review': return '👀';
      case 'done': return '✅';
      default: return '📋';
    }
  };

  const isOverdue = task.dueDate && new Date() > task.dueDate && task.status !== 'done';

  return (
    <div
      className={`border rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${className}`}
      style={{
        borderColor: colors.border,
        backgroundColor: colors.backgroundSecondary,
      }}
      onClick={() => onViewTask(task.id)}
      data-testid={testId}
    >
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg">📋</span>
            <span className="font-medium text-sm" style={{ color: colors.text }}>
              Task
            </span>
            <div
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: getPriorityColor(task.priority) }}
              title={`${task.priority} priority`}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <span
              className="text-xs px-2 py-1 rounded"
              style={{
                backgroundColor: getStatusColor(task.status) + '20',
                color: getStatusColor(task.status),
              }}
            >
              {getStatusIcon(task.status)} {task.status.replace('_', ' ')}
            </span>
          </div>
        </div>

        {/* Content */}
        <div>
          <h4 className="font-medium mb-1" style={{ color: colors.text }}>
            {task.title}
          </h4>
          {task.description && (
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              {task.description.length > 100 
                ? `${task.description.substring(0, 100)}...` 
                : task.description}
            </p>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between text-xs" style={{ color: colors.textSecondary }}>
          <div className="flex items-center space-x-3">
            {assignee && (
              <div className="flex items-center space-x-1">
                <span>👤</span>
                <span>{assignee.name}</span>
              </div>
            )}
            
            {task.dueDate && (
              <div className={`flex items-center space-x-1 ${isOverdue ? 'text-red-500' : ''}`}>
                <span>📅</span>
                <span>
                  {task.dueDate.toLocaleDateString()}
                  {isOverdue && ' (Overdue)'}
                </span>
              </div>
            )}
          </div>

          {/* Quick Status Update */}
          <div className="flex items-center space-x-1">
            {task.status !== 'done' && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  const nextStatus = task.status === 'todo' ? 'in_progress' : 
                                   task.status === 'in_progress' ? 'review' : 'done';
                  onUpdateStatus(task.id, nextStatus);
                }}
                className="px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                style={{ color: colors.primary }}
                title="Move to next status"
              >
                ▶️
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

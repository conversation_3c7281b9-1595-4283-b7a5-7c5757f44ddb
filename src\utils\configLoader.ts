import type {
  FeatureFlags,
  AppConfiguration,
  AppSettings,
} from '../stores/appStore';
import { mergeAppConfiguration } from '../stores/appStore';
import { getAppConfig, type AppConfig } from '../app-conf';

// Re-export AppConfig type from app-conf.ts for backward compatibility
export type { AppConfig } from '../app-conf';

/**
 * Loads the TypeScript configuration
 * @returns Configuration object
 */
export function loadConfig(): AppConfig {
  try {
    const config = getAppConfig();
    console.log('Configuration loaded successfully from TypeScript config');
    return config;
  } catch (error) {
    console.error('Error loading configuration:', error);
    // Return a minimal config to prevent app crashes
    return getAppConfig();
  }
}

/**
 * Transforms YAML config to App Store format
 * @param config - Raw YAML configuration
 * @returns Configuration in App Store format
 */
export function transformConfigToAppStore(config: AppConfig) {
  const featureFlags: Partial<FeatureFlags> = {
    ...config.features,
    enableDarkMode: config.ui?.allowThemeToggle ?? true,
  };

  const configuration: Partial<AppConfiguration> = {
    appName: config.app?.name,
    version: config.app?.version,
    environment: config.app?.environment,
    apiBaseUrl: config.api?.baseUrl,
    maxFileUploadSize: config.uploads?.maxFileSize,
    sessionTimeout: config.security?.sessionTimeout,
    defaultLanguage: config.localization?.defaultLanguage,
    supportedLanguages: config.localization?.supportedLanguages,
    dateFormat: config.localization?.dateFormat,
    timeFormat: config.localization?.timeFormat,
    timezone: config.localization?.timezone,
    itemsPerPage: config.ui?.itemsPerPage,
    maxRetries: config.api?.retries,
    retryDelay: config.api?.retryDelay,
  };

  const settings: Partial<AppSettings> = {
    sidebarCollapsed: config.ui?.sidebarCollapsed,
    compactMode: config.ui?.compactMode,
    showTooltips: config.ui?.showTooltips,
    autoSave: true, // Default to true
    autoSaveInterval: 30000, // Default 30 seconds
    soundEnabled: config.notifications?.enableSound,
    animationsEnabled: config.ui?.animationsEnabled,
    highContrastMode: config.ui?.highContrastMode,
    reducedMotion: config.ui?.reducedMotion,
  };

  return mergeAppConfiguration({
    featureFlags,
    configuration,
    settings,
  } as any);
}

/**
 * Loads configuration and initializes the app store
 * @returns Transformed configuration for the app store
 */
export function initializeAppConfig() {
  try {
    const config = loadConfig();
    const appStoreConfig = transformConfigToAppStore(config);

    console.log('App configuration initialized:', appStoreConfig);
    return appStoreConfig;
  } catch (error) {
    console.error('Failed to initialize app configuration:', error);
    return mergeAppConfiguration({}); // Return defaults
  }
}

/**
 * Validates configuration values
 * @param config - Configuration to validate
 * @returns Validation result
 */
export function validateConfig(config: AppConfig): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate environment
  if (
    config.app?.environment &&
    !['development', 'staging', 'production'].includes(config.app.environment)
  ) {
    errors.push(
      'Invalid environment value. Must be development, staging, or production.'
    );
  }

  // Validate file upload size
  if (config.uploads?.maxFileSize && config.uploads.maxFileSize < 0) {
    errors.push('Max file upload size must be a positive number.');
  }

  // Validate session timeout
  if (
    config.security?.sessionTimeout &&
    config.security.sessionTimeout < 60000
  ) {
    errors.push('Session timeout must be at least 60 seconds (60000ms).');
  }

  // Validate items per page
  if (
    config.ui?.itemsPerPage &&
    (config.ui.itemsPerPage < 1 || config.ui.itemsPerPage > 1000)
  ) {
    errors.push('Items per page must be between 1 and 1000.');
  }

  // Validate supported languages
  if (
    config.localization?.supportedLanguages &&
    config.localization.supportedLanguages.length === 0
  ) {
    errors.push('At least one supported language must be specified.');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Environment overrides are now handled in app-conf.ts

import React, { useState, useRef } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { Channel } from '../../types';

export interface ChannelFolder {
  id: string;
  name: string;
  icon?: string;
  color?: string;
  channelIds: string[];
  isCollapsed?: boolean;
  isCustom?: boolean;
}

export interface ChannelOrganizerProps {
  channels: Channel[];
  folders: ChannelFolder[];
  onFolderCreate: (name: string, channelIds: string[]) => void;
  onFolderUpdate: (folderId: string, updates: Partial<ChannelFolder>) => void;
  onFolderDelete: (folderId: string) => void;
  onChannelMove: (channelId: string, fromFolderId: string | null, toFolderId: string | null) => void;
  onChannelClick: (channel: Channel) => void;
  currentChannelId?: string;
  className?: string;
  'data-testid'?: string;
}

export const ChannelOrganizer: React.FC<ChannelOrganizerProps> = ({
  channels,
  folders,
  onFolderCreate,
  onFolderUpdate,
  onFolderDelete,
  onChannelMove,
  onChannelClick,
  currentChannelId,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [draggedChannel, setDraggedChannel] = useState<string | null>(null);
  const [dragOverFolder, setDragOverFolder] = useState<string | null>(null);
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);

  const getUnorganizedChannels = () => {
    const organizedChannelIds = new Set(
      folders.flatMap(folder => folder.channelIds)
    );
    return channels.filter(channel => !organizedChannelIds.has(channel.id));
  };

  const getChannelsByFolder = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId);
    if (!folder) return [];
    return folder.channelIds
      .map(id => channels.find(c => c.id === id))
      .filter(Boolean) as Channel[];
  };

  const handleDragStart = (e: React.DragEvent, channelId: string) => {
    setDraggedChannel(channelId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, folderId: string | null) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverFolder(folderId);
  };

  const handleDrop = (e: React.DragEvent, folderId: string | null) => {
    e.preventDefault();
    if (!draggedChannel) return;

    // Find current folder of the dragged channel
    const currentFolder = folders.find(f => f.channelIds.includes(draggedChannel));
    const currentFolderId = currentFolder?.id || null;

    if (currentFolderId !== folderId) {
      onChannelMove(draggedChannel, currentFolderId, folderId);
    }

    setDraggedChannel(null);
    setDragOverFolder(null);
  };

  const handleCreateFolder = () => {
    if (newFolderName.trim() && selectedChannels.length > 0) {
      onFolderCreate(newFolderName.trim(), selectedChannels);
      setNewFolderName('');
      setSelectedChannels([]);
      setShowCreateFolder(false);
    }
  };

  const toggleChannelSelection = (channelId: string) => {
    setSelectedChannels(prev =>
      prev.includes(channelId)
        ? prev.filter(id => id !== channelId)
        : [...prev, channelId]
    );
  };

  const renderChannel = (channel: Channel, inFolder = false) => {
    const isSelected = selectedChannels.includes(channel.id);
    const isCurrent = currentChannelId === channel.id;
    const isDragging = draggedChannel === channel.id;

    return (
      <div
        key={channel.id}
        draggable
        onDragStart={(e) => handleDragStart(e, channel.id)}
        onClick={() => {
          if (showCreateFolder) {
            toggleChannelSelection(channel.id);
          } else {
            onChannelClick(channel);
          }
        }}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg cursor-pointer transition-all ${
          isDragging ? 'opacity-50' : ''
        } ${
          isCurrent ? 'ring-2 ring-opacity-50' : 'hover:bg-gray-100 dark:hover:bg-gray-700'
        } ${
          isSelected ? 'bg-blue-100 dark:bg-blue-900' : ''
        }`}
        style={{
          backgroundColor: isCurrent ? `${colors.primary}20` : isSelected ? `${colors.primary}10` : 'transparent',
          ringColor: isCurrent ? colors.primary : 'transparent',
        }}
      >
        {showCreateFolder && (
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => toggleChannelSelection(channel.id)}
            className="mr-2"
          />
        )}
        
        <div className="text-sm">
          {channel.type === 'private' ? '🔒' : '#'}
        </div>
        
        <span className="flex-1 text-sm truncate" style={{ color: colors.text }}>
          {channel.name}
        </span>
        
        {channel.unreadCount && channel.unreadCount > 0 && (
          <div
            className="text-xs px-2 py-1 rounded-full text-white"
            style={{ backgroundColor: colors.primary }}
          >
            {channel.unreadCount > 99 ? '99+' : channel.unreadCount}
          </div>
        )}
      </div>
    );
  };

  const renderFolder = (folder: ChannelFolder) => {
    const folderChannels = getChannelsByFolder(folder.id);
    const isDropTarget = dragOverFolder === folder.id;

    return (
      <div
        key={folder.id}
        className={`border rounded-lg ${isDropTarget ? 'ring-2 ring-blue-500' : ''}`}
        style={{
          borderColor: isDropTarget ? colors.primary : colors.border,
          backgroundColor: colors.backgroundSecondary,
        }}
        onDragOver={(e) => handleDragOver(e, folder.id)}
        onDrop={(e) => handleDrop(e, folder.id)}
      >
        {/* Folder Header */}
        <div
          className="flex items-center justify-between p-3 cursor-pointer"
          onClick={() => onFolderUpdate(folder.id, { isCollapsed: !folder.isCollapsed })}
        >
          <div className="flex items-center space-x-2">
            <div className="text-sm">
              {folder.isCollapsed ? '▶️' : '▼️'}
            </div>
            <div className="text-sm">{folder.icon || '📁'}</div>
            <span className="font-medium text-sm" style={{ color: colors.text }}>
              {folder.name}
            </span>
            <span className="text-xs" style={{ color: colors.textSecondary }}>
              ({folderChannels.length})
            </span>
          </div>
          
          {folder.isCustom && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onFolderDelete(folder.id);
              }}
              className="text-red-500 hover:text-red-700 text-sm"
              title="Delete folder"
            >
              🗑️
            </button>
          )}
        </div>

        {/* Folder Channels */}
        {!folder.isCollapsed && (
          <div className="px-3 pb-3 space-y-1">
            {folderChannels.map(channel => renderChannel(channel, true))}
            {folderChannels.length === 0 && (
              <div
                className="text-center py-4 text-sm italic"
                style={{ color: colors.textSecondary }}
              >
                Drop channels here
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`} data-testid={testId}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold" style={{ color: colors.text }}>
          Channels
        </h3>
        <button
          onClick={() => setShowCreateFolder(!showCreateFolder)}
          className="text-sm px-2 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          style={{ color: colors.primary }}
        >
          {showCreateFolder ? 'Cancel' : '+ Folder'}
        </button>
      </div>

      {/* Create Folder Interface */}
      {showCreateFolder && (
        <div
          className="p-3 border rounded-lg"
          style={{
            borderColor: colors.border,
            backgroundColor: colors.backgroundSecondary,
          }}
        >
          <div className="space-y-3">
            <input
              type="text"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="Folder name"
              className="w-full px-3 py-2 border rounded bg-transparent outline-none"
              style={{
                borderColor: colors.border,
                color: colors.text,
              }}
            />
            <div className="text-sm" style={{ color: colors.textSecondary }}>
              Select channels to organize:
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowCreateFolder(false);
                  setSelectedChannels([]);
                  setNewFolderName('');
                }}
                className="text-sm px-3 py-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                style={{ color: colors.textSecondary }}
              >
                Cancel
              </button>
              <button
                onClick={handleCreateFolder}
                disabled={!newFolderName.trim() || selectedChannels.length === 0}
                className="text-sm px-3 py-1 rounded text-white disabled:opacity-50 transition-colors"
                style={{ backgroundColor: colors.primary }}
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Folders */}
      <div className="space-y-3">
        {folders.map(renderFolder)}
      </div>

      {/* Unorganized Channels */}
      {getUnorganizedChannels().length > 0 && (
        <div
          className={`border rounded-lg ${dragOverFolder === null ? 'ring-2 ring-blue-500' : ''}`}
          style={{
            borderColor: dragOverFolder === null ? colors.primary : colors.border,
            backgroundColor: colors.backgroundSecondary,
          }}
          onDragOver={(e) => handleDragOver(e, null)}
          onDrop={(e) => handleDrop(e, null)}
        >
          <div className="p-3">
            <div className="flex items-center space-x-2 mb-3">
              <span className="font-medium text-sm" style={{ color: colors.text }}>
                Channels
              </span>
              <span className="text-xs" style={{ color: colors.textSecondary }}>
                ({getUnorganizedChannels().length})
              </span>
            </div>
            <div className="space-y-1">
              {getUnorganizedChannels().map(channel => renderChannel(channel))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

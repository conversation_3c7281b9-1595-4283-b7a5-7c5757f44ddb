import React, { useState } from 'react';
import { useCompanyStore } from '../../../stores/companyStore';
import { useThemeStore } from '../../../stores/themeStore';
import Dropdown from '../Dropdown/Dropdown';
import {
  BuildingIcon,
  SwitchIcon,
  ChevronDownIcon,
  CheckIcon,
  PlusIcon,
} from '../../icons';
import type { DropdownItem } from '../Dropdown/Dropdown';

export interface CompanySelectorProps {
  className?: string;
  showLabel?: boolean;
  variant?: 'default' | 'compact' | 'expanded';
  size?: 'sm' | 'md' | 'lg';
  showCompanyCount?: boolean;
  'data-testid'?: string;
}

const CompanySelector: React.FC<CompanySelectorProps> = ({
  className = '',
  showLabel = false,
  variant = 'default',
  size = 'md',
  showCompanyCount = false,
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const { companies, currentCompany, switchCompany } = useCompanyStore();
  const [isHovered, setIsHovered] = useState(false);

  const handleCompanySwitch = (companyId: string) => {
    switchCompany(companyId);
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      padding: 'px-2 py-1.5',
      iconSize: 'w-4 h-4',
      logoSize: 'w-6 h-6',
      textSize: 'text-xs',
      chevronSize: 'w-3 h-3',
    },
    md: {
      padding: 'px-3 py-2',
      iconSize: 'w-4 h-4',
      logoSize: 'w-7 h-7',
      textSize: 'text-sm',
      chevronSize: 'w-4 h-4',
    },
    lg: {
      padding: 'px-4 py-3',
      iconSize: 'w-5 h-5',
      logoSize: 'w-8 h-8',
      textSize: 'text-base',
      chevronSize: 'w-4 h-4',
    },
  };

  const config = sizeConfig[size];

  // Create dropdown items from companies with enhanced styling
  const companyItems: DropdownItem[] = companies.map(company => ({
    id: company.id,
    label: '', // Remove duplicate label since we're showing it in the icon
    icon: (
      <div className="flex items-start justify-between w-full py-1">
        <div className="flex items-start space-x-3 flex-1 min-w-0">
          {/* Company Logo with enhanced styling */}
          <div className="relative flex-shrink-0">
            <div
              className={`${config.logoSize} rounded-lg flex items-center justify-center overflow-hidden transition-all duration-200`}
              style={{
                backgroundColor:
                  currentCompany?.id === company.id
                    ? `${colors.primary}15`
                    : colors.muted,
                border:
                  currentCompany?.id === company.id
                    ? `1px solid ${colors.primary}30`
                    : 'none',
              }}
            >
              {company.logo ? (
                <div className="text-lg leading-none">{company.logo}</div>
              ) : (
                <div
                  style={{
                    color:
                      currentCompany?.id === company.id
                        ? colors.primary
                        : colors.mutedForeground,
                  }}
                >
                  <BuildingIcon className={config.iconSize} />
                </div>
              )}
            </div>

            {/* Active indicator - positioned as overlay */}
            {currentCompany?.id === company.id && (
              <div
                className="absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 flex items-center justify-center"
                style={{
                  backgroundColor: colors.primary,
                  borderColor: colors.surface,
                }}
              >
                <CheckIcon className="w-2.5 h-2.5 text-white" />
              </div>
            )}
          </div>

          {/* Company Info with Stats */}
          <div className="flex flex-col min-w-0 flex-1 space-y-1">
            <div className="flex items-center justify-between">
              <div className="font-medium text-sm truncate">{company.name}</div>
              {company.isActive && (
                <div
                  className="px-2 py-0.5 rounded-full text-xs font-medium"
                  style={{
                    backgroundColor: `${colors.success}20`,
                    color: colors.success,
                  }}
                >
                  Active
                </div>
              )}
            </div>

            {company.domain && (
              <div
                className="text-xs truncate"
                style={{ color: colors.mutedForeground }}
              >
                🌐 {company.domain}
              </div>
            )}

            {/* Company Statistics */}
            <div className="flex items-center space-x-3 text-xs">
              {company.settings?.currency && (
                <div
                  className="flex items-center space-x-1"
                  style={{ color: colors.mutedForeground }}
                >
                  <span>💰</span>
                  <span>{company.settings.currency}</span>
                </div>
              )}
              {company.settings?.timezone && (
                <div
                  className="flex items-center space-x-1"
                  style={{ color: colors.mutedForeground }}
                >
                  <span>🕐</span>
                  <span>
                    {company.settings.timezone.split('/')[1] ||
                      company.settings.timezone}
                  </span>
                </div>
              )}
              {company.settings?.language && (
                <div
                  className="flex items-center space-x-1"
                  style={{ color: colors.mutedForeground }}
                >
                  <span>🌍</span>
                  <span>{company.settings.language.toUpperCase()}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    ),
    onClick: () => handleCompanySwitch(company.id),
  }));

  // Enhanced management options
  const managementItems: DropdownItem[] = [
    {
      id: 'divider-1',
      label: '',
      isDivider: true,
    },
    {
      id: 'add-company',
      label: '', // Remove label to use icon layout
      icon: (
        <div className="flex items-center justify-between w-full py-1">
          <div className="flex items-center space-x-3 flex-1">
            <div
              className={`${config.logoSize} rounded-lg flex items-center justify-center`}
              style={{ backgroundColor: `${colors.primary}15` }}
            >
              <div style={{ color: colors.primary }}>
                <PlusIcon className={config.iconSize} />
              </div>
            </div>
            <div className="flex flex-col min-w-0 flex-1">
              <div className="font-medium text-sm">Add New Company</div>
              <div
                className="text-xs"
                style={{ color: colors.mutedForeground }}
              >
                Create a new company profile
              </div>
            </div>
          </div>
        </div>
      ),
      onClick: () => {
        // TODO: Open add company modal
        console.log('Open add company modal');
      },
    },
    {
      id: 'manage-companies',
      label: '', // Remove label to use icon layout
      icon: (
        <div className="flex items-center justify-between w-full py-1">
          <div className="flex items-center space-x-3 flex-1">
            <div
              className={`${config.logoSize} rounded-lg flex items-center justify-center`}
              style={{ backgroundColor: colors.muted }}
            >
              <div style={{ color: colors.mutedForeground }}>
                <SwitchIcon className={config.iconSize} />
              </div>
            </div>
            <div className="flex flex-col min-w-0 flex-1">
              <div className="font-medium text-sm">Manage Companies</div>
              <div
                className="text-xs"
                style={{ color: colors.mutedForeground }}
              >
                Edit company settings and permissions
              </div>
            </div>
          </div>
        </div>
      ),
      onClick: () => {
        // TODO: Open company management modal
        console.log('Open company management modal');
      },
    },
  ];

  const dropdownItems: DropdownItem[] = [...companyItems, ...managementItems];

  // Enhanced trigger with better visual hierarchy
  const trigger = (
    <div
      className={`
        flex items-center space-x-2 ${config.padding} rounded-lg
        transition-all duration-200 cursor-pointer group
        ${variant === 'compact' ? 'min-w-0' : 'min-w-[120px]'}
        ${className}
      `}
      style={{
        backgroundColor: isHovered ? `${colors.hover}20` : 'transparent',
        border: `1px solid ${isHovered ? colors.primary + '30' : 'transparent'}`,
        boxShadow: isHovered ? `0 2px 8px ${colors.primary}15` : 'none',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      role="button"
      aria-label={`Current company: ${currentCompany?.name || 'None selected'}. Click to switch companies.`}
      aria-expanded={false}
      aria-haspopup="listbox"
    >
      {/* Company Logo/Icon with enhanced styling */}
      <div className="flex-shrink-0 relative">
        <div
          className={`${config.logoSize} rounded-lg flex items-center justify-center overflow-hidden transition-all duration-200`}
          style={{
            backgroundColor: isHovered
              ? `${colors.primary}20`
              : currentCompany?.logo
                ? `${colors.primary}10`
                : colors.muted,
            border: `1px solid ${isHovered ? colors.primary + '50' : 'transparent'}`,
            transform: isHovered ? 'scale(1.05)' : 'scale(1)',
          }}
        >
          {currentCompany?.logo ? (
            <div className="text-lg leading-none">{currentCompany.logo}</div>
          ) : (
            <div style={{ color: isHovered ? colors.primary : colors.text }}>
              <BuildingIcon className={config.iconSize} />
            </div>
          )}
        </div>

        {/* Active indicator dot */}
        {currentCompany && (
          <div
            className="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 transition-all duration-200"
            style={{
              backgroundColor: colors.primary,
              borderColor: colors.background,
              opacity: isHovered ? 1 : 0.8,
            }}
          />
        )}
      </div>

      {/* Company Info */}
      {(showLabel || variant === 'expanded') && currentCompany && (
        <div className="flex-1 min-w-0">
          <div
            className={`${config.textSize} font-medium truncate transition-colors duration-200`}
            style={{ color: isHovered ? colors.primary : colors.text }}
          >
            {currentCompany.name}
          </div>
          {variant === 'expanded' && currentCompany.domain && (
            <div
              className="text-xs truncate"
              style={{ color: colors.mutedForeground }}
            >
              {currentCompany.domain}
            </div>
          )}
          {showCompanyCount && companies.length > 1 && (
            <div className="text-xs" style={{ color: colors.mutedForeground }}>
              {companies.length} companies
            </div>
          )}
        </div>
      )}

      {/* Dropdown Arrow with enhanced animation */}
      <div
        className="transition-all duration-200"
        style={{
          color: isHovered ? colors.primary : colors.mutedForeground,
          transform: isHovered
            ? 'translateY(-1px) scale(1.1)'
            : 'translateY(0) scale(1)',
        }}
      >
        <ChevronDownIcon
          className={`${config.chevronSize} transition-transform duration-200`}
        />
      </div>
    </div>
  );

  return (
    <Dropdown
      trigger={trigger}
      items={dropdownItems}
      align="left"
      className={className}
      dropdownClassName="min-w-[320px]"
      data-testid={testId}
    />
  );
};

export default CompanySelector;

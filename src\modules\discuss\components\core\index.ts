// Core discuss components will be exported here
// These are the fundamental building blocks for the discuss module

// Message components
export { Message } from './Message';
export type { MessageProps } from './Message';

export { MessageList } from './MessageList';
export type { MessageListProps } from './MessageList';

export { MessageInput } from './MessageInput';
export type { MessageInputProps } from './MessageInput';

// Channel components
// export { Channel } from './Channel';
// export { ChannelList } from './ChannelList';

// User components
// export { UserPresence } from './UserPresence';
// export { UserList } from './UserList';

// Thread components
// export { Thread } from './Thread';
// export { ThreadView } from './ThreadView';

// TODO: Implement remaining core components

// AI Assistant service for natural language processing and intelligent responses
import type { 
  AIConversation, 
  AIMessage, 
  AIContext, 
  UserProfile,
  SmartResponse,
  ResponseSuggestion,
  Message,
  NLPConfig,
  ApiResponse 
} from '../types';

const API_BASE = '/api/discuss/ai-assistant';

export interface AIQueryRequest {
  query: string;
  context: Partial<AIContext>;
  conversationId?: string;
  channelId?: string;
  userId: string;
}

export interface AIQueryResponse {
  response: string;
  confidence: number;
  sources: Array<{ type: string; content: string; confidence: number }>;
  suggestions: ResponseSuggestion[];
  conversationId: string;
  metadata: Record<string, any>;
}

export interface ConversationSummaryRequest {
  messages: Message[];
  maxLength?: number;
  focusAreas?: string[];
}

export interface ConversationSummary {
  summary: string;
  keyPoints: string[];
  actionItems: string[];
  participants: string[];
  topics: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
  confidence: number;
}

export interface IntentAnalysisRequest {
  text: string;
  context?: Record<string, any>;
}

export interface IntentAnalysis {
  intent: string;
  confidence: number;
  entities: Array<{ type: string; value: string; confidence: number }>;
  sentiment: { label: string; score: number };
  keywords: string[];
  topics: string[];
}

export const aiAssistantService = {
  // Core AI Query Processing
  async processQuery(request: AIQueryRequest): Promise<ApiResponse<AIQueryResponse>> {
    const response = await fetch(`${API_BASE}/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to process AI query');
    }
    
    return response.json();
  },

  async generateResponse(
    prompt: string, 
    context: Partial<AIContext>,
    config?: Partial<NLPConfig>
  ): Promise<ApiResponse<SmartResponse>> {
    const response = await fetch(`${API_BASE}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt, context, config }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate AI response');
    }
    
    return response.json();
  },

  // Conversation Management
  async getConversation(id: string): Promise<ApiResponse<AIConversation>> {
    const response = await fetch(`${API_BASE}/conversations/${id}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch conversation');
    }
    
    return response.json();
  },

  async createConversation(
    userId: string, 
    channelId?: string,
    initialContext?: Partial<AIContext>
  ): Promise<ApiResponse<AIConversation>> {
    const response = await fetch(`${API_BASE}/conversations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId, channelId, initialContext }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create conversation');
    }
    
    return response.json();
  },

  async addMessageToConversation(
    conversationId: string, 
    message: Omit<AIMessage, 'id' | 'timestamp'>
  ): Promise<ApiResponse<AIConversation>> {
    const response = await fetch(`${API_BASE}/conversations/${conversationId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });
    
    if (!response.ok) {
      throw new Error('Failed to add message to conversation');
    }
    
    return response.json();
  },

  async updateConversationContext(
    conversationId: string, 
    context: Partial<AIContext>
  ): Promise<ApiResponse<AIConversation>> {
    const response = await fetch(`${API_BASE}/conversations/${conversationId}/context`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(context),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update conversation context');
    }
    
    return response.json();
  },

  // Natural Language Understanding
  async analyzeIntent(request: IntentAnalysisRequest): Promise<ApiResponse<IntentAnalysis>> {
    const response = await fetch(`${API_BASE}/analyze/intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to analyze intent');
    }
    
    return response.json();
  },

  async extractEntities(text: string, context?: Record<string, any>): Promise<ApiResponse<Array<{ type: string; value: string; confidence: number }>>> {
    const response = await fetch(`${API_BASE}/analyze/entities`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text, context }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to extract entities');
    }
    
    return response.json();
  },

  async analyzeSentiment(text: string): Promise<ApiResponse<{ label: string; score: number; confidence: number }>> {
    const response = await fetch(`${API_BASE}/analyze/sentiment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to analyze sentiment');
    }
    
    return response.json();
  },

  // Conversation Analysis
  async summarizeConversation(request: ConversationSummaryRequest): Promise<ApiResponse<ConversationSummary>> {
    const response = await fetch(`${API_BASE}/summarize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to summarize conversation');
    }
    
    return response.json();
  },

  async detectTopics(messages: Message[]): Promise<ApiResponse<Array<{ topic: string; confidence: number; messages: string[] }>>> {
    const response = await fetch(`${API_BASE}/analyze/topics`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messages }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to detect topics');
    }
    
    return response.json();
  },

  // Smart Suggestions
  async getSmartSuggestions(
    messageId: string,
    context: Record<string, any>
  ): Promise<ApiResponse<ResponseSuggestion[]>> {
    const response = await fetch(`${API_BASE}/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messageId, context }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to get smart suggestions');
    }
    
    return response.json();
  },

  async generateQuickReplies(
    messageContent: string,
    conversationContext: Message[]
  ): Promise<ApiResponse<string[]>> {
    const response = await fetch(`${API_BASE}/quick-replies`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messageContent, conversationContext }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate quick replies');
    }
    
    return response.json();
  },

  // User Profile Management
  async getUserProfile(userId: string): Promise<ApiResponse<UserProfile>> {
    const response = await fetch(`${API_BASE}/users/${userId}/profile`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch user profile');
    }
    
    return response.json();
  },

  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    const response = await fetch(`${API_BASE}/users/${userId}/profile`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update user profile');
    }
    
    return response.json();
  },

  // Configuration and Settings
  async getAIConfig(): Promise<ApiResponse<NLPConfig>> {
    const response = await fetch(`${API_BASE}/config`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch AI config');
    }
    
    return response.json();
  },

  async updateAIConfig(config: Partial<NLPConfig>): Promise<ApiResponse<NLPConfig>> {
    const response = await fetch(`${API_BASE}/config`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update AI config');
    }
    
    return response.json();
  },
};

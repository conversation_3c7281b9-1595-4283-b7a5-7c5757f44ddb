// Custom hook for managing WebSocket connection
import { useState, useEffect, useCallback } from 'react';
import { websocketService } from '../services';
import type { WebSocketEventType, WebSocketEventHandler } from '../services';

export interface UseWebSocketOptions {
  userId: string;
  autoConnect?: boolean;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connect: () => Promise<void>;
  disconnect: () => void;
  subscribe: (eventType: WebSocketEventType, handler: WebSocketEventHandler) => () => void;
  updatePresence: (status: 'online' | 'offline' | 'away' | 'busy') => void;
  joinChannel: (channelId: string) => void;
  leaveChannel: (channelId: string) => void;
}

export const useWebSocket = (options: UseWebSocketOptions): UseWebSocketReturn => {
  const { userId, autoConnect = true } = options;
  
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Connect to WebSocket
  const connect = useCallback(async () => {
    // Use functional updates to avoid dependency on current state
    setIsConnecting(prev => prev ? prev : true);
    setIsConnected(prev => {
      if (prev) {
        setIsConnecting(false);
        return prev; // Already connected, don't proceed
      }
      return prev;
    });

    setError(null);

    try {
      await websocketService.connect(userId);
      setIsConnected(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect');
      throw err;
    } finally {
      setIsConnecting(false);
    }
  }, [userId]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    websocketService.disconnect();
    setIsConnected(false);
    setIsConnecting(false);
  }, []);

  // Subscribe to WebSocket events
  const subscribe = useCallback((
    eventType: WebSocketEventType, 
    handler: WebSocketEventHandler
  ): (() => void) => {
    websocketService.on(eventType, handler);
    
    // Return unsubscribe function
    return () => {
      websocketService.off(eventType, handler);
    };
  }, []);

  // Update user presence
  const updatePresence = useCallback((status: 'online' | 'offline' | 'away' | 'busy') => {
    if (isConnected) {
      websocketService.updatePresence(status);
    }
  }, [isConnected]);

  // Join channel for real-time updates
  const joinChannel = useCallback((channelId: string) => {
    if (isConnected) {
      websocketService.joinChannel(channelId);
    }
  }, [isConnected]);

  // Leave channel
  const leaveChannel = useCallback((channelId: string) => {
    if (isConnected) {
      websocketService.leaveChannel(channelId);
    }
  }, [isConnected]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && userId) {
      connect().catch(err => {
        console.error('Auto-connect failed:', err);
      });
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, userId]); // Removed connect and disconnect from dependencies

  // Monitor WebSocket connection status
  useEffect(() => {
    const checkConnection = () => {
      const connected = websocketService.isConnected;
      setIsConnected(prevConnected => {
        if (prevConnected !== connected) {
          return connected;
        }
        return prevConnected;
      });
    };

    // Check connection status periodically
    const interval = setInterval(checkConnection, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []); // Removed isConnected from dependencies

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        updatePresence('away');
      } else {
        updatePresence('online');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [updatePresence]);

  // Handle window focus/blur
  useEffect(() => {
    const handleFocus = () => updatePresence('online');
    const handleBlur = () => updatePresence('away');

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [updatePresence]);

  return {
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    subscribe,
    updatePresence,
    joinChannel,
    leaveChannel,
  };
};

// Custom hook for managing messages
import { useState, useEffect, useCallback } from 'react';
import { messageService, websocketService } from '../services';
import type { Message, User } from '../types';

export interface UseMessagesOptions {
  channelId?: string;
  userId?: string; // For direct messages
  autoLoad?: boolean;
  pageSize?: number;
}

export interface UseMessagesReturn {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  sendMessage: (content: string, attachments?: File[]) => Promise<void>;
  editMessage: (messageId: string, content: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  addReaction: (messageId: string, emoji: string) => Promise<void>;
  removeReaction: (messageId: string, emoji: string) => Promise<void>;
  refresh: () => Promise<void>;
}

export const useMessages = (
  currentUserId: string,
  options: UseMessagesOptions = {}
): UseMessagesReturn => {
  const {
    channelId,
    userId,
    autoLoad = true,
    pageSize = 50,
  } = options;

  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);

  // Load messages
  const loadMessages = useCallback(async (page: number = 1, append: boolean = false) => {
    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      let response;
      
      if (channelId) {
        response = await messageService.getChannelMessages(channelId, page, pageSize);
      } else if (userId) {
        response = await messageService.getDirectMessages(userId, page, pageSize);
      } else {
        throw new Error('Either channelId or userId must be provided');
      }

      if (append) {
        setMessages(prev => [...prev, ...response.data]);
      } else {
        setMessages(response.data);
      }

      setHasMore(response.hasMore);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  }, [channelId, userId, pageSize, isLoading]);

  // Load more messages (pagination)
  const loadMore = useCallback(async () => {
    if (hasMore && !isLoading) {
      await loadMessages(currentPage + 1, true);
    }
  }, [hasMore, isLoading, currentPage, loadMessages]);

  // Send a new message
  const sendMessage = useCallback(async (content: string, attachments?: File[]) => {
    try {
      const response = await messageService.sendMessage({
        content,
        channelId,
        attachments,
      });

      if (response.success && response.data) {
        // Optimistically add the message to the list
        setMessages(prev => [...prev, response.data!]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
      throw err;
    }
  }, [channelId]);

  // Edit a message
  const editMessage = useCallback(async (messageId: string, content: string) => {
    try {
      const response = await messageService.updateMessage(messageId, { content });

      if (response.success && response.data) {
        setMessages(prev => prev.map(msg => 
          msg.id === messageId ? { ...msg, ...response.data, editedAt: new Date() } : msg
        ));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to edit message');
      throw err;
    }
  }, []);

  // Delete a message
  const deleteMessage = useCallback(async (messageId: string) => {
    try {
      await messageService.deleteMessage(messageId);
      
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, isDeleted: true } : msg
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete message');
      throw err;
    }
  }, []);

  // Add reaction to a message
  const addReaction = useCallback(async (messageId: string, emoji: string) => {
    try {
      await messageService.addReaction(messageId, emoji, currentUserId);
      
      // Optimistically update the UI
      setMessages(prev => prev.map(msg => {
        if (msg.id === messageId) {
          const existingReaction = msg.reactions.find(r => r.emoji === emoji);
          if (existingReaction) {
            if (!existingReaction.userIds.includes(currentUserId)) {
              existingReaction.userIds.push(currentUserId);
              existingReaction.count++;
            }
          } else {
            msg.reactions.push({
              emoji,
              userIds: [currentUserId],
              count: 1,
            });
          }
        }
        return msg;
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add reaction');
      throw err;
    }
  }, [currentUserId]);

  // Remove reaction from a message
  const removeReaction = useCallback(async (messageId: string, emoji: string) => {
    try {
      await messageService.removeReaction(messageId, emoji, currentUserId);
      
      // Optimistically update the UI
      setMessages(prev => prev.map(msg => {
        if (msg.id === messageId) {
          const reaction = msg.reactions.find(r => r.emoji === emoji);
          if (reaction) {
            reaction.userIds = reaction.userIds.filter(id => id !== currentUserId);
            reaction.count = reaction.userIds.length;
            
            if (reaction.count === 0) {
              msg.reactions = msg.reactions.filter(r => r.emoji !== emoji);
            }
          }
        }
        return msg;
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove reaction');
      throw err;
    }
  }, [currentUserId]);

  // Refresh messages
  const refresh = useCallback(async () => {
    setCurrentPage(1);
    await loadMessages(1, false);
  }, [loadMessages]);

  // Set up real-time message updates
  useEffect(() => {
    const handleNewMessage = (event: any) => {
      if (event.channelId === channelId || (!event.channelId && userId)) {
        setMessages(prev => {
          // Avoid duplicates
          if (prev.find(msg => msg.id === event.message.id)) {
            return prev;
          }
          return [...prev, event.message];
        });
      }
    };

    const handleMessageUpdate = (event: any) => {
      if (event.channelId === channelId || (!event.channelId && userId)) {
        setMessages(prev => prev.map(msg => 
          msg.id === event.message.id ? event.message : msg
        ));
      }
    };

    const handleMessageDelete = (event: any) => {
      if (event.channelId === channelId || (!event.channelId && userId)) {
        setMessages(prev => prev.map(msg => 
          msg.id === event.message.id ? { ...msg, isDeleted: true } : msg
        ));
      }
    };

    // Subscribe to WebSocket events
    websocketService.on('message_created', handleNewMessage);
    websocketService.on('message_updated', handleMessageUpdate);
    websocketService.on('message_deleted', handleMessageDelete);

    return () => {
      websocketService.off('message_created', handleNewMessage);
      websocketService.off('message_updated', handleMessageUpdate);
      websocketService.off('message_deleted', handleMessageDelete);
    };
  }, [channelId, userId]);

  // Auto-load messages on mount
  useEffect(() => {
    if (autoLoad && (channelId || userId)) {
      loadMessages();
    }
  }, [autoLoad, channelId, userId, loadMessages]);

  return {
    messages,
    isLoading,
    error,
    hasMore,
    loadMore,
    sendMessage,
    editMessage,
    deleteMessage,
    addReaction,
    removeReaction,
    refresh,
  };
};

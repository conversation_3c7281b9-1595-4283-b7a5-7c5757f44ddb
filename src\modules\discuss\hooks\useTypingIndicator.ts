// Custom hook for managing typing indicators
import { useState, useEffect, useCallback, useRef } from 'react';
import { websocketService } from '../services';

export interface TypingUser {
  userId: string;
  userName: string;
  channelId?: string;
}

export interface UseTypingIndicatorOptions {
  channelId?: string;
  currentUserId: string;
  typingTimeout?: number; // milliseconds
}

export interface UseTypingIndicatorReturn {
  typingUsers: TypingUser[];
  startTyping: () => void;
  stopTyping: () => void;
  isTyping: boolean;
}

export const useTypingIndicator = (
  options: UseTypingIndicatorOptions
): UseTypingIndicatorReturn => {
  const { channelId, currentUserId, typingTimeout = 3000 } = options;
  
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const stopTypingTimeoutRef = useRef<NodeJS.Timeout>();

  // Start typing indicator
  const startTyping = useCallback(() => {
    if (!isTyping && channelId) {
      setIsTyping(true);
      websocketService.sendTyping(channelId, true);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to automatically stop typing
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, typingTimeout);
  }, [isTyping, channelId, typingTimeout]);

  // Stop typing indicator
  const stopTyping = useCallback(() => {
    if (isTyping && channelId) {
      setIsTyping(false);
      websocketService.sendTyping(channelId, false);
    }

    // Clear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  }, [isTyping, channelId]);

  // Handle user typing events from WebSocket
  useEffect(() => {
    const handleUserTyping = (event: any) => {
      const { userId, channelId: eventChannelId, presence } = event;
      
      // Only handle events for the current channel and exclude current user
      if (eventChannelId === channelId && userId !== currentUserId) {
        setTypingUsers(prev => {
          // Remove existing entry for this user
          const filtered = prev.filter(user => user.userId !== userId);
          
          // Add new entry if user is typing
          if (presence.isTyping) {
            return [...filtered, {
              userId,
              userName: `User ${userId}`, // In real app, get from user store
              channelId: eventChannelId,
            }];
          }
          
          return filtered;
        });

        // Auto-remove typing indicator after timeout
        if (presence.isTyping) {
          setTimeout(() => {
            setTypingUsers(prev => prev.filter(user => user.userId !== userId));
          }, typingTimeout + 1000); // Add buffer time
        }
      }
    };

    const handleUserStoppedTyping = (event: any) => {
      const { userId, channelId: eventChannelId } = event;
      
      if (eventChannelId === channelId && userId !== currentUserId) {
        setTypingUsers(prev => prev.filter(user => user.userId !== userId));
      }
    };

    // Subscribe to WebSocket events
    websocketService.on('user_typing', handleUserTyping);
    websocketService.on('user_stopped_typing', handleUserStoppedTyping);

    return () => {
      websocketService.off('user_typing', handleUserTyping);
      websocketService.off('user_stopped_typing', handleUserStoppedTyping);
    };
  }, [channelId, currentUserId, typingTimeout]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (stopTypingTimeoutRef.current) {
        clearTimeout(stopTypingTimeoutRef.current);
      }
      if (isTyping) {
        stopTyping();
      }
    };
  }, [isTyping, stopTyping]);

  // Clear typing users when channel changes
  useEffect(() => {
    setTypingUsers([]);
    if (isTyping) {
      stopTyping();
    }
  }, [channelId, isTyping, stopTyping]);

  return {
    typingUsers,
    startTyping,
    stopTyping,
    isTyping,
  };
};

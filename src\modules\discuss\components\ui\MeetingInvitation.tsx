import React, { useState } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { getMockUserById } from '../../../../mocks/data/discuss';
import type { User, Call } from '../../types';

export interface MeetingInvitationProps {
  call: Call;
  invitedBy: string;
  onAccept: () => void;
  onDecline: () => void;
  className?: string;
  'data-testid'?: string;
}

export const MeetingInvitation: React.FC<MeetingInvitationProps> = ({
  call,
  invitedBy,
  onAccept,
  onDecline,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [isResponding, setIsResponding] = useState(false);
  
  const inviter = getMockUserById(invitedBy);
  const participantCount = call.participantIds?.length || 0;

  const handleAccept = async () => {
    setIsResponding(true);
    try {
      await onAccept();
    } finally {
      setIsResponding(false);
    }
  };

  const handleDecline = async () => {
    setIsResponding(true);
    try {
      await onDecline();
    } finally {
      setIsResponding(false);
    }
  };

  const formatCallType = () => {
    return call.type === 'video' ? 'Video Call' : 'Voice Call';
  };

  return (
    <div 
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-6 max-w-md ${className}`}
      style={{ borderColor: colors.border }}
      data-testid={testId}
    >
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-2xl">
            {call.type === 'video' ? '📹' : '📞'}
          </span>
        </div>
        
        <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
          {formatCallType()} Invitation
        </h3>
        
        <p className="text-sm" style={{ color: colors.textSecondary }}>
          {inviter?.name || 'Someone'} is inviting you to join a {call.type} call
        </p>
      </div>

      {/* Call Details */}
      <div className="space-y-3 mb-6">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium" style={{ color: colors.text }}>
            Call Type:
          </span>
          <span className="text-sm" style={{ color: colors.textSecondary }}>
            {formatCallType()}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium" style={{ color: colors.text }}>
            Participants:
          </span>
          <span className="text-sm" style={{ color: colors.textSecondary }}>
            {participantCount} {participantCount === 1 ? 'person' : 'people'}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium" style={{ color: colors.text }}>
            Started:
          </span>
          <span className="text-sm" style={{ color: colors.textSecondary }}>
            {call.startedAt ? new Date(call.startedAt).toLocaleTimeString() : 'Just now'}
          </span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3">
        <button
          onClick={handleDecline}
          disabled={isResponding}
          className={`flex-1 py-3 px-4 rounded-lg border transition-colors ${
            isResponding
              ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
          }`}
          style={!isResponding ? { borderColor: colors.border, color: colors.text } : {}}
        >
          {isResponding ? 'Declining...' : 'Decline'}
        </button>
        
        <button
          onClick={handleAccept}
          disabled={isResponding}
          className={`flex-1 py-3 px-4 rounded-lg transition-colors ${
            isResponding
              ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
              : 'bg-green-500 hover:bg-green-600 text-white'
          }`}
        >
          {isResponding ? 'Joining...' : 'Join Call'}
        </button>
      </div>

      {/* Additional Info */}
      <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <p className="text-xs text-center" style={{ color: colors.textSecondary }}>
          Your camera and microphone will be activated when you join
        </p>
      </div>
    </div>
  );
};

// Invitation Toast Component for notifications
export interface InvitationToastProps {
  call: Call;
  invitedBy: string;
  onAccept: () => void;
  onDecline: () => void;
  onDismiss: () => void;
  className?: string;
}

export const InvitationToast: React.FC<InvitationToastProps> = ({
  call,
  invitedBy,
  onAccept,
  onDecline,
  onDismiss,
  className = '',
}) => {
  const { colors } = useThemeStore();
  const inviter = getMockUserById(invitedBy);

  return (
    <div 
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 min-w-80 ${className}`}
      style={{ borderColor: colors.border }}
    >
      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
          <span className="text-lg">
            {call.type === 'video' ? '📹' : '📞'}
          </span>
        </div>
        
        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="font-medium text-sm" style={{ color: colors.text }}>
              {call.type === 'video' ? 'Video' : 'Voice'} Call Invitation
            </h4>
            <button
              onClick={onDismiss}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 text-sm"
            >
              ✕
            </button>
          </div>
          
          <p className="text-sm mb-3" style={{ color: colors.textSecondary }}>
            {inviter?.name || 'Someone'} is calling you
          </p>
          
          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={onDecline}
              className="px-3 py-1.5 text-sm border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              style={{ borderColor: colors.border, color: colors.textSecondary }}
            >
              Decline
            </button>
            <button
              onClick={onAccept}
              className="px-3 py-1.5 text-sm bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
            >
              Join
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Invitation Manager Component
export interface InvitationManagerProps {
  currentUserId: string;
  onJoinCall: (callId: string) => void;
  className?: string;
}

export const InvitationManager: React.FC<InvitationManagerProps> = ({
  currentUserId,
  onJoinCall,
  className = '',
}) => {
  const [pendingInvitations, setPendingInvitations] = useState<Array<{
    id: string;
    call: Call;
    invitedBy: string;
    timestamp: Date;
  }>>([]);

  // Mock invitation for demo
  React.useEffect(() => {
    const mockInvitation = {
      id: 'inv-1',
      call: {
        id: 'call-123',
        type: 'video' as const,
        participantIds: ['1', '2'],
        startedBy: '2',
        startedAt: new Date(),
        status: 'active' as const,
      },
      invitedBy: '2',
      timestamp: new Date(),
    };
    
    // Simulate receiving an invitation after 3 seconds
    const timer = setTimeout(() => {
      setPendingInvitations([mockInvitation]);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handleAcceptInvitation = (invitation: typeof pendingInvitations[0]) => {
    onJoinCall(invitation.call.id);
    setPendingInvitations(prev => prev.filter(inv => inv.id !== invitation.id));
  };

  const handleDeclineInvitation = (invitationId: string) => {
    setPendingInvitations(prev => prev.filter(inv => inv.id !== invitationId));
  };

  const handleDismissInvitation = (invitationId: string) => {
    setPendingInvitations(prev => prev.filter(inv => inv.id !== invitationId));
  };

  if (pendingInvitations.length === 0) {
    return null;
  }

  return (
    <div className={`fixed top-4 right-4 z-50 space-y-3 ${className}`}>
      {pendingInvitations.map(invitation => (
        <InvitationToast
          key={invitation.id}
          call={invitation.call}
          invitedBy={invitation.invitedBy}
          onAccept={() => handleAcceptInvitation(invitation)}
          onDecline={() => handleDeclineInvitation(invitation.id)}
          onDismiss={() => handleDismissInvitation(invitation.id)}
        />
      ))}
    </div>
  );
};

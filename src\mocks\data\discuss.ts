// Mock data for Discuss module
import type {
  User,
  Message,
  Channel,
  DirectMessage,
  Team,
  NotificationSettings,
  PresenceInfo,
  Call,
  CallParticipant,
  Bot,
  AutomationRule,
  ScheduledMessage,
  MessageArchive,
  RetentionPolicyRule,
  LegalHold,
  ExportRequest,
  ExternalIntegration,
  ERPIntegration,
  FAQEntry,
  FAQCategory,
  WebhookEndpoint,
  APIKey,
} from '../../modules/discuss/types';

// Mock users
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'JD',
    status: 'online',
    lastSeen: new Date(),
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'JS',
    status: 'away',
    lastSeen: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'MW',
    status: 'online',
    lastSeen: new Date(),
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'S<PERSON>',
    status: 'offline',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
  },
  {
    id: '5',
    name: 'David Brown',
    email: '<EMAIL>',
    avatar: 'DB',
    status: 'busy',
    lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
  },
];

// Mock channels
export const mockChannels: Channel[] = [
  {
    id: 'general',
    name: 'general',
    description: 'General discussion and announcements for the team',
    type: 'public',
    memberIds: ['1', '2', '3', '4', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    lastActivity: new Date(),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'development',
    name: 'development',
    description: 'Development discussions and code reviews',
    type: 'public',
    memberIds: ['1', '3', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-02'),
    lastActivity: new Date(Date.now() - 30 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'design',
    name: 'design',
    description: 'Design discussions and feedback',
    type: 'public',
    memberIds: ['2', '4'],
    createdBy: '2',
    createdAt: new Date('2024-01-03'),
    lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'marketing',
    name: 'marketing',
    description: 'Marketing campaigns and strategies',
    type: 'private',
    memberIds: ['2', '4', '5'],
    createdBy: '2',
    createdAt: new Date('2024-01-04'),
    lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: false,
    },
  },
  {
    id: 'random',
    name: 'random',
    description: 'Random conversations and fun stuff',
    type: 'public',
    memberIds: ['1', '2', '3', '4', '5'],
    createdBy: '3',
    createdAt: new Date('2024-01-05'),
    lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: false,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
];

// Mock messages with comprehensive data
export const mockMessages: Message[] = [
  // General channel messages
  {
    id: 'msg-1',
    content: 'Welcome everyone to our new discuss platform! 🎉 Feel free to share ideas, ask questions, and collaborate here.',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date('2024-01-10T09:30:00'),
    reactions: [
      { emoji: '👍', userIds: ['2', '3', '4'], count: 3 },
      { emoji: '🎉', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-2',
    content: 'This looks great! I love the clean interface. Can we also add file sharing capabilities?',
    authorId: '2',
    channelId: 'general',
    timestamp: new Date('2024-01-10T10:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['1'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-3',
    content: '@Jane Smith Yes! File sharing is definitely on the roadmap. We\'re also planning to add video calls and screen sharing.',
    authorId: '3',
    channelId: 'general',
    timestamp: new Date('2024-01-10T11:00:00'),
    reactions: [],
    attachments: [],
    mentions: ['2'],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-4',
    content: 'I\'ve uploaded the latest design mockups for review. Please take a look and let me know your thoughts!',
    authorId: '4',
    channelId: 'general',
    timestamp: new Date('2024-01-10T14:30:00'),
    reactions: [
      { emoji: '👀', userIds: ['1', '2', '3'], count: 3 },
      { emoji: '🔥', userIds: ['1'], count: 1 },
    ],
    attachments: [
      {
        id: 'att-1',
        name: 'design-mockups-v2.pdf',
        type: 'document',
        url: '/mock-files/design-mockups-v2.pdf',
        size: 2048576, // 2MB
        mimeType: 'application/pdf',
      },
      {
        id: 'att-2',
        name: 'homepage-screenshot.png',
        type: 'image',
        url: '/mock-files/homepage-screenshot.png',
        size: 512000, // 500KB
        mimeType: 'image/png',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-5',
    content: 'Great work @Sarah Johnson! The new color scheme looks much more professional. 💯',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date('2024-01-10T15:45:00'),
    reactions: [
      { emoji: '💯', userIds: ['2', '3', '4'], count: 3 },
    ],
    attachments: [],
    mentions: ['4'],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Development channel messages
  {
    id: 'msg-6',
    content: 'Just pushed the new authentication system to the dev branch. Ready for testing!',
    authorId: '3',
    channelId: 'development',
    timestamp: new Date('2024-01-10T09:00:00'),
    reactions: [
      { emoji: '🚀', userIds: ['1', '5'], count: 2 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-7',
    content: 'Found a bug in the user registration flow. Creating a ticket now.',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date('2024-01-10T11:30:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-8',
    content: 'Here\'s the error log from the staging server. Looks like a database connection issue.',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date('2024-01-10T12:00:00'),
    reactions: [],
    attachments: [
      {
        id: 'att-3',
        name: 'error-log-2024-01-10.txt',
        type: 'document',
        url: '/mock-files/error-log-2024-01-10.txt',
        size: 15360, // 15KB
        mimeType: 'text/plain',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Design channel messages
  {
    id: 'msg-9',
    content: 'Working on the new icon set for the mobile app. Here\'s the progress so far:',
    authorId: '2',
    channelId: 'design',
    timestamp: new Date('2024-01-09T16:20:00'),
    reactions: [
      { emoji: '🎨', userIds: ['4'], count: 1 },
    ],
    attachments: [
      {
        id: 'att-4',
        name: 'mobile-icons-preview.png',
        type: 'image',
        url: '/mock-files/mobile-icons-preview.png',
        size: 768000, // 750KB
        mimeType: 'image/png',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-10',
    content: 'Love the minimalist approach! Can we make the notification icon a bit more prominent?',
    authorId: '4',
    channelId: 'design',
    timestamp: new Date('2024-01-09T17:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Direct messages (no channelId)
  {
    id: 'msg-11',
    content: 'Hey! I wanted to discuss the new project requirements with you. Do you have some time today?',
    authorId: '1',
    timestamp: new Date('2024-01-09T15:45:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-12',
    content: 'Sure! I\'m free after 5 PM. Should we schedule a quick call?',
    authorId: '2',
    timestamp: new Date('2024-01-09T16:12:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-13',
    content: 'Perfect! Let\'s do a video call at 5:30 PM. I\'ll send you the meeting link.',
    authorId: '1',
    timestamp: new Date('2024-01-09T16:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-14',
    content: 'Good morning! I\'ve reviewed the requirements document. Looks great overall, just have a few questions about the timeline.',
    authorId: '1',
    timestamp: new Date('2024-01-10T09:00:00'),
    reactions: [],
    attachments: [
      {
        id: 'att-5',
        name: 'project-requirements-v3.docx',
        type: 'document',
        url: '/mock-files/project-requirements-v3.docx',
        size: 1024000, // 1MB
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-15',
    content: 'Thanks for the review! I\'ll address those timeline questions in our call today.',
    authorId: '2',
    timestamp: new Date('2024-01-10T09:30:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // More recent messages for testing
  {
    id: 'msg-16',
    content: 'Just deployed the hotfix to production. Everything looks good! 🎉',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    reactions: [
      { emoji: '🎉', userIds: ['1', '3'], count: 2 },
      { emoji: '🚀', userIds: ['1'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-17',
    content: 'Team meeting in 30 minutes! Don\'t forget to join the video call.',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    reactions: [
      { emoji: '📅', userIds: ['2', '3', '4', '5'], count: 4 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-18',
    content: 'Can someone help me with the CSS animation issue? It\'s not working in Safari.',
    authorId: '3',
    channelId: 'development',
    timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'delivered',
  },
  {
    id: 'msg-19',
    content: 'I can help! Safari has some quirks with CSS animations. Let me take a look.',
    authorId: '1',
    channelId: 'development',
    timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
    reactions: [
      { emoji: '🙏', userIds: ['3'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'delivered',
  },
  {
    id: 'msg-20',
    content: 'Quick question about the API endpoint for user preferences. Is it /api/users/:id/preferences?',
    authorId: '2',
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'sent',
  },
];

// Mock direct messages
export const mockDirectMessages: DirectMessage[] = [
  {
    id: 'dm-1-2',
    participantIds: ['1', '2'],
    lastMessage: mockMessages.find(m => m.id === 'msg-5'),
    lastActivity: new Date('2024-01-09T16:12:00'),
    isArchived: false,
  },
  {
    id: 'dm-1-3',
    participantIds: ['1', '3'],
    lastActivity: new Date('2024-01-08T14:30:00'),
    isArchived: false,
  },
];

// Mock teams
export const mockTeams: Team[] = [
  {
    id: 'frontend-team',
    name: 'Frontend Team',
    description: 'UI/UX development and design implementation',
    memberIds: ['1', '2', '4'],
    channelIds: ['general', 'design'],
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    settings: {
      visibility: 'public',
      joinPolicy: 'open',
      allowMemberInvites: true,
    },
  },
  {
    id: 'backend-team',
    name: 'Backend Team',
    description: 'Server-side development and API management',
    memberIds: ['3', '5'],
    channelIds: ['development'],
    createdBy: '3',
    createdAt: new Date('2024-01-02'),
    settings: {
      visibility: 'public',
      joinPolicy: 'invite-only',
      allowMemberInvites: false,
    },
  },
];

// Mock notification settings
export const mockNotificationSettings: NotificationSettings = {
  desktop: true,
  sound: true,
  email: false,
  mobile: true,
  mentions: true,
  directMessages: true,
  channels: true,
  doNotDisturbStart: '22:00',
  doNotDisturbEnd: '08:00',
};

// Mock presence info
export const mockPresenceInfo: PresenceInfo[] = [
  {
    userId: '1',
    status: 'online',
    lastSeen: new Date(),
    isTyping: false,
  },
  {
    userId: '2',
    status: 'away',
    lastSeen: new Date(Date.now() - 15 * 60 * 1000),
    isTyping: true,
    currentChannel: 'general',
  },
  {
    userId: '3',
    status: 'online',
    lastSeen: new Date(),
    isTyping: false,
  },
  {
    userId: '4',
    status: 'offline',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isTyping: false,
  },
  {
    userId: '5',
    status: 'busy',
    lastSeen: new Date(Date.now() - 30 * 60 * 1000),
    isTyping: false,
  },
];

// Helper functions to get mock data
export const getMockUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getMockChannelById = (id: string): Channel | undefined => {
  return mockChannels.find(channel => channel.id === id);
};

export const getMockMessagesByChannelId = (channelId: string): Message[] => {
  return mockMessages.filter(message => message.channelId === channelId);
};

export const getMockDirectMessagesByUserId = (userId: string): DirectMessage[] => {
  return mockDirectMessages.filter(dm => dm.participantIds.includes(userId));
};

export const getMockTeamById = (id: string): Team | undefined => {
  return mockTeams.find(team => team.id === id);
};

// Mock calls
export const mockCalls: Call[] = [
  {
    id: 'call-1',
    type: 'video',
    channelId: 'general',
    participantIds: ['1', '2', '3'],
    startedBy: '1',
    startedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    endedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000), // 15 minutes duration
    status: 'ended',
    recordingUrl: '/recordings/call-1.mp4',
  },
  {
    id: 'call-2',
    type: 'voice',
    channelId: 'development',
    participantIds: ['2', '3', '4'],
    startedBy: '2',
    startedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    endedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000), // 8 minutes duration
    status: 'ended',
  },
  {
    id: 'call-3',
    type: 'video',
    participantIds: ['1', '4'], // Direct call
    startedBy: '4',
    startedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    endedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 25 * 60 * 1000), // 25 minutes duration
    status: 'ended',
    recordingUrl: '/recordings/call-3.mp4',
  },
  {
    id: 'call-4',
    type: 'voice',
    channelId: 'general',
    participantIds: ['1', '2', '3', '5'],
    startedBy: '3',
    startedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    endedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 45 * 60 * 1000), // 45 minutes duration
    status: 'ended',
  },
  {
    id: 'call-5',
    type: 'video',
    channelId: 'design',
    participantIds: ['2', '5'],
    startedBy: '5',
    startedAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago (ongoing)
    status: 'active',
  },
];

// Mock call participants
export const mockCallParticipants: { [callId: string]: CallParticipant[] } = {
  'call-1': [
    {
      userId: '1',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: false,
    },
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: true,
    },
    {
      userId: '3',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 2 * 60 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 12 * 60 * 1000),
      isMuted: true,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
  ],
  'call-2': [
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
    {
      userId: '3',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 15 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
    {
      userId: '4',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 45 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 6 * 60 * 1000),
      isMuted: true,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
  ],
  'call-5': [
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 5 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: false,
    },
    {
      userId: '5',
      joinedAt: new Date(Date.now() - 5 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: true,
    },
  ],
};

// Helper functions for calls
export const getMockCallById = (id: string): Call | undefined => {
  return mockCalls.find(call => call.id === id);
};

export const getMockCallsByChannelId = (channelId: string): Call[] => {
  return mockCalls.filter(call => call.channelId === channelId);
};

export const getMockCallsByUserId = (userId: string): Call[] => {
  return mockCalls.filter(call => call.participantIds.includes(userId));
};

export const getMockCallParticipants = (callId: string): CallParticipant[] => {
  return mockCallParticipants[callId] || [];
};

// Mock message templates
export const mockMessageTemplates = [
  {
    id: 'template-welcome',
    name: 'Welcome Message',
    description: 'Welcome new team members',
    content: 'Welcome to {{channelName}}, {{userName}}! 👋 We\'re excited to have you join our team. Feel free to introduce yourself and let us know if you have any questions.',
    variables: [
      { name: 'channelName', type: 'channel', required: true, description: 'Channel name' },
      { name: 'userName', type: 'user', required: true, description: 'User name' }
    ],
    category: 'onboarding',
    tags: ['welcome', 'new-member'],
    usage: 45,
    isActive: true,
    createdBy: '1',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-01')
  },
  {
    id: 'template-reminder',
    name: 'Daily Standup Reminder',
    description: 'Remind team about daily standup',
    content: '📅 Daily Standup Reminder\n\nHi team! Don\'t forget about our daily standup at {{time}}.\n\nPlease be ready to share:\n• What you completed yesterday\n• What you\'re working on today\n• Any blockers or challenges\n\nSee you there! 🚀',
    variables: [
      { name: 'time', type: 'string', required: true, description: 'Meeting time' }
    ],
    category: 'meetings',
    tags: ['standup', 'reminder', 'daily'],
    usage: 120,
    isActive: true,
    createdBy: '1',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-25')
  },
  {
    id: 'template-celebration',
    name: 'Achievement Celebration',
    description: 'Celebrate team achievements',
    content: '🎉 Congratulations {{userName}}! 🎉\n\nWe\'re thrilled to celebrate your {{achievement}}! Your hard work and dedication truly make a difference.\n\n{{customMessage}}\n\nKeep up the amazing work! 💪',
    variables: [
      { name: 'userName', type: 'user', required: true, description: 'User to celebrate' },
      { name: 'achievement', type: 'string', required: true, description: 'What they achieved' },
      { name: 'customMessage', type: 'string', required: false, description: 'Additional message' }
    ],
    category: 'recognition',
    tags: ['celebration', 'achievement', 'recognition'],
    usage: 28,
    isActive: true,
    createdBy: '2',
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-15')
  }
];

// Mock bots
export const mockBots: Bot[] = [
  {
    id: 'bot-faq',
    name: 'FAQ Assistant',
    description: 'Answers frequently asked questions automatically',
    avatar: '🤖',
    type: 'faq',
    commands: [
      {
        command: '/faq',
        description: 'Search FAQ database',
        usage: '/faq <question>',
        permissions: ['read'],
        handler: 'handleFAQQuery',
        parameters: [
          {
            name: 'question',
            type: 'string',
            required: true,
            description: 'The question to search for',
          },
        ],
      },
      {
        command: '/help',
        description: 'Show available commands',
        usage: '/help',
        permissions: ['read'],
        handler: 'handleHelp',
      },
    ],
    triggers: [
      {
        id: 'faq-keyword',
        type: 'keyword',
        pattern: 'how to,what is,where can,help me',
        conditions: [],
        action: {
          type: 'reply',
          config: {
            content: 'I can help you with that! Try using /faq <your question>',
            useTemplating: true,
            variables: { botName: 'FAQ Assistant' }
          },
        },
        enabled: true,
        priority: 1,
        cooldown: 30,
        config: {
          keywords: ['how to', 'what is', 'where can', 'help me', 'how do i', 'can you help'],
          caseSensitive: false,
          wholeWordsOnly: false,
          fuzzyMatching: true,
          fuzzyThreshold: 0.8,
          responseVariables: { botName: 'FAQ Assistant' },
          randomResponses: [
            'I can help you with that! Try using /faq <your question>',
            'Let me assist you! Use /faq followed by your question.',
            'I\'m here to help! Type /faq and then your question.'
          ]
        }
      },
      {
        id: 'sentiment-positive',
        type: 'sentiment',
        pattern: 'positive',
        conditions: [],
        action: {
          type: 'reaction',
          config: {
            emoji: '👍'
          }
        },
        enabled: true,
        priority: 2,
        config: {
          sentimentType: 'positive',
          sentimentThreshold: 0.7
        }
      },
      {
        id: 'urgent-help',
        type: 'regex',
        pattern: '(urgent|emergency|critical|asap|help!).*\\?',
        conditions: [],
        action: {
          type: 'reply',
          config: {
            content: '🚨 I see this is urgent! Let me escalate this to our support team immediately.',
            mentions: ['support-team']
          }
        },
        enabled: true,
        priority: 10,
        config: {
          regexFlags: 'gi',
          multiline: false
        }
      }
    ],
    config: {
      permissions: {
        channels: ['general', 'support'],
        users: [],
        commands: ['/faq', '/help'],
        canReadHistory: true,
        canSendDM: false,
        canMentionUsers: false,
      },
      rateLimiting: {
        maxRequestsPerMinute: 10,
        maxRequestsPerHour: 100,
        cooldownPeriod: 5,
      },
      context: {
        rememberConversations: true,
        contextWindow: 5,
        persistUserData: false,
      },
    },
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-15'),
    lastActive: new Date(),
    analytics: {
      totalInteractions: 156,
      successfulResponses: 142,
      failedResponses: 14,
      averageResponseTime: 250,
      lastInteraction: new Date(),
      popularCommands: {
        '/faq': 89,
        '/help': 67,
      },
    },
  },
  {
    id: 'bot-ai-assistant',
    name: 'AI Assistant',
    description: 'Intelligent assistant powered by AI for complex queries',
    avatar: '🧠',
    type: 'ai_assistant',
    commands: [
      {
        command: '/ask',
        description: 'Ask the AI assistant anything',
        usage: '/ask <question>',
        permissions: ['read', 'write'],
        handler: 'handleAIQuery',
        parameters: [
          {
            name: 'question',
            type: 'string',
            required: true,
            description: 'Your question for the AI',
          },
        ],
      },
      {
        command: '/summarize',
        description: 'Summarize recent conversation',
        usage: '/summarize [count]',
        permissions: ['read'],
        handler: 'handleSummarize',
        parameters: [
          {
            name: 'count',
            type: 'number',
            required: false,
            description: 'Number of messages to summarize',
            defaultValue: 10,
          },
        ],
      },
    ],
    triggers: [
      {
        id: 'ai-mention',
        type: 'mention',
        pattern: '@ai-assistant',
        conditions: [],
        action: {
          type: 'reply',
          config: {
            content: 'Let me help you with that using AI assistance.',
            functionName: 'processAIQuery',
            parameters: { useAI: true, context: 'conversation' }
          },
        },
        enabled: true,
      },
    ],
    config: {
      permissions: {
        channels: ['general', 'development', 'support'],
        users: [],
        commands: ['/ask', '/summarize'],
        canReadHistory: true,
        canSendDM: true,
        canMentionUsers: true,
      },
      rateLimiting: {
        maxRequestsPerMinute: 5,
        maxRequestsPerHour: 50,
        cooldownPeriod: 10,
      },
      context: {
        rememberConversations: true,
        contextWindow: 20,
        persistUserData: true,
      },
      nlp: {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 500,
        systemPrompt: 'You are a helpful assistant for a team collaboration platform.',
      },
    },
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-20'),
    lastActive: new Date(),
    analytics: {
      totalInteractions: 89,
      successfulResponses: 85,
      failedResponses: 4,
      averageResponseTime: 1200,
      lastInteraction: new Date(),
      popularCommands: {
        '/ask': 52,
        '/summarize': 37,
      },
    },
  },
];

// Mock FAQ Categories
export const mockFAQCategories: FAQCategory[] = [
  {
    id: 'getting-started',
    name: 'Getting Started',
    description: 'Basic information for new users',
    icon: '🚀',
    order: 1,
    isActive: true,
    entryCount: 8,
  },
  {
    id: 'account-management',
    name: 'Account Management',
    description: 'Managing your account and profile',
    icon: '👤',
    order: 2,
    isActive: true,
    entryCount: 12,
  },
  {
    id: 'messaging',
    name: 'Messaging & Communication',
    description: 'How to use messaging features',
    icon: '💬',
    order: 3,
    isActive: true,
    entryCount: 15,
  },
  {
    id: 'collaboration',
    name: 'Collaboration Tools',
    description: 'Working together effectively',
    icon: '🤝',
    order: 4,
    isActive: true,
    entryCount: 10,
  },
  {
    id: 'technical',
    name: 'Technical Support',
    description: 'Technical issues and troubleshooting',
    icon: '🔧',
    order: 5,
    isActive: true,
    entryCount: 18,
  },
  {
    id: 'integrations',
    name: 'Integrations',
    description: 'Third-party integrations and APIs',
    icon: '🔗',
    order: 6,
    isActive: true,
    entryCount: 7,
  },
];

// Mock FAQ Entries
export const mockFAQEntries: FAQEntry[] = [
  // Getting Started
  {
    id: 'faq-1',
    question: 'How do I create my first channel?',
    answer: 'To create a new channel:\n1. Click the "+" button next to Channels in the sidebar\n2. Choose between Public or Private channel\n3. Enter a channel name and description\n4. Add team members if needed\n5. Click "Create Channel"\n\nYour new channel will appear in the sidebar and you can start messaging immediately!',
    keywords: ['create', 'channel', 'new', 'first', 'setup'],
    category: 'getting-started',
    tags: ['channels', 'setup', 'beginner'],
    confidence: 0.95,
    usage: 45,
    lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-25'),
    isActive: true,
    relatedEntries: ['faq-2', 'faq-15'],
    metadata: {
      difficulty: 'beginner',
      estimatedTime: '2 minutes',
    },
  },
  {
    id: 'faq-2',
    question: 'How do I invite team members to a channel?',
    answer: 'To invite team members to a channel:\n1. Open the channel you want to add members to\n2. Click the channel name at the top to open channel settings\n3. Select "Members" tab\n4. Click "Add Members" button\n5. Search for users by name or email\n6. Select the users you want to invite\n7. Click "Add Selected Members"\n\nInvited members will receive a notification and can immediately access the channel.',
    keywords: ['invite', 'members', 'add', 'team', 'users', 'channel'],
    category: 'getting-started',
    tags: ['members', 'invitation', 'channels'],
    confidence: 0.92,
    usage: 38,
    lastUsed: new Date(Date.now() - 5 * 60 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-20'),
    isActive: true,
    relatedEntries: ['faq-1', 'faq-16'],
    metadata: {
      difficulty: 'beginner',
      estimatedTime: '3 minutes',
    },
  },
  {
    id: 'faq-3',
    question: 'How do I reset my password?',
    answer: 'To reset your password:\n1. Go to the login page\n2. Click "Forgot Password?" link\n3. Enter your email address\n4. Check your email for a reset link\n5. Click the link in the email\n6. Enter your new password\n7. Confirm the new password\n8. Click "Reset Password"\n\nIf you don\'t receive the email, check your spam folder or contact support.',
    keywords: ['password', 'reset', 'forgot', 'login', 'email'],
    category: 'account-management',
    tags: ['password', 'security', 'login'],
    confidence: 0.98,
    usage: 67,
    lastUsed: new Date(Date.now() - 1 * 60 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-22'),
    isActive: true,
    relatedEntries: ['faq-4', 'faq-5'],
    metadata: {
      difficulty: 'beginner',
      estimatedTime: '5 minutes',
    },
  },
  {
    id: 'faq-4',
    question: 'How do I change my profile picture?',
    answer: 'To change your profile picture:\n1. Click your avatar in the top-right corner\n2. Select "Profile Settings" from the dropdown\n3. Click on your current profile picture\n4. Choose "Upload New Photo" or "Choose from Gallery"\n5. Select your desired image file\n6. Crop the image if needed\n7. Click "Save Changes"\n\nSupported formats: JPG, PNG, GIF (max 5MB). Your new picture will appear across all channels immediately.',
    keywords: ['profile', 'picture', 'avatar', 'photo', 'change', 'upload'],
    category: 'account-management',
    tags: ['profile', 'avatar', 'settings'],
    confidence: 0.94,
    usage: 29,
    lastUsed: new Date(Date.now() - 3 * 60 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-12'),
    isActive: true,
    relatedEntries: ['faq-3', 'faq-6'],
    metadata: {
      difficulty: 'beginner',
      estimatedTime: '3 minutes',
    },
  },
  {
    id: 'faq-5',
    question: 'How do I enable two-factor authentication?',
    answer: 'To enable two-factor authentication (2FA):\n1. Go to Profile Settings > Security\n2. Click "Enable Two-Factor Authentication"\n3. Download an authenticator app (Google Authenticator, Authy, etc.)\n4. Scan the QR code with your authenticator app\n5. Enter the 6-digit code from your app\n6. Save your backup codes in a secure location\n7. Click "Enable 2FA"\n\n2FA adds an extra layer of security to your account and is highly recommended.',
    keywords: ['two-factor', '2fa', 'authentication', 'security', 'enable'],
    category: 'account-management',
    tags: ['security', '2fa', 'authentication'],
    confidence: 0.96,
    usage: 23,
    lastUsed: new Date(Date.now() - 6 * 60 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-15'),
    isActive: true,
    relatedEntries: ['faq-3'],
    metadata: {
      difficulty: 'intermediate',
      estimatedTime: '10 minutes',
    },
  },
  // Messaging & Communication
  {
    id: 'faq-6',
    question: 'How do I format messages with bold, italic, or code?',
    answer: 'You can format your messages using markdown syntax:\n\n**Bold text**: Wrap text with **asterisks** or __underscores__\n*Italic text*: Wrap text with *single asterisks* or _single underscores_\n`Code`: Wrap text with `backticks`\n```\nCode blocks: Wrap with triple backticks\n```\n~~Strikethrough~~: Wrap with ~~double tildes~~\n\nYou can also use the formatting toolbar above the message input for easier formatting.',
    keywords: ['format', 'bold', 'italic', 'code', 'markdown', 'styling'],
    category: 'messaging',
    tags: ['formatting', 'markdown', 'text'],
    confidence: 0.97,
    usage: 52,
    lastUsed: new Date(Date.now() - 30 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-11'),
    isActive: true,
    relatedEntries: ['faq-7', 'faq-8'],
    metadata: {
      difficulty: 'beginner',
      estimatedTime: '2 minutes',
    },
  },
  {
    id: 'faq-7',
    question: 'How do I mention someone in a message?',
    answer: 'To mention someone in a message:\n1. Type @ followed by their name\n2. Select the person from the dropdown that appears\n3. Their name will appear highlighted in blue\n4. Send your message\n\nThe mentioned person will receive a notification. You can also mention @everyone to notify all channel members, or @here to notify only active members.',
    keywords: ['mention', 'notify', '@', 'everyone', 'here', 'notification'],
    category: 'messaging',
    tags: ['mentions', 'notifications', 'communication'],
    confidence: 0.95,
    usage: 41,
    lastUsed: new Date(Date.now() - 45 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-11'),
    isActive: true,
    relatedEntries: ['faq-6', 'faq-9'],
    metadata: {
      difficulty: 'beginner',
      estimatedTime: '1 minute',
    },
  },
  {
    id: 'faq-8',
    question: 'How do I share files and attachments?',
    answer: 'To share files:\n1. Click the paperclip icon in the message input\n2. Choose "Upload from Computer" or drag files directly into the chat\n3. Select one or multiple files\n4. Add a message if desired\n5. Click Send\n\nSupported file types: Images, documents, videos, audio files (max 100MB per file). Files are automatically scanned for security and can be previewed inline.',
    keywords: ['files', 'attachments', 'upload', 'share', 'documents'],
    category: 'messaging',
    tags: ['files', 'attachments', 'sharing'],
    confidence: 0.93,
    usage: 35,
    lastUsed: new Date(Date.now() - 1 * 60 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-12'),
    isActive: true,
    relatedEntries: ['faq-6', 'faq-10'],
    metadata: {
      difficulty: 'beginner',
      estimatedTime: '2 minutes',
    },
  },
  // Technical Support
  {
    id: 'faq-9',
    question: 'Why am I not receiving notifications?',
    answer: 'If you\'re not receiving notifications, check these settings:\n\n1. **Browser notifications**: Ensure notifications are enabled for this site in your browser settings\n2. **App notifications**: Go to Settings > Notifications and verify your preferences\n3. **Channel settings**: Check if notifications are muted for specific channels\n4. **Do Not Disturb**: Make sure DND mode is not enabled\n5. **Email notifications**: Verify your email settings in Profile > Notifications\n\nIf issues persist, try logging out and back in, or contact support.',
    keywords: ['notifications', 'not receiving', 'alerts', 'settings', 'muted'],
    category: 'technical',
    tags: ['notifications', 'troubleshooting', 'settings'],
    confidence: 0.91,
    usage: 73,
    lastUsed: new Date(Date.now() - 20 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-09'),
    updatedAt: new Date('2024-01-28'),
    isActive: true,
    relatedEntries: ['faq-7', 'faq-11'],
    metadata: {
      difficulty: 'intermediate',
      estimatedTime: '5 minutes',
    },
  },
  {
    id: 'faq-10',
    question: 'How do I troubleshoot connection issues?',
    answer: 'If you\'re experiencing connection problems:\n\n1. **Check your internet**: Verify your internet connection is stable\n2. **Refresh the page**: Try refreshing your browser or restarting the app\n3. **Clear cache**: Clear your browser cache and cookies\n4. **Check server status**: Visit our status page for any ongoing issues\n5. **Disable extensions**: Try disabling browser extensions temporarily\n6. **Try incognito mode**: Test in a private/incognito browser window\n\nIf problems persist, contact support with your browser version and error details.',
    keywords: ['connection', 'issues', 'troubleshoot', 'offline', 'network'],
    category: 'technical',
    tags: ['connection', 'troubleshooting', 'network'],
    confidence: 0.89,
    usage: 56,
    lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
    createdBy: '1',
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-26'),
    isActive: true,
    relatedEntries: ['faq-9', 'faq-12'],
    metadata: {
      difficulty: 'intermediate',
      estimatedTime: '10 minutes',
    },
  },
];

// Mock automation rules
export const mockAutomationRules: AutomationRule[] = [
  {
    id: 'auto-welcome',
    name: 'Welcome New Members',
    description: 'Automatically welcome new team members when they join a channel',
    trigger: {
      type: 'user_joined',
      config: {
        channels: ['general'],
      },
    },
    conditions: [],
    actions: [
      {
        type: 'send_message',
        config: {
          channelId: 'general',
          content: 'Welcome to the team, {{user.name}}! 👋 Feel free to introduce yourself and ask any questions.',
        },
        delay: 2000,
      },
    ],
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-10'),
    lastExecuted: new Date(),
    executionCount: 23,
  },
  {
    id: 'auto-urgent-notify',
    name: 'Urgent Message Notification',
    description: 'Send email notifications for messages marked as urgent',
    trigger: {
      type: 'message_sent',
      config: {
        keywords: ['urgent', 'emergency', 'critical'],
      },
    },
    conditions: [
      {
        field: 'message.content',
        operator: 'contains',
        value: 'urgent',
      },
    ],
    actions: [
      {
        type: 'email',
        config: {
          recipients: ['<EMAIL>'],
          subject: 'Urgent Message Alert',
          template: 'urgent_message',
        },
      },
    ],
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-12'),
    lastExecuted: new Date(Date.now() - 2 * 60 * 60 * 1000),
    executionCount: 7,
  },
  {
    id: 'auto-keyword-escalation',
    name: 'Keyword-based Escalation',
    description: 'Automatically escalate messages containing critical keywords',
    trigger: {
      type: 'keyword_detected',
      config: {
        keywords: ['urgent', 'critical', 'emergency', 'down', 'broken', 'not working'],
        channels: ['support', 'general'],
        caseSensitive: false
      },
    },
    conditions: [
      {
        field: 'message.content',
        operator: 'contains',
        value: 'help'
      }
    ],
    actions: [
      {
        type: 'send_notification',
        config: {
          notificationType: 'email',
          recipients: ['<EMAIL>'],
          notificationContent: 'Urgent message detected: {{message.content}}'
        }
      },
      {
        type: 'send_message',
        config: {
          channelId: 'support-escalation',
          content: '🚨 **URGENT ESCALATION** 🚨\n\nUser: {{user.name}}\nChannel: {{channel.name}}\nMessage: {{message.content}}\nTime: {{timestamp}}'
        },
        delay: 1000
      }
    ],
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-02-01'),
    lastExecuted: new Date('2024-02-15'),
    executionCount: 8,
  },
  {
    id: 'auto-inactive-reminder',
    name: 'Inactive User Reminder',
    description: 'Send reminders to users who have been inactive for too long',
    trigger: {
      type: 'user_inactive',
      config: {
        inactivityThreshold: 1440, // 24 hours in minutes
        channels: ['development', 'design'],
        excludeWeekends: true
      },
    },
    conditions: [
      {
        field: 'user.role',
        operator: 'in',
        value: ['member', 'lead']
      }
    ],
    actions: [
      {
        type: 'send_message',
        config: {
          userId: '{{user.id}}',
          content: 'Hi {{user.name}}! 👋 We noticed you haven\'t been active in {{channel.name}} for a while. Is everything okay? Let us know if you need any help or support!'
        }
      }
    ],
    enabled: true,
    createdBy: '2',
    createdAt: new Date('2024-01-20'),
    lastExecuted: new Date('2024-02-12'),
    executionCount: 15,
  },
  {
    id: 'auto-file-backup',
    name: 'Automatic File Backup',
    description: 'Automatically backup important files to external storage',
    trigger: {
      type: 'file_uploaded',
      config: {
        fileTypes: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
        maxFileSize: 10485760, // 10MB
        channels: ['documents', 'reports']
      },
    },
    conditions: [
      {
        field: 'file.size',
        operator: 'greater_than',
        value: 1048576 // 1MB
      }
    ],
    actions: [
      {
        type: 'webhook',
        config: {
          url: 'https://backup-service.company.com/api/backup',
          method: 'POST',
          headers: {
            'Authorization': 'Bearer {{backup_token}}',
            'Content-Type': 'application/json'
          },
          body: {
            fileId: '{{file.id}}',
            fileName: '{{file.name}}',
            channelId: '{{channel.id}}',
            uploadedBy: '{{user.id}}',
            timestamp: '{{timestamp}}'
          }
        }
      },
      {
        type: 'send_message',
        config: {
          channelId: '{{channel.id}}',
          content: '📁 File "{{file.name}}" has been automatically backed up to secure storage.'
        },
        delay: 3000
      }
    ],
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-25'),
    lastExecuted: new Date('2024-02-14'),
    executionCount: 42,
  }
];

// Mock scheduled messages
export const mockScheduledMessages: ScheduledMessage[] = [
  {
    id: 'sched-1',
    content: 'Daily standup reminder: Please share your updates in the #development channel! 📝',
    channelId: 'development',
    authorId: '1',
    scheduledFor: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    status: 'pending',
    recurring: {
      type: 'daily',
      interval: 1,
      daysOfWeek: [1, 2, 3, 4, 5], // Weekdays only
    },
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'sched-2',
    content: 'Weekly team meeting in 30 minutes! Join the video call in #general 📹',
    channelId: 'general',
    authorId: '1',
    scheduledFor: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
    status: 'pending',
    recurring: {
      type: 'weekly',
      interval: 1,
      daysOfWeek: [1], // Mondays
    },
    createdAt: new Date('2024-01-20'),
  },
  {
    id: 'sched-template-welcome',
    content: 'Welcome to {{channelName}}, {{userName}}! 👋 We\'re excited to have you join our team.',
    channelId: 'general',
    authorId: 'bot-welcome',
    scheduledFor: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    status: 'pending',
    recurring: {
      type: 'custom',
      interval: 1,
      cronExpression: '0 9 * * 1', // Every Monday at 9 AM
      timezone: 'America/New_York',
      endDate: new Date('2024-12-31'),
      maxOccurrences: 50
    },
    createdAt: new Date('2024-02-01'),
  },
  {
    id: 'sched-celebration',
    content: '🎉 Congratulations team! We\'ve reached {{milestone}} this week! Keep up the amazing work! 💪',
    channelId: 'general',
    authorId: '1',
    scheduledFor: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
    status: 'pending',
    recurring: {
      type: 'weekly',
      interval: 1,
      daysOfWeek: [5], // Friday
      timezone: 'UTC',
      skipHolidays: true,
      skipWeekends: false
    },
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'sched-conditional',
    content: 'Monthly team sync meeting in 30 minutes! 📅 Join us in the main conference room or via video call.',
    channelId: 'general',
    authorId: '2',
    scheduledFor: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 1 month from now
    status: 'pending',
    recurring: {
      type: 'monthly',
      interval: 1,
      daysOfMonth: [1], // First day of each month
      timezone: 'America/Los_Angeles',
      endDate: new Date('2024-12-31')
    },
    createdAt: new Date('2024-01-20'),
  },
  {
    id: 'sched-complex-cron',
    content: '⏰ End of sprint reminder: Please complete your tasks and update your status before EOD.',
    channelId: 'development',
    authorId: 'bot-scrum',
    scheduledFor: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 2 weeks from now
    status: 'pending',
    recurring: {
      type: 'cron',
      interval: 1,
      cronExpression: '0 17 * * 5', // Every Friday at 5 PM
      timezone: 'Europe/London',
      skipHolidays: true,
      maxOccurrences: 26 // Roughly one year of sprints
    },
    createdAt: new Date('2024-01-10'),
  },
  {
    id: 'sched-dynamic-content',
    content: 'Good morning team! Today is {{date}} and we have {{taskCount}} tasks in progress. Let\'s make it a productive day! ☀️',
    channelId: 'general',
    authorId: 'bot-motivator',
    scheduledFor: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    status: 'pending',
    recurring: {
      type: 'daily',
      interval: 1,
      timezone: 'America/New_York',
      skipWeekends: true,
      endDate: new Date('2024-12-31')
    },
    createdAt: new Date('2024-02-01'),
  }
];

// Mock workflows
export const mockWorkflows = [
  {
    id: 'workflow-onboarding',
    name: 'New Employee Onboarding',
    description: 'Automated workflow for onboarding new team members',
    version: '1.2.0',
    steps: [
      {
        id: 'step-welcome',
        name: 'Send Welcome Message',
        type: 'action',
        config: {
          actionType: 'send_message',
          content: 'Welcome to the team, {{user.name}}! 🎉',
          channelId: 'general'
        },
        nextSteps: ['step-assign-buddy'],
        timeout: 30000
      },
      {
        id: 'step-assign-buddy',
        name: 'Assign Onboarding Buddy',
        type: 'action',
        config: {
          actionType: 'assign_buddy',
          buddyRole: 'senior_member'
        },
        nextSteps: ['step-create-tasks'],
        timeout: 60000
      },
      {
        id: 'step-create-tasks',
        name: 'Create Onboarding Tasks',
        type: 'action',
        config: {
          actionType: 'create_tasks',
          tasks: [
            'Complete profile setup',
            'Read team handbook',
            'Schedule 1:1 with manager',
            'Join relevant channels'
          ]
        },
        nextSteps: ['step-schedule-checkin'],
        timeout: 30000
      },
      {
        id: 'step-schedule-checkin',
        name: 'Schedule Check-in',
        type: 'action',
        config: {
          actionType: 'schedule_message',
          content: 'How is your first week going, {{user.name}}? Let us know if you need any help! 😊',
          delayDays: 7
        },
        nextSteps: [],
        timeout: 30000
      }
    ],
    triggers: [
      {
        id: 'trigger-user-joined',
        type: 'event',
        config: {
          eventType: 'user_joined',
          eventFilters: {
            'channel.type': 'public',
            'user.role': 'member'
          }
        },
        enabled: true
      }
    ],
    variables: [
      {
        name: 'user',
        type: 'object',
        required: true,
        description: 'The user being onboarded'
      },
      {
        name: 'channel',
        type: 'object',
        required: true,
        description: 'The channel where user joined'
      }
    ],
    settings: {
      maxConcurrentExecutions: 5,
      executionTimeout: 3600000, // 1 hour
      errorHandling: 'continue',
      logging: true,
      notifications: {
        onSuccess: true,
        onFailure: true,
        recipients: ['<EMAIL>'],
        channels: ['hr-notifications']
      }
    },
    status: 'active',
    createdBy: '1',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-02-01'),
    lastExecuted: new Date('2024-02-15'),
    executionCount: 23,
    analytics: {
      totalExecutions: 23,
      successfulExecutions: 21,
      failedExecutions: 2,
      averageExecutionTime: 45000,
      lastExecutionStatus: 'success',
      lastExecutionTime: new Date('2024-02-15'),
      errorRate: 8.7,
      performanceMetrics: {
        'step-welcome': 2000,
        'step-assign-buddy': 15000,
        'step-create-tasks': 8000,
        'step-schedule-checkin': 1000
      }
    }
  },
  {
    id: 'workflow-incident-response',
    name: 'Incident Response Workflow',
    description: 'Automated workflow for handling critical incidents',
    version: '2.1.0',
    steps: [
      {
        id: 'step-detect-incident',
        name: 'Detect Incident',
        type: 'condition',
        config: {
          conditions: [
            {
              field: 'message.content',
              operator: 'contains',
              value: 'critical'
            },
            {
              field: 'message.content',
              operator: 'contains',
              value: 'down'
            }
          ],
          operator: 'OR'
        },
        nextSteps: ['step-create-incident-channel'],
        timeout: 10000
      },
      {
        id: 'step-create-incident-channel',
        name: 'Create Incident Channel',
        type: 'action',
        config: {
          actionType: 'create_channel',
          channelName: 'incident-{{timestamp}}',
          channelType: 'private',
          inviteUsers: ['incident-team']
        },
        nextSteps: ['step-notify-oncall'],
        timeout: 30000
      },
      {
        id: 'step-notify-oncall',
        name: 'Notify On-Call Team',
        type: 'parallel',
        config: {
          actions: [
            {
              type: 'send_notification',
              config: {
                notificationType: 'sms',
                recipients: ['oncall-primary', 'oncall-secondary'],
                content: 'CRITICAL INCIDENT: {{incident.description}}'
              }
            },
            {
              type: 'send_message',
              config: {
                channelId: 'incident-response',
                content: '🚨 INCIDENT DETECTED 🚨\n\nSeverity: Critical\nDescription: {{incident.description}}\nChannel: {{incident.channel}}'
              }
            }
          ]
        },
        nextSteps: ['step-start-bridge'],
        timeout: 60000
      },
      {
        id: 'step-start-bridge',
        name: 'Start Incident Bridge',
        type: 'webhook',
        config: {
          url: 'https://incident-bridge.company.com/api/start',
          method: 'POST',
          headers: {
            'Authorization': 'Bearer {{bridge_token}}'
          },
          body: {
            incidentId: '{{incident.id}}',
            severity: 'critical',
            description: '{{incident.description}}'
          }
        },
        nextSteps: [],
        timeout: 30000
      }
    ],
    triggers: [
      {
        id: 'trigger-critical-keyword',
        type: 'event',
        config: {
          eventType: 'message_sent',
          eventFilters: {
            'message.content': 'critical|emergency|down|outage'
          }
        },
        enabled: true
      }
    ],
    variables: [
      {
        name: 'incident',
        type: 'object',
        required: true,
        description: 'Incident details'
      },
      {
        name: 'timestamp',
        type: 'string',
        defaultValue: '{{now}}',
        description: 'Current timestamp'
      }
    ],
    settings: {
      maxConcurrentExecutions: 10,
      executionTimeout: 1800000, // 30 minutes
      errorHandling: 'stop',
      logging: true,
      notifications: {
        onSuccess: true,
        onFailure: true,
        onTimeout: true,
        recipients: ['<EMAIL>'],
        channels: ['incident-response']
      }
    },
    status: 'active',
    createdBy: '2',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-02-10'),
    lastExecuted: new Date('2024-02-14'),
    executionCount: 7,
    analytics: {
      totalExecutions: 7,
      successfulExecutions: 6,
      failedExecutions: 1,
      averageExecutionTime: 180000,
      lastExecutionStatus: 'success',
      lastExecutionTime: new Date('2024-02-14'),
      errorRate: 14.3,
      performanceMetrics: {
        'step-detect-incident': 1000,
        'step-create-incident-channel': 25000,
        'step-notify-oncall': 45000,
        'step-start-bridge': 8000
      }
    }
  }
];

// Mock message archives
export const mockMessageArchives: MessageArchive[] = [
  {
    id: 'archive-1',
    messageId: 'msg-old-1',
    channelId: 'general',
    authorId: '2',
    content: 'This is an archived message from last year',
    timestamp: new Date('2023-06-15T10:30:00'),
    archivedAt: new Date('2024-01-01'),
    retentionPolicy: 'policy-1',
    metadata: {
      originalFormat: 'json',
      encryption: true,
      checksum: 'sha256:abc123...',
      tags: ['archived', 'general'],
      legalHold: false,
      exportable: true,
    },
  },
  {
    id: 'archive-2',
    messageId: 'msg-old-2',
    channelId: 'development',
    authorId: '3',
    content: 'Code review completed for PR #123',
    timestamp: new Date('2023-08-20T14:15:00'),
    archivedAt: new Date('2024-01-01'),
    retentionPolicy: 'policy-2',
    metadata: {
      originalFormat: 'json',
      encryption: true,
      checksum: 'sha256:def456...',
      tags: ['archived', 'development', 'code-review'],
      legalHold: true,
      exportable: true,
    },
  },
];

// Mock retention policies
export const mockRetentionPolicies: RetentionPolicyRule[] = [
  {
    id: 'policy-1',
    name: 'General Channel Retention',
    description: 'Archive messages in general channels after 1 year',
    scope: {
      channels: ['general'],
      users: [],
      messageTypes: ['text', 'file'],
    },
    duration: 365,
    action: 'archive',
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    lastApplied: new Date('2024-01-01'),
  },
  {
    id: 'policy-2',
    name: 'Development Channel Retention',
    description: 'Archive development messages after 2 years',
    scope: {
      channels: ['development'],
      users: [],
      messageTypes: ['text', 'file', 'image'],
    },
    duration: 730,
    action: 'archive',
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    lastApplied: new Date('2024-01-01'),
  },
];

// Mock legal holds
export const mockLegalHolds: LegalHold[] = [
  {
    id: 'hold-1',
    name: 'Project Alpha Investigation',
    description: 'Legal hold for Project Alpha related communications',
    scope: {
      users: ['2', '3'],
      channels: ['development'],
      keywords: ['project alpha', 'alpha project'],
      dateRange: {
        from: new Date('2023-06-01'),
        to: new Date('2023-12-31'),
      },
    },
    status: 'active',
    createdBy: '1',
    createdAt: new Date('2024-01-05'),
    reason: 'Litigation hold for ongoing legal proceedings',
  },
];

// Mock export requests
export const mockExportRequests: ExportRequest[] = [
  {
    id: 'export-1',
    name: 'Q4 2023 Communications Export',
    format: 'json',
    scope: {
      channels: ['general', 'development'],
      users: [],
      dateRange: {
        from: new Date('2023-10-01'),
        to: new Date('2023-12-31'),
      },
      includeAttachments: true,
      includeDeleted: false,
    },
    status: 'completed',
    requestedBy: '1',
    requestedAt: new Date('2024-01-10'),
    completedAt: new Date('2024-01-10T10:30:00'),
    downloadUrl: '/api/exports/export-1/download',
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    options: {
      anonymizeUsers: false,
      includeMetadata: true,
      compression: true,
    },
  },
  {
    id: 'export-2',
    name: 'Legal Hold Export',
    format: 'pdf',
    scope: {
      channels: ['development'],
      users: ['2', '3'],
      dateRange: {
        from: new Date('2023-06-01'),
        to: new Date('2023-12-31'),
      },
      includeAttachments: false,
      includeDeleted: true,
    },
    status: 'processing',
    requestedBy: '1',
    requestedAt: new Date(),
    options: {
      anonymizeUsers: false,
      includeMetadata: true,
      compression: false,
      watermark: 'CONFIDENTIAL - LEGAL HOLD',
    },
  },
];

// Mock external integrations
export const mockExternalIntegrations: ExternalIntegration[] = [
  {
    id: 'int-slack',
    name: 'Slack Integration',
    type: 'slack',
    status: 'active',
    config: {
      syncDirection: 'bidirectional',
      syncFrequency: 5,
      autoSync: true,
      retryAttempts: 3,
      timeout: 30,
      batchSize: 100,
      filters: [
        {
          field: 'channel',
          operator: 'equals',
          value: 'general',
          exclude: false,
        },
      ],
    },
    credentials: {
      type: 'oauth',
      data: {}, // Encrypted
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    },
    mapping: {
      userMapping: {
        'slack-user-1': '1',
        'slack-user-2': '2',
      },
      channelMapping: {
        'slack-general': 'general',
      },
      messageMapping: {},
      customFields: {},
    },
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-15'),
    lastSync: new Date(),
    syncStats: {
      totalSynced: 1250,
      successfulSyncs: 1230,
      failedSyncs: 20,
      averageSyncTime: 2500,
    },
  },
];

// Mock ERP integrations
export const mockERPIntegrations: ERPIntegration[] = [
  {
    id: 'erp-crm-1',
    module: 'CRM',
    recordType: 'lead',
    recordId: 'lead-123',
    channelId: 'sales',
    linkType: 'reference',
    config: {
      autoNotify: true,
      syncFields: ['status', 'value', 'owner'],
      notificationEvents: ['status_change', 'value_update'],
      accessPermissions: ['read', 'write'],
    },
    createdAt: new Date('2024-01-20'),
    lastUpdated: new Date(),
  },
];

// Mock webhooks
export const mockWebhooks: WebhookEndpoint[] = [
  {
    id: 'webhook-1',
    name: 'External System Notifications',
    url: 'https://external-system.com/webhooks/discuss',
    events: [
      { type: 'message_created' },
      { type: 'channel_created' },
    ],
    headers: {
      'Authorization': 'Bearer token123',
      'Content-Type': 'application/json',
    },
    secret: 'webhook-secret-123',
    enabled: true,
    retryPolicy: {
      maxRetries: 3,
      backoffMultiplier: 2,
      maxBackoffSeconds: 300,
    },
    createdBy: '1',
    createdAt: new Date('2024-01-18'),
    lastTriggered: new Date(),
    stats: {
      totalDeliveries: 45,
      successfulDeliveries: 42,
      failedDeliveries: 3,
      averageResponseTime: 150,
    },
  },
];

// Mock API keys
export const mockAPIKeys: APIKey[] = [
  {
    id: 'api-key-1',
    name: 'Mobile App Integration',
    key: 'hashed-key-value', // This would be hashed
    permissions: [
      {
        resource: 'messages',
        actions: ['read', 'create'],
        scope: { channels: ['general'] },
      },
      {
        resource: 'channels',
        actions: ['read'],
      },
    ],
    rateLimit: {
      maxRequestsPerMinute: 100,
      maxRequestsPerHour: 1000,
      cooldownPeriod: 60,
    },
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-22'),
    lastUsed: new Date(),
    usage: {
      totalRequests: 2340,
      requestsToday: 156,
      requestsThisMonth: 4567,
      averageResponseTime: 85,
      errorRate: 0.02,
      lastRequest: new Date(),
    },
  },
];

// Helper functions for bots
export const getMockBotById = (id: string): Bot | undefined => {
  return mockBots.find(bot => bot.id === id);
};

export const getMockBotsByType = (type: Bot['type']): Bot[] => {
  return mockBots.filter(bot => bot.type === type);
};

export const getMockEnabledBots = (): Bot[] => {
  return mockBots.filter(bot => bot.enabled);
};

// Helper functions for automation
export const getMockAutomationRuleById = (id: string): AutomationRule | undefined => {
  return mockAutomationRules.find(rule => rule.id === id);
};

export const getMockEnabledAutomationRules = (): AutomationRule[] => {
  return mockAutomationRules.filter(rule => rule.enabled);
};

// Helper functions for scheduled messages
export const getMockScheduledMessageById = (id: string): ScheduledMessage | undefined => {
  return mockScheduledMessages.find(msg => msg.id === id);
};

export const getMockPendingScheduledMessages = (): ScheduledMessage[] => {
  return mockScheduledMessages.filter(msg => msg.status === 'pending');
};

export const getMockScheduledMessagesByChannel = (channelId: string): ScheduledMessage[] => {
  return mockScheduledMessages.filter(msg => msg.channelId === channelId);
};

// Helper functions for archives
export const getMockArchivedMessageById = (id: string): MessageArchive | undefined => {
  return mockMessageArchives.find(archive => archive.id === id);
};

export const getMockArchivedMessagesByChannel = (channelId: string): MessageArchive[] => {
  return mockMessageArchives.filter(archive => archive.channelId === channelId);
};

export const getMockArchivedMessagesByUser = (userId: string): MessageArchive[] => {
  return mockMessageArchives.filter(archive => archive.authorId === userId);
};

// Helper functions for retention policies
export const getMockRetentionPolicyById = (id: string): RetentionPolicyRule | undefined => {
  return mockRetentionPolicies.find(policy => policy.id === id);
};

export const getMockEnabledRetentionPolicies = (): RetentionPolicyRule[] => {
  return mockRetentionPolicies.filter(policy => policy.enabled);
};

// Helper functions for legal holds
export const getMockLegalHoldById = (id: string): LegalHold | undefined => {
  return mockLegalHolds.find(hold => hold.id === id);
};

export const getMockActiveLegalHolds = (): LegalHold[] => {
  return mockLegalHolds.filter(hold => hold.status === 'active');
};

// Helper functions for exports
export const getMockExportRequestById = (id: string): ExportRequest | undefined => {
  return mockExportRequests.find(request => request.id === id);
};

export const getMockExportRequestsByUser = (userId: string): ExportRequest[] => {
  return mockExportRequests.filter(request => request.requestedBy === userId);
};

export const getMockExportRequestsByStatus = (status: ExportRequest['status']): ExportRequest[] => {
  return mockExportRequests.filter(request => request.status === status);
};

// Helper functions for integrations
export const getMockIntegrationById = (id: string): ExternalIntegration | undefined => {
  return mockExternalIntegrations.find(integration => integration.id === id);
};

export const getMockIntegrationsByType = (type: ExternalIntegration['type']): ExternalIntegration[] => {
  return mockExternalIntegrations.filter(integration => integration.type === type);
};

export const getMockEnabledIntegrations = (): ExternalIntegration[] => {
  return mockExternalIntegrations.filter(integration => integration.enabled);
};

// Helper functions for ERP integrations
export const getMockERPIntegrationById = (id: string): ERPIntegration | undefined => {
  return mockERPIntegrations.find(integration => integration.id === id);
};

export const getMockERPIntegrationsByModule = (module: string): ERPIntegration[] => {
  return mockERPIntegrations.filter(integration => integration.module === module);
};

export const getMockERPIntegrationsByChannel = (channelId: string): ERPIntegration[] => {
  return mockERPIntegrations.filter(integration => integration.channelId === channelId);
};

// Helper functions for webhooks
export const getMockWebhookById = (id: string): WebhookEndpoint | undefined => {
  return mockWebhooks.find(webhook => webhook.id === id);
};

export const getMockEnabledWebhooks = (): WebhookEndpoint[] => {
  return mockWebhooks.filter(webhook => webhook.enabled);
};

// Helper functions for API keys
export const getMockAPIKeyById = (id: string): APIKey | undefined => {
  return mockAPIKeys.find(key => key.id === id);
};

export const getMockEnabledAPIKeys = (): APIKey[] => {
  return mockAPIKeys.filter(key => key.enabled);
};

export const getMockAPIKeysByUser = (userId: string): APIKey[] => {
  return mockAPIKeys.filter(key => key.createdBy === userId);
};

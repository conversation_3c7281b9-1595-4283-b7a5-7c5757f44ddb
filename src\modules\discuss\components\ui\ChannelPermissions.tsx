import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { getMockUserById } from '../../../../mocks/data/discuss';
import type { User, Channel } from '../../types';

export interface ChannelRole {
  id: string;
  name: string;
  permissions: ChannelPermission[];
  color: string;
  isDefault?: boolean;
}

export interface ChannelPermission {
  id: string;
  name: string;
  description: string;
  category: 'messaging' | 'moderation' | 'management' | 'calls';
}

export interface UserChannelRole {
  userId: string;
  roleId: string;
  assignedAt: Date;
  assignedBy: string;
}

export interface ChannelPermissionsProps {
  channel: Channel;
  currentUserId: string;
  onUpdatePermissions?: (permissions: UserChannelRole[]) => void;
  className?: string;
  'data-testid'?: string;
}

const DEFAULT_PERMISSIONS: ChannelPermission[] = [
  // Messaging permissions
  { id: 'send_messages', name: 'Send Messages', description: 'Can send messages in the channel', category: 'messaging' },
  { id: 'edit_messages', name: 'Edit Messages', description: 'Can edit their own messages', category: 'messaging' },
  { id: 'delete_messages', name: 'Delete Messages', description: 'Can delete their own messages', category: 'messaging' },
  { id: 'upload_files', name: 'Upload Files', description: 'Can upload and share files', category: 'messaging' },
  { id: 'mention_users', name: 'Mention Users', description: 'Can mention other users', category: 'messaging' },
  
  // Moderation permissions
  { id: 'moderate_messages', name: 'Moderate Messages', description: 'Can delete any message', category: 'moderation' },
  { id: 'pin_messages', name: 'Pin Messages', description: 'Can pin and unpin messages', category: 'moderation' },
  { id: 'mute_users', name: 'Mute Users', description: 'Can temporarily mute users', category: 'moderation' },
  { id: 'kick_users', name: 'Kick Users', description: 'Can remove users from channel', category: 'moderation' },
  
  // Management permissions
  { id: 'manage_channel', name: 'Manage Channel', description: 'Can edit channel settings', category: 'management' },
  { id: 'invite_users', name: 'Invite Users', description: 'Can invite new users to channel', category: 'management' },
  { id: 'manage_roles', name: 'Manage Roles', description: 'Can assign and modify user roles', category: 'management' },
  
  // Call permissions
  { id: 'start_calls', name: 'Start Calls', description: 'Can initiate voice/video calls', category: 'calls' },
  { id: 'join_calls', name: 'Join Calls', description: 'Can join ongoing calls', category: 'calls' },
  { id: 'record_calls', name: 'Record Calls', description: 'Can record calls', category: 'calls' },
];

const DEFAULT_ROLES: ChannelRole[] = [
  {
    id: 'member',
    name: 'Member',
    color: '#6B7280',
    isDefault: true,
    permissions: DEFAULT_PERMISSIONS.filter(p => 
      ['send_messages', 'edit_messages', 'delete_messages', 'upload_files', 'mention_users', 'join_calls'].includes(p.id)
    ),
  },
  {
    id: 'moderator',
    name: 'Moderator',
    color: '#3B82F6',
    permissions: DEFAULT_PERMISSIONS.filter(p => 
      !['manage_channel', 'manage_roles'].includes(p.id)
    ),
  },
  {
    id: 'admin',
    name: 'Admin',
    color: '#EF4444',
    permissions: DEFAULT_PERMISSIONS,
  },
];

export const ChannelPermissions: React.FC<ChannelPermissionsProps> = ({
  channel,
  currentUserId,
  onUpdatePermissions,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [userRoles, setUserRoles] = useState<UserChannelRole[]>([]);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load channel members and their roles
  useEffect(() => {
    const loadChannelData = async () => {
      try {
        setIsLoading(true);
        
        // Mock data - in real app, this would come from API
        const mockUserRoles: UserChannelRole[] = [
          { userId: '1', roleId: 'admin', assignedAt: new Date(), assignedBy: '1' },
          { userId: '2', roleId: 'moderator', assignedAt: new Date(), assignedBy: '1' },
          { userId: '3', roleId: 'member', assignedAt: new Date(), assignedBy: '1' },
          { userId: '4', roleId: 'member', assignedAt: new Date(), assignedBy: '1' },
        ];
        
        const users = mockUserRoles.map(ur => getMockUserById(ur.userId)).filter(Boolean) as User[];
        
        setUserRoles(mockUserRoles);
        setAvailableUsers(users);
      } catch (error) {
        console.error('Failed to load channel permissions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadChannelData();
  }, [channel.id]);

  const handleRoleChange = (userId: string, newRoleId: string) => {
    const updatedRoles = userRoles.map(ur => 
      ur.userId === userId 
        ? { ...ur, roleId: newRoleId, assignedAt: new Date(), assignedBy: currentUserId }
        : ur
    );
    
    setUserRoles(updatedRoles);
    onUpdatePermissions?.(updatedRoles);
  };

  const getRoleById = (roleId: string) => {
    return DEFAULT_ROLES.find(role => role.id === roleId);
  };

  const getUserRole = (userId: string) => {
    const userRole = userRoles.find(ur => ur.userId === userId);
    return userRole ? getRoleById(userRole.roleId) : DEFAULT_ROLES.find(r => r.isDefault);
  };

  const getPermissionsByCategory = (category: string) => {
    return DEFAULT_PERMISSIONS.filter(p => p.category === category);
  };

  const hasPermission = (userId: string, permissionId: string) => {
    const role = getUserRole(userId);
    return role?.permissions.some(p => p.id === permissionId) || false;
  };

  if (isLoading) {
    return (
      <div className={`p-6 ${className}`} data-testid={testId}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`} data-testid={testId}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
            Channel Permissions
          </h3>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Manage user roles and permissions for #{channel.name}
          </p>
        </div>

        {/* User Roles */}
        <div>
          <h4 className="font-medium mb-4" style={{ color: colors.text }}>
            User Roles
          </h4>
          <div className="space-y-3">
            {availableUsers.map(user => {
              const role = getUserRole(user.id);
              return (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border"
                  style={{ borderColor: colors.border }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <p className="font-medium" style={{ color: colors.text }}>
                        {user.name}
                      </p>
                      <p className="text-sm" style={{ color: colors.textSecondary }}>
                        {user.email}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <select
                      value={role?.id || 'member'}
                      onChange={(e) => handleRoleChange(user.id, e.target.value)}
                      className="px-3 py-1.5 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                      style={{ 
                        borderColor: colors.border,
                        backgroundColor: colors.background,
                        color: colors.text 
                      }}
                    >
                      {DEFAULT_ROLES.map(role => (
                        <option key={role.id} value={role.id}>
                          {role.name}
                        </option>
                      ))}
                    </select>
                    
                    {role && (
                      <span
                        className="px-2 py-1 text-xs rounded-full text-white"
                        style={{ backgroundColor: role.color }}
                      >
                        {role.name}
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Role Permissions */}
        <div>
          <h4 className="font-medium mb-4" style={{ color: colors.text }}>
            Role Permissions
          </h4>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {['messaging', 'moderation', 'management', 'calls'].map(category => (
              <div key={category} className="space-y-3">
                <h5 className="font-medium capitalize" style={{ color: colors.text }}>
                  {category} Permissions
                </h5>
                
                <div className="space-y-2">
                  {getPermissionsByCategory(category).map(permission => (
                    <div
                      key={permission.id}
                      className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm" style={{ color: colors.text }}>
                          {permission.name}
                        </span>
                        <div className="flex space-x-1">
                          {DEFAULT_ROLES.map(role => (
                            <div
                              key={role.id}
                              className={`w-3 h-3 rounded-full ${
                                role.permissions.some(p => p.id === permission.id)
                                  ? 'opacity-100'
                                  : 'opacity-30'
                              }`}
                              style={{ backgroundColor: role.color }}
                              title={`${role.name}: ${role.permissions.some(p => p.id === permission.id) ? 'Allowed' : 'Denied'}`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-xs" style={{ color: colors.textSecondary }}>
                        {permission.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Legend */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h5 className="font-medium mb-2" style={{ color: colors.text }}>
            Role Legend
          </h5>
          <div className="flex flex-wrap gap-4">
            {DEFAULT_ROLES.map(role => (
              <div key={role.id} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: role.color }}
                />
                <span className="text-sm" style={{ color: colors.text }}>
                  {role.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

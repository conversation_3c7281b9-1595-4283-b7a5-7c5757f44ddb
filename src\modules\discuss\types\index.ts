// Discuss module type definitions

// Core types
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen?: Date;
}

export interface Message {
  id: string;
  content: string;
  authorId: string;
  channelId?: string;
  threadId?: string;
  parentMessageId?: string;
  timestamp: Date;
  editedAt?: Date;
  reactions: Reaction[];
  attachments: Attachment[];
  mentions: string[];
  isDeleted: boolean;
  deliveryStatus: 'sent' | 'delivered' | 'read' | 'failed';
}

export interface Channel {
  id: string;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'direct';
  memberIds: string[];
  createdBy: string;
  createdAt: Date;
  lastActivity?: Date;
  isArchived: boolean;
  settings: ChannelSettings;
}

export interface DirectMessage {
  id: string;
  participantIds: string[];
  lastMessage?: Message;
  lastActivity: Date;
  isArchived: boolean;
}

export interface Team {
  id: string;
  name: string;
  description?: string;
  memberIds: string[];
  channelIds: string[];
  createdBy: string;
  createdAt: Date;
  settings: TeamSettings;
}

export interface Reaction {
  emoji: string;
  userIds: string[];
  count: number;
}

export interface Attachment {
  id: string;
  name: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'other';
  url: string;
  size: number;
  mimeType: string;
}

export interface ChannelSettings {
  notifications: boolean;
  muteUntil?: Date;
  allowFileUploads: boolean;
  allowExternalLinks: boolean;
  retentionPolicy?: RetentionPolicy;
}

export interface TeamSettings {
  visibility: 'public' | 'private';
  joinPolicy: 'open' | 'invite-only' | 'admin-approval';
  allowMemberInvites: boolean;
}

export interface RetentionPolicy {
  enabled: boolean;
  duration: number; // in days
  autoDelete: boolean;
}

export interface NotificationSettings {
  desktop: boolean;
  sound: boolean;
  email: boolean;
  mobile: boolean;
  mentions: boolean;
  directMessages: boolean;
  channels: boolean;
  doNotDisturbStart?: string;
  doNotDisturbEnd?: string;
}

export interface PresenceInfo {
  userId: string;
  status: User['status'];
  lastSeen: Date;
  isTyping: boolean;
  currentChannel?: string;
}

// Event types for real-time updates
export interface MessageEvent {
  type: 'message_created' | 'message_updated' | 'message_deleted';
  message: Message;
  channelId: string;
}

export interface PresenceEvent {
  type: 'user_online' | 'user_offline' | 'user_typing' | 'user_stopped_typing';
  userId: string;
  channelId?: string;
  presence: PresenceInfo;
}

export interface ChannelEvent {
  type: 'channel_created' | 'channel_updated' | 'channel_deleted' | 'user_joined' | 'user_left';
  channel: Channel;
  userId?: string;
}

// API response types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Search types
export interface SearchQuery {
  query: string;
  channelId?: string;
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  messageType?: 'text' | 'file' | 'image' | 'video';
}

export interface SearchResult {
  message: Message;
  channel: Channel;
  author: User;
  highlights: string[];
}

// Integration types
export interface Integration {
  id: string;
  name: string;
  type: 'webhook' | 'bot' | 'external_app';
  enabled: boolean;
  config: Record<string, any>;
}

export interface Webhook {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  enabled: boolean;
}

// Bot types
export interface Bot {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  type: 'faq' | 'ai_assistant' | 'workflow' | 'custom';
  commands: BotCommand[];
  triggers: BotTrigger[];
  config: BotConfig;
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastActive?: Date;
  analytics: BotAnalytics;
}

export interface BotCommand {
  command: string;
  description: string;
  usage: string;
  permissions: string[];
  handler: string; // Function name or endpoint
  parameters?: BotParameter[];
}

export interface BotTrigger {
  id: string;
  type: 'keyword' | 'mention' | 'schedule' | 'event' | 'regex' | 'sentiment' | 'context' | 'user_action' | 'time_based' | 'reaction';
  pattern: string;
  conditions?: TriggerCondition[];
  action: BotAction;
  enabled: boolean;
  priority?: number; // Higher priority triggers are processed first
  cooldown?: number; // Minimum time between trigger activations (in seconds)
  maxExecutions?: number; // Maximum number of times this trigger can execute
  executionCount?: number; // Current execution count
  lastExecuted?: Date;
  config?: TriggerConfig;
}

export interface BotParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'user' | 'channel';
  required: boolean;
  description: string;
  defaultValue?: any;
}

export interface TriggerCondition {
  field: string;
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'regex' | 'greater_than' | 'less_than' | 'in_list' | 'not_in_list' | 'exists' | 'not_exists';
  value: any;
  caseSensitive?: boolean;
  negate?: boolean; // Invert the condition result
}

export interface TriggerConfig {
  // Keyword trigger config
  keywords?: string[];
  caseSensitive?: boolean;
  wholeWordsOnly?: boolean;
  fuzzyMatching?: boolean;
  fuzzyThreshold?: number; // 0-1, similarity threshold for fuzzy matching

  // Sentiment trigger config
  sentimentThreshold?: number; // -1 to 1, negative to positive
  sentimentType?: 'positive' | 'negative' | 'neutral' | 'mixed';

  // Context trigger config
  contextWindow?: number; // Number of previous messages to analyze
  contextKeywords?: string[];
  userHistory?: boolean; // Consider user's message history

  // Time-based trigger config
  timeWindow?: {
    start: string; // HH:MM format
    end: string;   // HH:MM format
    timezone?: string;
    daysOfWeek?: number[]; // 0-6, Sunday = 0
  };

  // User action trigger config
  actionTypes?: ('join' | 'leave' | 'message_edit' | 'message_delete' | 'reaction_add' | 'reaction_remove' | 'status_change')[];

  // Advanced matching
  regexFlags?: string; // Regex flags like 'gi', 'i', etc.
  multiline?: boolean;

  // Response customization
  responseVariables?: Record<string, string>; // Template variables for responses
  randomResponses?: string[]; // Random response selection

  // Rate limiting
  perUserCooldown?: number; // Cooldown per user in seconds
  globalCooldown?: number; // Global cooldown in seconds
}

export interface BotAction {
  type: 'reply' | 'dm' | 'webhook' | 'workflow' | 'function' | 'reaction' | 'thread_reply' | 'forward' | 'schedule_message' | 'create_channel' | 'invite_user' | 'set_status' | 'send_notification' | 'log_event' | 'update_user_data' | 'trigger_integration';
  config: BotActionConfig;
  delay?: number; // in milliseconds
  conditions?: ActionCondition[]; // Conditions that must be met for action to execute
  retryPolicy?: RetryPolicy;
}

export interface BotActionConfig {
  // Reply/DM action config
  content?: string;
  channelId?: string;
  userId?: string;
  threadId?: string;
  mentions?: string[];
  attachments?: string[];

  // Reaction action config
  emoji?: string;
  messageId?: string;

  // Webhook action config
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;

  // Workflow action config
  workflowId?: string;
  workflowParams?: Record<string, any>;

  // Function action config
  functionName?: string;
  parameters?: Record<string, any>;

  // Schedule message action config
  scheduleFor?: Date | string;
  recurring?: RecurrenceConfig;

  // Channel/User management config
  channelName?: string;
  channelType?: 'public' | 'private' | 'direct';
  userIds?: string[];
  permissions?: string[];

  // Status/Notification config
  status?: 'online' | 'away' | 'busy' | 'offline';
  notificationType?: 'desktop' | 'email' | 'sms' | 'push';
  notificationContent?: string;

  // Integration config
  integrationId?: string;
  integrationAction?: string;
  integrationData?: Record<string, any>;

  // Template variables
  variables?: Record<string, any>;
  useTemplating?: boolean;
}

export interface ActionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'exists';
  value: any;
}

export interface RetryPolicy {
  maxRetries: number;
  retryDelay: number; // in milliseconds
  backoffMultiplier?: number; // Exponential backoff multiplier
}

export interface BotConfig {
  permissions: BotPermissions;
  rateLimiting: RateLimitConfig;
  context: ContextConfig;
  nlp?: NLPConfig;
}

export interface BotPermissions {
  channels: string[]; // Channel IDs where bot can operate
  users: string[]; // User IDs who can interact with bot
  commands: string[]; // Commands the bot can execute
  canReadHistory: boolean;
  canSendDM: boolean;
  canMentionUsers: boolean;
}

export interface RateLimitConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  cooldownPeriod: number; // in seconds
}

export interface ContextConfig {
  rememberConversations: boolean;
  contextWindow: number; // Number of previous messages to consider
  persistUserData: boolean;
}

export interface NLPConfig {
  provider: 'openai' | 'anthropic' | 'local' | 'custom';
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
}

// FAQ and Knowledge Base types
export interface FAQEntry {
  id: string;
  question: string;
  answer: string;
  keywords: string[];
  category: string;
  tags: string[];
  confidence: number;
  usage: number;
  lastUsed?: Date;
  createdBy: string;
  createdAt: Date;
  updatedAt?: Date;
  isActive: boolean;
  relatedEntries: string[];
  metadata: Record<string, any>;
}

export interface FAQCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  parentId?: string;
  order: number;
  isActive: boolean;
  entryCount: number;
}

export interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  categories: FAQCategory[];
  entries: FAQEntry[];
  settings: KnowledgeBaseSettings;
  analytics: KnowledgeBaseAnalytics;
  createdBy: string;
  createdAt: Date;
  updatedAt?: Date;
}

export interface KnowledgeBaseSettings {
  autoSuggest: boolean;
  confidenceThreshold: number;
  maxSuggestions: number;
  enableFeedback: boolean;
  enableAnalytics: boolean;
  allowPublicAccess: boolean;
  moderationRequired: boolean;
}

export interface KnowledgeBaseAnalytics {
  totalQueries: number;
  successfulMatches: number;
  averageConfidence: number;
  topQuestions: Array<{ question: string; count: number }>;
  topCategories: Array<{ category: string; count: number }>;
  userSatisfaction: number;
  lastUpdated: Date;
}

// Smart Response types
export interface SmartResponse {
  id: string;
  content: string;
  confidence: number;
  source: 'faq' | 'ai' | 'template' | 'history';
  metadata: Record<string, any>;
  context: ResponseContext;
}

export interface ResponseContext {
  messageId: string;
  channelId: string;
  userId: string;
  conversationHistory: Message[];
  keywords: string[];
  intent: string;
  entities: Array<{ type: string; value: string; confidence: number }>;
}

export interface ResponseSuggestion {
  id: string;
  type: 'quick_reply' | 'faq_answer' | 'ai_response' | 'template';
  content: string;
  confidence: number;
  reasoning: string;
  actions?: Array<{ type: string; label: string; action: string }>;
}

// AI Assistant types
export interface AIConversation {
  id: string;
  userId: string;
  channelId?: string;
  messages: AIMessage[];
  context: AIContext;
  status: 'active' | 'paused' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}

export interface AIMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AIContext {
  userProfile: UserProfile;
  conversationHistory: Message[];
  currentTopic?: string;
  intent?: string;
  entities: Array<{ type: string; value: string; confidence: number }>;
  preferences: AIPreferences;
}

export interface UserProfile {
  id: string;
  name: string;
  role?: string;
  department?: string;
  preferences: Record<string, any>;
  conversationStyle: 'formal' | 'casual' | 'technical';
  expertise: string[];
}

export interface AIPreferences {
  responseLength: 'short' | 'medium' | 'detailed';
  tone: 'professional' | 'friendly' | 'casual';
  includeExamples: boolean;
  includeReferences: boolean;
  language: string;
}

export interface BotAnalytics {
  totalInteractions: number;
  successfulResponses: number;
  failedResponses: number;
  averageResponseTime: number;
  lastInteraction?: Date;
  popularCommands: Record<string, number>;
}

// Automation types
export interface AutomationRule {
  id: string;
  name: string;
  description?: string;
  trigger: AutomationTrigger;
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastExecuted?: Date;
  executionCount: number;
}

export interface AutomationTrigger {
  type: 'message_sent' | 'user_joined' | 'user_left' | 'schedule' | 'webhook' | 'message_reaction' | 'user_status_change' | 'channel_created' | 'file_uploaded' | 'mention_received' | 'keyword_detected' | 'time_based' | 'external_event' | 'user_inactive' | 'message_thread_created';
  config: AutomationTriggerConfig;
}

export interface AutomationTriggerConfig {
  // Message triggers
  keywords?: string[];
  channels?: string[];
  users?: string[];
  messageTypes?: ('text' | 'file' | 'image' | 'video' | 'audio')[];

  // Schedule triggers
  cron?: string; // Cron expression for complex scheduling
  timezone?: string;
  startDate?: Date;
  endDate?: Date;

  // User activity triggers
  inactivityThreshold?: number; // Minutes of inactivity
  statusTypes?: ('online' | 'away' | 'busy' | 'offline')[];

  // Webhook triggers
  webhookUrl?: string;
  webhookSecret?: string;
  expectedPayload?: Record<string, any>;

  // File triggers
  fileTypes?: string[]; // File extensions
  maxFileSize?: number; // In bytes

  // External event triggers
  eventSource?: string;
  eventType?: string;
  eventFilters?: Record<string, any>;
}

export interface AutomationCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: any;
}

export interface AutomationAction {
  type: 'send_message' | 'create_channel' | 'add_user' | 'remove_user' | 'webhook' | 'email';
  config: Record<string, any>;
  delay?: number;
}

// Scheduled message types
export interface ScheduledMessage {
  id: string;
  content: string;
  channelId: string;
  authorId: string;
  scheduledFor: Date;
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  recurring?: RecurrenceConfig;
  createdAt: Date;
  sentAt?: Date;
  error?: string;
}

export interface RecurrenceConfig {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom' | 'cron';
  interval: number;
  daysOfWeek?: number[]; // 0-6, Sunday = 0
  daysOfMonth?: number[]; // 1-31
  monthsOfYear?: number[]; // 1-12
  endDate?: Date;
  maxOccurrences?: number;
  cronExpression?: string; // For complex scheduling
  timezone?: string;
  skipHolidays?: boolean;
  skipWeekends?: boolean;
}

// Workflow types
export interface Workflow {
  id: string;
  name: string;
  description?: string;
  version: string;
  steps: WorkflowStep[];
  triggers: WorkflowTrigger[];
  variables: WorkflowVariable[];
  settings: WorkflowSettings;
  status: 'draft' | 'active' | 'paused' | 'archived';
  createdBy: string;
  createdAt: Date;
  updatedAt?: Date;
  lastExecuted?: Date;
  executionCount: number;
  analytics: WorkflowAnalytics;
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: 'action' | 'condition' | 'loop' | 'parallel' | 'delay' | 'human_approval' | 'webhook' | 'integration';
  config: WorkflowStepConfig;
  nextSteps: string[]; // IDs of next steps
  errorHandling?: ErrorHandling;
  timeout?: number; // in milliseconds
  retryPolicy?: RetryPolicy;
}

export interface WorkflowStepConfig {
  // Action step config
  actionType?: string;
  actionParams?: Record<string, any>;

  // Condition step config
  conditions?: WorkflowCondition[];
  operator?: 'AND' | 'OR';

  // Loop step config
  loopType?: 'for' | 'while' | 'foreach';
  loopCondition?: WorkflowCondition;
  loopVariable?: string;
  loopData?: any[];
  maxIterations?: number;

  // Delay step config
  delayDuration?: number; // in milliseconds
  delayUntil?: Date;

  // Human approval config
  approvers?: string[]; // User IDs
  approvalMessage?: string;
  timeoutAction?: 'approve' | 'reject' | 'skip';

  // Webhook config
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;

  // Integration config
  integrationId?: string;
  integrationAction?: string;
  integrationParams?: Record<string, any>;
}

export interface WorkflowCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in' | 'exists' | 'not_exists' | 'regex';
  value: any;
  dataType?: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
}

export interface WorkflowTrigger {
  id: string;
  type: 'manual' | 'schedule' | 'event' | 'webhook' | 'api';
  config: WorkflowTriggerConfig;
  enabled: boolean;
}

export interface WorkflowTriggerConfig {
  // Schedule trigger
  schedule?: RecurrenceConfig;

  // Event trigger
  eventType?: string;
  eventFilters?: Record<string, any>;

  // Webhook trigger
  webhookUrl?: string;
  webhookSecret?: string;

  // API trigger
  apiEndpoint?: string;
  apiMethod?: string;
  apiAuth?: Record<string, any>;
}

export interface WorkflowVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  defaultValue?: any;
  required?: boolean;
  description?: string;
}

export interface WorkflowSettings {
  maxConcurrentExecutions: number;
  executionTimeout: number; // in milliseconds
  errorHandling: 'stop' | 'continue' | 'retry';
  logging: boolean;
  notifications: WorkflowNotificationSettings;
}

export interface WorkflowNotificationSettings {
  onSuccess?: boolean;
  onFailure?: boolean;
  onTimeout?: boolean;
  recipients?: string[]; // User IDs or email addresses
  channels?: string[]; // Channel IDs for notifications
}

export interface ErrorHandling {
  strategy: 'stop' | 'continue' | 'retry' | 'fallback';
  maxRetries?: number;
  retryDelay?: number;
  fallbackStep?: string; // Step ID to execute on error
  notifyOnError?: boolean;
}

export interface WorkflowAnalytics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number; // in milliseconds
  lastExecutionStatus: 'success' | 'failure' | 'timeout' | 'cancelled';
  lastExecutionTime?: Date;
  errorRate: number; // Percentage
  performanceMetrics: Record<string, number>;
}

// Archival & Compliance types
export interface MessageArchive {
  id: string;
  messageId: string;
  channelId: string;
  authorId: string;
  content: string;
  timestamp: Date;
  archivedAt: Date;
  retentionPolicy: string;
  metadata: ArchiveMetadata;
}

export interface ArchiveMetadata {
  originalFormat: string;
  encryption: boolean;
  checksum: string;
  tags: string[];
  legalHold: boolean;
  exportable: boolean;
}

// Template and Response types
export interface MessageTemplate {
  id: string;
  name: string;
  description?: string;
  content: string;
  variables: TemplateVariable[];
  category: string;
  tags: string[];
  usage: number;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt?: Date;
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'user' | 'channel' | 'custom';
  required: boolean;
  defaultValue?: any;
  description?: string;
  validation?: VariableValidation;
}

export interface VariableValidation {
  pattern?: string; // Regex pattern
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  allowedValues?: any[];
}

// Smart Response Enhancement
export interface SmartResponseEngine {
  id: string;
  name: string;
  type: 'keyword' | 'ml' | 'rule_based' | 'hybrid';
  config: SmartResponseConfig;
  enabled: boolean;
  priority: number;
  analytics: ResponseEngineAnalytics;
}

export interface SmartResponseConfig {
  // Keyword-based config
  keywords?: string[];
  keywordMatching?: 'exact' | 'fuzzy' | 'semantic';

  // ML-based config
  modelType?: 'classification' | 'similarity' | 'generation';
  modelEndpoint?: string;
  confidenceThreshold?: number;

  // Rule-based config
  rules?: ResponseRule[];

  // Hybrid config
  engines?: string[]; // IDs of other engines to combine
  combinationStrategy?: 'weighted' | 'cascade' | 'voting';
}

export interface ResponseRule {
  id: string;
  conditions: ResponseCondition[];
  response: ResponseAction;
  priority: number;
  enabled: boolean;
}

export interface ResponseCondition {
  field: 'content' | 'author' | 'channel' | 'time' | 'sentiment' | 'intent' | 'entities';
  operator: 'equals' | 'contains' | 'matches' | 'greater_than' | 'less_than';
  value: any;
  weight?: number; // For weighted scoring
}

export interface ResponseAction {
  type: 'template' | 'generated' | 'redirect' | 'escalate';
  config: ResponseActionConfig;
}

export interface ResponseActionConfig {
  templateId?: string;
  generatedContent?: string;
  redirectTo?: string; // Channel ID or user ID
  escalateTo?: string[]; // User IDs to escalate to
  variables?: Record<string, any>;
  followUpActions?: ResponseAction[];
}

export interface ResponseEngineAnalytics {
  totalResponses: number;
  successfulResponses: number;
  averageConfidence: number;
  averageResponseTime: number;
  userSatisfactionScore: number;
  topTriggers: Array<{ trigger: string; count: number }>;
  lastUpdated: Date;
}

// Event Processing types
export interface EventProcessor {
  id: string;
  name: string;
  eventTypes: string[];
  filters: EventFilter[];
  actions: EventAction[];
  enabled: boolean;
  priority: number;
  rateLimiting?: EventRateLimiting;
  analytics: EventProcessorAnalytics;
}

export interface EventFilter {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'in' | 'not_in' | 'exists' | 'not_exists' | 'greater_than' | 'less_than';
  value: any;
}

export interface EventAction {
  type: 'trigger_bot' | 'send_notification' | 'create_task' | 'update_data' | 'call_webhook' | 'start_workflow';
  config: EventActionConfig;
  delay?: number;
  conditions?: EventFilter[];
}

export interface EventActionConfig {
  botId?: string;
  notificationType?: 'desktop' | 'email' | 'sms' | 'push';
  notificationContent?: string;
  taskData?: Record<string, any>;
  dataUpdates?: Record<string, any>;
  webhookUrl?: string;
  workflowId?: string;
  workflowParams?: Record<string, any>;
}

export interface EventRateLimiting {
  maxEventsPerMinute: number;
  maxEventsPerHour: number;
  cooldownPeriod: number; // in seconds
}

export interface EventProcessorAnalytics {
  totalEventsProcessed: number;
  successfulActions: number;
  failedActions: number;
  averageProcessingTime: number;
  lastProcessed?: Date;
  errorRate: number;
}

export interface RetentionPolicyRule {
  id: string;
  name: string;
  description?: string;
  scope: RetentionScope;
  duration: number; // in days
  action: 'archive' | 'delete' | 'anonymize';
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastApplied?: Date;
}

export interface RetentionScope {
  channels: string[];
  users: string[];
  messageTypes: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export interface LegalHold {
  id: string;
  name: string;
  description?: string;
  scope: LegalHoldScope;
  status: 'active' | 'released' | 'expired';
  createdBy: string;
  createdAt: Date;
  expiresAt?: Date;
  releasedAt?: Date;
  reason: string;
}

export interface LegalHoldScope {
  users: string[];
  channels: string[];
  keywords: string[];
  dateRange: {
    from: Date;
    to?: Date;
  };
}

export interface ExportRequest {
  id: string;
  name: string;
  format: 'json' | 'csv' | 'pdf' | 'html';
  scope: ExportScope;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  requestedBy: string;
  requestedAt: Date;
  completedAt?: Date;
  downloadUrl?: string;
  expiresAt?: Date;
  error?: string;
  options: ExportOptions;
}

export interface ExportScope {
  channels: string[];
  users: string[];
  dateRange: {
    from: Date;
    to: Date;
  };
  includeAttachments: boolean;
  includeDeleted: boolean;
}

export interface ExportOptions {
  anonymizeUsers: boolean;
  includeMetadata: boolean;
  compression: boolean;
  password?: string;
  watermark?: string;
}

export interface ComplianceReport {
  id: string;
  type: 'retention' | 'legal_hold' | 'export' | 'audit';
  period: {
    from: Date;
    to: Date;
  };
  status: 'generating' | 'completed' | 'failed';
  generatedBy: string;
  generatedAt: Date;
  data: ComplianceData;
  downloadUrl?: string;
}

export interface ComplianceData {
  totalMessages: number;
  archivedMessages: number;
  deletedMessages: number;
  legalHoldMessages: number;
  exportRequests: number;
  violations: ComplianceViolation[];
}

export interface ComplianceViolation {
  id: string;
  type: 'retention_exceeded' | 'unauthorized_access' | 'export_failure';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detectedAt: Date;
  resolvedAt?: Date;
  affectedItems: string[];
}

// External Integration types
export interface ExternalIntegration {
  id: string;
  name: string;
  type: 'slack' | 'teams' | 'whatsapp' | 'email' | 'webhook' | 'api' | 'erp';
  status: 'active' | 'inactive' | 'error' | 'pending';
  config: IntegrationConfig;
  credentials: IntegrationCredentials;
  mapping: IntegrationMapping;
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastSync?: Date;
  syncStats: SyncStats;
}

export interface IntegrationConfig {
  syncDirection: 'inbound' | 'outbound' | 'bidirectional';
  syncFrequency: number; // in minutes
  autoSync: boolean;
  retryAttempts: number;
  timeout: number; // in seconds
  batchSize: number;
  filters: IntegrationFilter[];
}

export interface IntegrationCredentials {
  type: 'oauth' | 'api_key' | 'basic_auth' | 'certificate';
  data: Record<string, string>; // Encrypted storage
  expiresAt?: Date;
  refreshToken?: string;
}

export interface IntegrationMapping {
  userMapping: Record<string, string>; // External ID -> Internal ID
  channelMapping: Record<string, string>;
  messageMapping: Record<string, string>;
  customFields: Record<string, string>;
}

export interface IntegrationFilter {
  field: string;
  operator: 'equals' | 'contains' | 'starts_with' | 'regex';
  value: string;
  exclude: boolean;
}

export interface SyncStats {
  totalSynced: number;
  successfulSyncs: number;
  failedSyncs: number;
  lastError?: string;
  averageSyncTime: number;
}

// ERP Integration types
export interface ERPIntegration {
  id: string;
  module: string; // CRM, Sales, Inventory, etc.
  recordType: string;
  recordId: string;
  channelId: string;
  linkType: 'reference' | 'notification' | 'sync';
  config: ERPLinkConfig;
  createdAt: Date;
  lastUpdated?: Date;
}

export interface ERPLinkConfig {
  autoNotify: boolean;
  syncFields: string[];
  notificationEvents: string[];
  accessPermissions: string[];
}

// Webhook types
export interface WebhookEndpoint {
  id: string;
  name: string;
  url: string;
  events: WebhookEvent[];
  headers: Record<string, string>;
  secret?: string;
  enabled: boolean;
  retryPolicy: WebhookRetryPolicy;
  createdBy: string;
  createdAt: Date;
  lastTriggered?: Date;
  stats: WebhookStats;
}

export interface WebhookEvent {
  type: string;
  filters?: Record<string, any>;
}

export interface WebhookRetryPolicy {
  maxRetries: number;
  backoffMultiplier: number;
  maxBackoffSeconds: number;
}

export interface WebhookStats {
  totalDeliveries: number;
  successfulDeliveries: number;
  failedDeliveries: number;
  averageResponseTime: number;
  lastError?: string;
}

export interface WebhookDelivery {
  id: string;
  webhookId: string;
  event: string;
  payload: Record<string, any>;
  status: 'pending' | 'delivered' | 'failed' | 'retrying';
  attempts: number;
  createdAt: Date;
  deliveredAt?: Date;
  responseCode?: number;
  responseBody?: string;
  error?: string;
}

// API types for developers
export interface APIKey {
  id: string;
  name: string;
  key: string; // Hashed
  permissions: APIPermission[];
  rateLimit: RateLimitConfig;
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastUsed?: Date;
  expiresAt?: Date;
  usage: APIUsageStats;
}

export interface APIPermission {
  resource: string;
  actions: string[];
  scope?: Record<string, any>;
}

export interface APIUsageStats {
  totalRequests: number;
  requestsToday: number;
  requestsThisMonth: number;
  averageResponseTime: number;
  errorRate: number;
  lastRequest?: Date;
}

// File upload types
export interface FileUploadProgress {
  fileId: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'failed';
  error?: string;
}

// Voice/Video call types
export interface Call {
  id: string;
  type: 'voice' | 'video';
  channelId?: string;
  participantIds: string[];
  startedBy: string;
  startedAt: Date;
  endedAt?: Date;
  status: 'ringing' | 'active' | 'ended';
  recordingUrl?: string;
}

export interface CallParticipant {
  userId: string;
  joinedAt: Date;
  leftAt?: Date;
  isMuted: boolean;
  isVideoEnabled: boolean;
  isScreenSharing: boolean;
}

// Export all types
export type * from './index';

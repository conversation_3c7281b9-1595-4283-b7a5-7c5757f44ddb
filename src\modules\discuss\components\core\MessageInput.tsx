import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { RichTextEditor, EmojiPicker, FileUpload } from '../ui';

export interface MessageInputProps {
  placeholder?: string;
  channelId?: string;
  threadId?: string;
  replyToMessageId?: string;
  onSendMessage: (content: string, attachments?: File[]) => void;
  onTyping?: (isTyping: boolean) => void;
  onFileUpload?: (files: File[]) => void;
  disabled?: boolean;
  maxLength?: number;
  className?: string;
  'data-testid'?: string;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  placeholder = 'Type a message...',
  channelId,
  threadId,
  replyToMessageId,
  onSendMessage,
  onTyping,
  onFileUpload,
  disabled = false,
  maxLength = 4000,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Handle typing indicator
  useEffect(() => {
    if (message.trim() && !isTyping) {
      setIsTyping(true);
      onTyping?.(true);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        onTyping?.(false);
      }
    }, 2000);

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [message, isTyping, onTyping]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() && attachments.length === 0) return;
    if (disabled) return;

    onSendMessage(message.trim(), attachments);
    setMessage('');
    setAttachments([]);
    
    // Stop typing indicator
    if (isTyping) {
      setIsTyping(false);
      onTyping?.(false);
    }
  };



  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      setAttachments(prev => [...prev, ...files]);
      onFileUpload?.(files);
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const insertEmoji = (emoji: string) => {
    setMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };



  return (
    <div
      className={`border-t ${className}`}
      style={{
        backgroundColor: colors.surface,
        borderTopColor: colors.border,
      }}
      data-testid={testId}
    >
      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="p-4 border-b" style={{ borderBottomColor: colors.border }}>
          <div className="flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg"
                style={{
                  backgroundColor: colors.backgroundSecondary,
                  borderColor: colors.border,
                }}
              >
                <span className="text-sm">
                  {file.type.startsWith('image/') ? '🖼️' :
                   file.type.startsWith('video/') ? '🎥' :
                   file.type.startsWith('audio/') ? '🎵' : '📄'}
                </span>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate" style={{ color: colors.text }}>
                    {file.name}
                  </p>
                  <p className="text-xs" style={{ color: colors.textSecondary }}>
                    {formatFileSize(file.size)}
                  </p>
                </div>
                <button
                  onClick={() => removeAttachment(index)}
                  className="text-red-500 hover:text-red-700 text-sm"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Main Input Area */}
      <div className="p-4">
        <form onSubmit={handleSubmit} className="space-y-3">
          {/* Rich Text Editor */}
          <RichTextEditor
            value={message}
            onChange={setMessage}
            placeholder={placeholder}
            disabled={disabled}
            maxLength={maxLength}
            onSubmit={handleSubmit}
            onFileUpload={(files) => {
              const fileArray = Array.from(files);
              setAttachments(prev => [...prev, ...fileArray]);
              onFileUpload?.(fileArray);
            }}
          />

          {/* Toolbar */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {/* File Upload */}
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled}
                className="flex items-center space-x-1 text-sm hover:underline disabled:opacity-50"
                style={{ color: colors.textSecondary }}
              >
                <span>📎</span>
                <span>Attach</span>
              </button>

              {/* Emoji Picker */}
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  disabled={disabled}
                  className="flex items-center space-x-1 text-sm hover:underline disabled:opacity-50"
                  style={{ color: colors.textSecondary }}
                >
                  <span>😊</span>
                  <span>Emoji</span>
                </button>

                <EmojiPicker
                  isOpen={showEmojiPicker}
                  onEmojiSelect={insertEmoji}
                  onClose={() => setShowEmojiPicker(false)}
                  className="bottom-full left-0 mb-2"
                />
              </div>

              {/* Format Options */}
              <button
                type="button"
                disabled={disabled}
                className="flex items-center space-x-1 text-sm hover:underline disabled:opacity-50"
                style={{ color: colors.textSecondary }}
              >
                <span>📝</span>
                <span>Format</span>
              </button>
            </div>

            {/* Character Count & Send Hint */}
            <div className="flex items-center space-x-4">
              {message.length > maxLength * 0.8 && (
                <span
                  className={`text-xs ${message.length >= maxLength ? 'text-red-500' : ''}`}
                  style={{ color: message.length >= maxLength ? 'red' : colors.textSecondary }}
                >
                  {message.length}/{maxLength}
                </span>
              )}
              <span className="text-xs" style={{ color: colors.textSecondary }}>
                Press Enter to send
              </span>
            </div>
          </div>
        </form>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>
    </div>
  );
};

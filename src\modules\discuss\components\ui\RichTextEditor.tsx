import React, { useState, useRef, useCallback } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';

export interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  maxLength?: number;
  onSubmit?: () => void;
  onFileUpload?: (files: FileList) => void;
  className?: string;
  'data-testid'?: string;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Type a message...',
  disabled = false,
  maxLength = 2000,
  onSubmit,
  onFileUpload,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [isFocused, setIsFocused] = useState(false);
  const [showFormatting, setShowFormatting] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSubmit?.();
    }
    
    // Handle formatting shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          insertFormatting('**', '**');
          break;
        case 'i':
          e.preventDefault();
          insertFormatting('*', '*');
          break;
        case 'u':
          e.preventDefault();
          insertFormatting('__', '__');
          break;
      }
    }
  }, [onSubmit]);

  const insertFormatting = useCallback((before: string, after: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const newValue = value.substring(0, start) + before + selectedText + after + value.substring(end);
    
    onChange(newValue);
    
    // Set cursor position after formatting
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, end + before.length);
    }, 0);
  }, [value, onChange]);

  const handleFileSelect = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      onFileUpload?.(files);
    }
    // Reset input
    e.target.value = '';
  }, [onFileUpload]);

  const formatButtons = [
    { icon: 'B', title: 'Bold (Ctrl+B)', action: () => insertFormatting('**', '**') },
    { icon: 'I', title: 'Italic (Ctrl+I)', action: () => insertFormatting('*', '*') },
    { icon: 'U', title: 'Underline (Ctrl+U)', action: () => insertFormatting('__', '__') },
    { icon: '`', title: 'Code', action: () => insertFormatting('`', '`') },
    { icon: '```', title: 'Code Block', action: () => insertFormatting('\n```\n', '\n```\n') },
  ];

  return (
    <div
      className={`relative border rounded-lg transition-colors ${className}`}
      style={{
        borderColor: isFocused ? colors.primary : colors.border,
        backgroundColor: colors.background,
      }}
      data-testid={testId}
    >
      {/* Formatting Toolbar */}
      {(isFocused || showFormatting) && (
        <div
          className="flex items-center justify-between px-3 py-2 border-b"
          style={{ borderColor: colors.border }}
        >
          <div className="flex items-center space-x-1">
            {formatButtons.map((button) => (
              <button
                key={button.icon}
                onClick={button.action}
                disabled={disabled}
                className="px-2 py-1 text-xs font-mono rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                style={{ color: colors.text }}
                title={button.title}
              >
                {button.icon}
              </button>
            ))}
          </div>
          
          <div className="flex items-center space-x-2">
            {onFileUpload && (
              <button
                onClick={handleFileSelect}
                disabled={disabled}
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                style={{ color: colors.text }}
                title="Attach file"
              >
                📎
              </button>
            )}
            <span
              className="text-xs"
              style={{ 
                color: value.length > maxLength * 0.9 ? colors.error : colors.textSecondary 
              }}
            >
              {value.length}/{maxLength}
            </span>
          </div>
        </div>
      )}

      {/* Text Area */}
      <textarea
        ref={textareaRef}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        disabled={disabled}
        maxLength={maxLength}
        className="w-full px-3 py-2 bg-transparent border-none outline-none resize-none min-h-[80px] max-h-[200px]"
        style={{ color: colors.text }}
      />

      {/* File Input */}
      {onFileUpload && (
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileChange}
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
        />
      )}

      {/* Formatting Help */}
      {isFocused && (
        <div
          className="px-3 py-2 text-xs border-t"
          style={{ 
            borderColor: colors.border,
            color: colors.textSecondary,
            backgroundColor: colors.backgroundSecondary,
          }}
        >
          <span>**bold** *italic* `code` ```code block``` Enter to send, Shift+Enter for new line</span>
        </div>
      )}
    </div>
  );
};

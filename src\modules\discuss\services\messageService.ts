// Message service for handling message-related API calls
import type { 
  Message, 
  ApiResponse, 
  PaginatedResponse,
  SearchQuery,
  SearchResult 
} from '../types';

const API_BASE = '/api/discuss';

export interface SendMessageRequest {
  content: string;
  channelId?: string;
  threadId?: string;
  parentMessageId?: string;
  mentions?: string[];
  attachments?: File[];
}

export interface UpdateMessageRequest {
  content: string;
}

export const messageService = {
  // Get messages for a channel
  async getChannelMessages(
    channelId: string, 
    page: number = 1, 
    pageSize: number = 50
  ): Promise<PaginatedResponse<Message>> {
    const response = await fetch(
      `${API_BASE}/channels/${channelId}/messages?page=${page}&pageSize=${pageSize}`
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch messages');
    }
    
    return response.json();
  },

  // Get direct messages between users
  async getDirectMessages(
    userId: string,
    page: number = 1,
    pageSize: number = 50
  ): Promise<PaginatedResponse<Message>> {
    const response = await fetch(
      `${API_BASE}/direct-messages?userId=${userId}&page=${page}&pageSize=${pageSize}`
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch direct messages');
    }
    
    return response.json();
  },

  // Send a new message
  async sendMessage(request: SendMessageRequest): Promise<ApiResponse<Message>> {
    const formData = new FormData();
    formData.append('content', request.content);
    
    if (request.channelId) {
      formData.append('channelId', request.channelId);
    }
    
    if (request.threadId) {
      formData.append('threadId', request.threadId);
    }
    
    if (request.parentMessageId) {
      formData.append('parentMessageId', request.parentMessageId);
    }
    
    if (request.mentions) {
      formData.append('mentions', JSON.stringify(request.mentions));
    }
    
    if (request.attachments) {
      request.attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file);
      });
    }

    const response = await fetch(`${API_BASE}/messages`, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error('Failed to send message');
    }
    
    return response.json();
  },

  // Update a message
  async updateMessage(
    messageId: string, 
    request: UpdateMessageRequest
  ): Promise<ApiResponse<Message>> {
    const response = await fetch(`${API_BASE}/messages/${messageId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update message');
    }
    
    return response.json();
  },

  // Delete a message
  async deleteMessage(messageId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/messages/${messageId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete message');
    }
    
    return response.json();
  },

  // Add reaction to a message
  async addReaction(
    messageId: string, 
    emoji: string, 
    userId: string
  ): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/messages/${messageId}/reactions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ emoji, userId }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to add reaction');
    }
    
    return response.json();
  },

  // Remove reaction from a message
  async removeReaction(
    messageId: string, 
    emoji: string, 
    userId: string
  ): Promise<ApiResponse<any>> {
    const response = await fetch(
      `${API_BASE}/messages/${messageId}/reactions/${emoji}?userId=${userId}`, 
      {
        method: 'DELETE',
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to remove reaction');
    }
    
    return response.json();
  },

  // Search messages
  async searchMessages(query: SearchQuery): Promise<ApiResponse<SearchResult[]>> {
    const params = new URLSearchParams();
    params.append('q', query.query);
    
    if (query.channelId) {
      params.append('channelId', query.channelId);
    }
    
    if (query.userId) {
      params.append('userId', query.userId);
    }
    
    if (query.dateFrom) {
      params.append('dateFrom', query.dateFrom.toISOString());
    }
    
    if (query.dateTo) {
      params.append('dateTo', query.dateTo.toISOString());
    }
    
    if (query.messageType) {
      params.append('messageType', query.messageType);
    }

    const response = await fetch(`${API_BASE}/search?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error('Failed to search messages');
    }
    
    return response.json();
  },

  // Pin a message
  async pinMessage(messageId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/messages/${messageId}/pin`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to pin message');
    }
    
    return response.json();
  },

  // Unpin a message
  async unpinMessage(messageId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/messages/${messageId}/pin`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to unpin message');
    }
    
    return response.json();
  },

  // Mark messages as read
  async markAsRead(messageIds: string[]): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/messages/read`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messageIds }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to mark messages as read');
    }
    
    return response.json();
  },

  // Get message thread
  async getMessageThread(messageId: string): Promise<ApiResponse<Message[]>> {
    const response = await fetch(`${API_BASE}/messages/${messageId}/thread`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch message thread');
    }
    
    return response.json();
  },
};

import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

/**
 * @deprecated Use Dropdown from components/global instead
 * This component will be removed in a future version.
 *
 * Migration:
 * import { Dropdown } from '../../global';
 *
 * The new Dropdown component provides better composition, accessibility,
 * and supports sections, better theming, and more flexible layouts.
 */

export interface DropdownItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  isDivider?: boolean;
  shortcut?: string;
  description?: string;
}

export interface DropdownProps {
  trigger: React.ReactNode;
  items: DropdownItem[];
  align?: 'left' | 'right';
  className?: string;
  dropdownClassName?: string;
  disabled?: boolean;
  'data-testid'?: string;
}

const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  align = 'right',
  className = '',
  dropdownClassName = '',
  disabled = false,
  'data-testid': testId,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { colors } = useThemeStore();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
        triggerRef.current?.focus();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen]);

  const handleTriggerClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleItemClick = (item: DropdownItem) => {
    if (!item.disabled && !item.isDivider && item.onClick) {
      item.onClick();
      setIsOpen(false);
    }
  };

  const alignmentClasses = {
    left: 'left-0',
    right: 'right-0',
  };

  return (
    <div className={cn('relative', className)} data-testid={testId}>
      {/* Trigger */}
      <button
        ref={triggerRef}
        onClick={handleTriggerClick}
        disabled={disabled}
        className="focus:outline-none"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {trigger}
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className={cn(
            'absolute top-full mt-2 min-w-[200px] rounded-lg border shadow-lg z-50',
            alignmentClasses[align],
            dropdownClassName
          )}
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
            boxShadow: `0 10px 25px -5px ${colors.shadow}40, 0 4px 6px -2px ${colors.shadow}20`,
          }}
          role="menu"
          aria-orientation="vertical"
        >
          <div className="py-1">
            {items.map((item, index) => {
              if (item.isDivider) {
                return (
                  <div
                    key={item.id || `divider-${index}`}
                    className="my-1 h-px"
                    style={{ backgroundColor: colors.border }}
                    role="separator"
                  />
                );
              }

              return (
                <button
                  key={item.id}
                  onClick={() => handleItemClick(item)}
                  disabled={item.disabled}
                  className={cn(
                    'w-full px-4 py-2 text-left flex items-center justify-between',
                    'hover:bg-opacity-10 transition-colors duration-150',
                    'disabled:opacity-50 disabled:cursor-not-allowed',
                    'focus:outline-none focus:bg-opacity-10'
                  )}
                  style={{
                    color: item.disabled ? colors.mutedForeground : colors.text,
                    backgroundColor: 'transparent',
                  }}
                  onMouseEnter={e => {
                    if (!item.disabled) {
                      e.currentTarget.style.backgroundColor = `${colors.hover}20`;
                    }
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                  onFocus={e => {
                    if (!item.disabled) {
                      e.currentTarget.style.backgroundColor = `${colors.hover}20`;
                    }
                  }}
                  onBlur={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                  role="menuitem"
                >
                  <div className="flex items-center space-x-3">
                    {item.icon && (
                      <div
                        className="flex-shrink-0"
                        style={{ color: colors.mutedForeground }}
                      >
                        {item.icon}
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="text-sm font-medium">{item.label}</div>
                      {item.description && (
                        <div
                          className="text-xs mt-0.5"
                          style={{ color: colors.mutedForeground }}
                        >
                          {item.description}
                        </div>
                      )}
                    </div>
                  </div>
                  {item.shortcut && (
                    <div
                      className="text-xs font-mono px-2 py-1 rounded"
                      style={{
                        backgroundColor: colors.muted,
                        color: colors.mutedForeground,
                      }}
                    >
                      {item.shortcut}
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown;
